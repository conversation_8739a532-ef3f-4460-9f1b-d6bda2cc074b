### Consolidated TODO/FIXME

**Totalt funn:** 124

### `src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/AbstractKafkaPublisherService.kt`

- [L72](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/AbstractKafkaPublisherService.kt#L72):     // **`TODO`**: Replace with KafkaRetryTemplate

### `src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/config/KafkaInputTopics.kt`

- [L12](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/config/KafkaInputTopics.kt#L12): ) : KafkaTopics // **`TODO`**: Get rid of this dependency.

### `src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/config/KafkaOutputTopics.kt`

- [L19](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/config/KafkaOutputTopics.kt#L19): ) : KafkaTopics // **`TODO`**: Get rid of this dependency.

### `src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt`

- [L65](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L65):                 DTOEventType.OPERATING_WITHOUT_SIGN_ON to "OPERATING_WITHOUT_SIGN_ON", // **`TODO`**: Change to "OPERATING_WITHOUT_SIGN_ON"?
- [L111](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L111):                 // **`TODO`**: Remove quayRef when all operational events are drained from journey-manager
- [L318](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L318):                 // **`TODO`**: Should this be stricter?
- [L320](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L320):                 // **`TODO`**: Should this be stricter?
- [L358](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L358):                 // **`TODO`**: Should this be stricter?
- [L360](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/kafka/entity/AbstractEntityV2OutputMapper.kt#L360):                 // **`TODO`**: Should this be stricter?

### `src/main/kotlin/no/ruter/tranop/app/common/dataflow/snowflake/SnowflakeClientProperties.kt`

- [L5](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/dataflow/snowflake/SnowflakeClientProperties.kt#L5): // **`TODO`**: Split this into client properties (user, url, private key, ...?) and target properties (schema name, table name, ...?)

### `src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordRepository.kt`

- [L50](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordRepository.kt#L50):     // **`TODO`**: Hack to avoid outboxRepo (an impl of BaseRecordRepository to go completely bananas ). Make the OutboxRepo not impl BaseRecordRepository.
- [L148](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordRepository.kt#L148):         trace: TraceInfo, // **`TODO`**: Record actual trace for who deleted the record...
- [L166](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordRepository.kt#L166):         // **`TODO`**: Make outboxes on delete

### `src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordTableMetadata.kt`

- [L25](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/db/record/base/BaseRecordTableMetadata.kt#L25):     // **`TODO`**: Support multiple operator ids on a record? Only meaningful for journeys...?

### `src/main/kotlin/no/ruter/tranop/app/common/insight/InsightService.kt`

- [L124](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/insight/InsightService.kt#L124):     // **`TODO`**: [BIG_ONE] Consolidate metrics for dated journeys with assignment with friends

### `src/main/kotlin/no/ruter/tranop/app/common/lifecycle/AbstractLifeCycleRoutine.kt`

- [L22](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/lifecycle/AbstractLifeCycleRoutine.kt#L22):     // **`TODO`**: Clean up app-config. Excludes like these are not needed in this context
- [L53](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/lifecycle/AbstractLifeCycleRoutine.kt#L53):             insightService.recordDataInsight(subject, insightType, result.toDouble())

### `src/main/kotlin/no/ruter/tranop/app/common/lifecycle/AbstractOutboxPublishingRoutine.kt`

- [L48](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/lifecycle/AbstractOutboxPublishingRoutine.kt#L48):     // **`TODO`**: Is this supposed to be configured per target type?

### `src/main/kotlin/no/ruter/tranop/app/common/RecordType.kt`

- [L132](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/common/RecordType.kt#L132):                         // **`TODO`**: Add Snowflake target for RecordType.DATED_JOURNEY

### `src/main/kotlin/no/ruter/tranop/app/event/journey/db/JourneyEventRecordMapper.kt`

- [L41](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/db/JourneyEventRecordMapper.kt#L41):         // **`TODO`**: The update logic is similar for all database kinds. Try to gather the logic.

### `src/main/kotlin/no/ruter/tranop/app/event/journey/db/JourneyEventRepository.kt`

- [L77](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/db/JourneyEventRepository.kt#L77):     // **`TODO`**: Rename this to correspond with actual deletion criteria.
- [L78](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/db/JourneyEventRepository.kt#L78):     // **`TODO`**: Find a more suitable logic than published-state to decide deletion (events are never published).

### `src/main/kotlin/no/ruter/tranop/app/event/journey/input/JourneyEventInputContext.kt`

- [L97](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/JourneyEventInputContext.kt#L97):     // **`TODO`**: Fix up, this is horrible!!!

### `src/main/kotlin/no/ruter/tranop/app/event/journey/input/JourneyEventInputService.kt`

- [L55](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/JourneyEventInputService.kt#L55):             // **`TODO`**: Make specific exception for this
- [L63](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/JourneyEventInputService.kt#L63):             // **`TODO`**: Order of handlers may(?) be important. Since we do not know for sure, we assume it is, for now.

### `src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/deviation/OmitJourneyEventHandler.kt`

- [L96](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/deviation/OmitJourneyEventHandler.kt#L96):     // **`TODO`**: Should we do minute-resolution matching here instead of exact, like we do for cancelled calls?

### `src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/ReplacementJourneyEventHandler.kt`

- [L29](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/ReplacementJourneyEventHandler.kt#L29):                                 this.entityDatedJourneyKeyV2Ref = it // **`TODO`** sufficient with only entityDatedJourneyKeyV2Ref?

### `src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/StandbyVehicleJourneyEventHandler.kt`

- [L52](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/StandbyVehicleJourneyEventHandler.kt#L52):             // **`TODO`**: Remove operator from list when deviation is recalled?
- [L53](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/StandbyVehicleJourneyEventHandler.kt#L53):             // **`TODO`**: If we remove operator, how do we ensure original operator still has access?
- [L57](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/input/process/mitigation/StandbyVehicleJourneyEventHandler.kt#L57):                     // **`TODO`**: Resolve proper operator name instead of using same hard-coded name for all operators.

### `src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventOutboxSchedule.kt`

- [L16](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventOutboxSchedule.kt#L16): // **`TODO`**: Convert to life-cycle routine?

### `src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventOutboxSnowflakeIngestingRoutine.kt`

- [L39](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventOutboxSnowflakeIngestingRoutine.kt#L39):     // **`TODO`**: Mapping is performed in the ingest client JourneyEventOutboxSnowflakeIngestingService. Make mappers and move the logic here

### `src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventSnowflakeIngestClient.kt`

- [L36](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/journey/output/JourneyEventSnowflakeIngestClient.kt#L36):     // **`TODO`**: Use generated classes, BIJourneyEvent needs to be deleted

### `src/main/kotlin/no/ruter/tranop/app/event/traffic/input/TrafficEventInputService.kt`

- [L381](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/event/traffic/input/TrafficEventInputService.kt#L381):             // **`TODO`**: Fill in the blanks

### `src/main/kotlin/no/ruter/tranop/app/outbox/db/OutboxRepository.kt`

- [L99](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/outbox/db/OutboxRepository.kt#L99):         pagination: AbstractQueryBuilder.Pagination? = null, // **`TODO`**: Remove pagination and use an Int as limit/batchSize

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/config/DatedJourneyToggles.kt`

- [L10](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/config/DatedJourneyToggles.kt#L10):     // **`TODO`**: `createAdHocDeadRuns` is an input config. Move to a better, more suitable home :)

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRecordMapper.kt`

- [L81](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRecordMapper.kt#L81):         val creator = insightService.appName // **`TODO`**: Use something more meaningful.

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRepository.kt`

- [L94](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRepository.kt#L94):         // **`TODO`**: Make more robust?
- [L95](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRepository.kt#L95):         // **`TODO`**: Group in transaction?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/db/InternalDatedJourney.kt`

- [L23](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/db/InternalDatedJourney.kt#L23):     // **`TODO`**: This should not be needed. Used for printing out status details atm.

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputContext.kt`

- [L16](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputContext.kt#L16): // **`TODO`**: Limit access to the constructor. This context should only be called by the DatedJourneyValidator

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputMapper.kt`

- [L286](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputMapper.kt#L286):             this.expectedArrival = null // **`TODO`**: input.plannedArrival?
- [L287](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputMapper.kt#L287):             this.expectedDeparture = null // **`TODO`**: input.plannedDeparture?
- [L325](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputMapper.kt#L325):             this.direction = input.direction // **`TODO`**: Should we use DTODatedJourneyDirectionCode here?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputService.kt`

- [L74](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/input/DatedJourneyInputService.kt#L74):             // **`TODO`**: Wrap these operations in a separate @Transactional?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaPublishRoutine.kt`

- [L28](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaPublishRoutine.kt#L28):  * **`TODO`**: Split publishing to different Kafka topics into separate routines?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaRepublishRoutine.kt`

- [L19](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaRepublishRoutine.kt#L19):  * **`TODO`**: Split re-publishing to different Kafka topics into separate routines?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaTombstoneRoutine.kt`

- [L20](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/DatedJourneyKafkaTombstoneRoutine.kt#L20):  * **`TODO`**: Split tombstone publishing to different Kafka topics into separate routines?

### `src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt`

- [L509](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L509):                 // **`TODO`**: Should this be stricter?
- [L520](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L520):                 // **`TODO`**: Should this be stricter?
- [L795](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L795):             ) // **`TODO`**: Should we downgrade this a warning and us a blank / empty fall-back text?
- [L1010](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L1010):         // **`TODO`**: Why on earth are creating an input message to create an output message?
- [L1013](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L1013):                 channel = type.channels.output, // **`TODO`**: Pick correct output channel for correct version of output entity.
- [L1019](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyEntityOutputMapper.kt#L1019):         // **`TODO`** verify result

### `src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishingService.kt`

- [L15](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishingService.kt#L15): // **`TODO`**: Gather the impls of all these Publishing services

### `src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishRoutine.kt`

- [L84](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishRoutine.kt#L84):         // **`TODO`**: Why are we creating an input context to create an output message?
- [L87](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishRoutine.kt#L87):                 channel = RecordType.LINK.channels.output, // **`TODO`**: Set proper output channel for proper entity version.
- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/link/output/entity/StopPointLinkEntityPublishRoutine.kt#L91):         // **`TODO`**: link.ref == null

### `src/main/kotlin/no/ruter/tranop/app/plan/link/output/internal/StopPointLinkDTOPublishingService.kt`

- [L15](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/link/output/internal/StopPointLinkDTOPublishingService.kt#L15): // **`TODO`**: Gather the impls of all these Publishing services

### `src/main/kotlin/no/ruter/tranop/app/plan/stop/output/entity/StopPointEntityPublishingService.kt`

- [L15](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/stop/output/entity/StopPointEntityPublishingService.kt#L15): // **`TODO`**: Gather the impls of all these Publishing services

### `src/main/kotlin/no/ruter/tranop/app/plan/stop/output/entity/StopPointEntityPublishRoutine.kt`

- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/stop/output/entity/StopPointEntityPublishRoutine.kt#L91):         // **`TODO`**: stopPoint.ref == null

### `src/main/kotlin/no/ruter/tranop/app/plan/stop/output/internal/StopPointDTOPublishingService.kt`

- [L15](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/plan/stop/output/internal/StopPointDTOPublishingService.kt#L15): // **`TODO`**: Gather the impls of all these Publishing services

### `src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/AbstractADTv4APIInputMapper.kt`

- [L64](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/AbstractADTv4APIInputMapper.kt#L64):             // **`TODO`**: Implement proper direction mapping. Do NOT assume APIJourneyDirectionCode == DTOJourneyDirectionCode!!

### `src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/ADTv4InputContext.kt`

- [L40](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/ADTv4InputContext.kt#L40):         get() = emptyMap() // **`TODO`**: Fix this.
- [L42](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/ADTv4InputContext.kt#L42):     override val ref: String? = null // **`TODO`**: Fix this.
- [L43](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/ADTv4InputContext.kt#L43):     override val refPath: String = "$" // **`TODO`**: Fix this.
- [L48](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/api/adt/v4/input/ADTv4InputContext.kt#L48):         get() = emptyMap() // **`TODO`**: Fix this.

### `src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt`

- [L74](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt#L74):         // **`TODO`**: Support filtering on duration.
- [L78](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt#L78):         } // **`TODO`**: Handle multiple matches.
- [L94](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt#L94):         // **`TODO`**: Fire out what rules to actually apply here...
- [L142](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt#L142):         // **`TODO`**: Support filtering on duration.
- [L178](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/JourneyImpactService.kt#L178):         // **`TODO`**: Support filtering on duration.

### `src/main/kotlin/no/ruter/tranop/app/variance/common/ServiceImpactValidator.kt`

- [L172](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/ServiceImpactValidator.kt#L172):         // **`TODO`**: Figure out what the actual rules / requirements are supposed to be here...

### `src/main/kotlin/no/ruter/tranop/app/variance/common/ServiceVarianceRequest.kt`

- [L30](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/common/ServiceVarianceRequest.kt#L30):         get() = emptyMap() // **`TODO`**: Implement this properly.

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/ADTv4DeviationAPIController.kt`

- [L111](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/ADTv4DeviationAPIController.kt#L111):                     // **`TODO`**: Check that client has access to read this deviation.

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/input/DeviationInputMapper.kt`

- [L110](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/input/DeviationInputMapper.kt#L110):         get() = "api service deviation" // **`TODO`**: Implement this.
- [L112](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/input/DeviationInputMapper.kt#L112):         get() = "dto service deviation" // **`TODO`**: Implement this.
- [L120](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/input/DeviationInputMapper.kt#L120):         // **`TODO`**: Implement this.

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/output/DeviationOutputMapper.kt`

- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/output/DeviationOutputMapper.kt#L91):         get() = "api service deviation" // **`TODO`**: Implement this.
- [L93](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/output/DeviationOutputMapper.kt#L93):         get() = "dto service deviation" // **`TODO`**: Implement this.
- [L101](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/api/output/DeviationOutputMapper.kt#L101):         // **`TODO`**: Implement this.

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/JourneyDelayService.kt`

- [L106](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/JourneyDelayService.kt#L106):                 this.entityTrafficCaseKeyV2Ref = null // **`TODO`**: Map this from deviation metadata?
- [L107](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/JourneyDelayService.kt#L107):                 this.entityTrafficEventKeyV1Ref = null // **`TODO`**: Map this from deviation metadata?
- [L108](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/JourneyDelayService.kt#L108):                 this.entityTrafficSituationKeyV2Ref = null // **`TODO`**: Map this from deviation metadata?
- [L117](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/JourneyDelayService.kt#L117):             // **`TODO`**: Avoid adding a new event if only deviation metadata has changed (i.e., delay value is the same).

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/entity/ServiceDeviationEntityOutputContext.kt`

- [L18](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/entity/ServiceDeviationEntityOutputContext.kt#L18):         get() = emptyMap() // **`TODO`** maybe implement this in assignment-api?

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/entity/ServiceDeviationEntityOutputMapper.kt`

- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/entity/ServiceDeviationEntityOutputMapper.kt#L91):         // **`TODO`**: Make use of this method instead of having to supply the InputMessage in the other signature

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/internal/ServiceDeviationDTOPublishingService.kt`

- [L30](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/internal/ServiceDeviationDTOPublishingService.kt#L30):         keyType = "ENTITY_DEVIATION_KEY_V2", // **`TODO`**: Update log keys
- [L41](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/internal/ServiceDeviationDTOPublishingService.kt#L41):                     // **`TODO`** should this function be responsible for this or it should be moved one lvl up

### `src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/ServiceDeviationSnowflakeIngestClient.kt`

- [L64](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/deviation/output/ServiceDeviationSnowflakeIngestClient.kt#L64):                 spec?.code?.value?.let { BIServiceDeviationCode.of(it) }, // **`TODO`**: Convert BI model type to string.

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/ADTv4MitigationService.kt`

- [L50](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/ADTv4MitigationService.kt#L50):     // **`TODO`**: Merge output mappers?

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/input/MitigationInputMapper.kt`

- [L100](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/input/MitigationInputMapper.kt#L100):         get() = "api service mitigation" // **`TODO`**: Implement this.
- [L102](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/input/MitigationInputMapper.kt#L102):         get() = "dto service mitigation" // **`TODO`**: Implement this.
- [L110](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/input/MitigationInputMapper.kt#L110):         // **`TODO`**: Implement this.

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/MitigationOutputMapper.kt`

- [L81](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/MitigationOutputMapper.kt#L81):         get() = "api service mitigation" // **`TODO`**: Implement this.
- [L83](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/MitigationOutputMapper.kt#L83):         get() = "dto service mitigation" // **`TODO`**: Implement this.
- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/MitigationOutputMapper.kt#L91):         // **`TODO`**: Implement this.

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/ReplacementServiceOutputMapper.kt`

- [L34](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/api/output/ReplacementServiceOutputMapper.kt#L34):                 serviceJourneyId = refs?.externalJourneyRef, // **`TODO`**: Is this mapping correct?

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt`

- [L51](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L51):     // **`TODO`**: Does standbyOperator have an operatorContract for replacement journeys? As of now, standbyOperator is applied to tram operator contract
- [L82](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L82):             replacementJourney.operators = listOf(defaultStandbyOperator.deepCopy()) // **`TODO`**: should capture from input?
- [L83](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L83):             replacementJourney.operatorContracts = listOf(defaultStandbyOperatorContract.deepCopy()) // **`TODO`**: capture from input?
- [L85](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L85):             // **`TODO`**: Support draft mode (do not commit changes)
- [L86](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L86):             // **`TODO`**: Support asynchronous update (do not commit changes on initial call, but do so in scheduled task later)
- [L107](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L107):             // **`TODO`**: Figure out proper handling of updates with calculation of included / excluded, etc.
- [L185](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L185):             targets = JourneyTargetDiff.EMPTY, // **`TODO`**: Return proper targets?
- [L190](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/JourneyReplacementService.kt#L190):         datedJourneyInputService.processInternal(journey.ref, journey) // **`TODO`** validate

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/output/ServiceMitigationSnowflakeIngestClient.kt`

- [L59](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/output/ServiceMitigationSnowflakeIngestClient.kt#L59):                     spec.code?.value?.let { BIServiceMitigationCode.of(it) }, // **`TODO`**: Change type to String in BI model.

### `src/main/kotlin/no/ruter/tranop/app/variance/mitigation/replacement/BusForTramReplacementService.kt`

- [L88](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/replacement/BusForTramReplacementService.kt#L88):                         this.ownerId = MapperUtils.OWNER_ID // **`TODO`**: Fix hard-coded owner id.
- [L212](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/no/ruter/tranop/app/variance/mitigation/replacement/BusForTramReplacementService.kt#L212):                         // **`TODO`**: edit existing call or initialize new call?

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/DatedJourneyDeleteLifeCycleTest.kt`

- [L14](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/DatedJourneyDeleteLifeCycleTest.kt#L14):     // **`TODO`**: refactor test

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/DatedJourneyOutboxLifeCycleTest.kt`

- [L65](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/DatedJourneyOutboxLifeCycleTest.kt#L65):         // **`TODO`**: Can not assert on kafka here.

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRepositoryTest.kt`

- [L280](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/db/DatedJourneyRepositoryTest.kt#L280):     // **`TODO`**: Move this. It's not testing repository class, but rather higher-level input service.

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyOutboxPublishKafkaEntityTest.kt`

- [L13](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/output/entity/DatedJourneyOutboxPublishKafkaEntityTest.kt#L13): // **`TODO`**: Simulate exception in routine to check error handling

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/output/entity/PublishDatedJourneyLifeCycleTest.kt`

- [L45](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/output/entity/PublishDatedJourneyLifeCycleTest.kt#L45):                 // **`TODO`**: Resolve these (we should control their value, which may not be same as they were in input).

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/output/internal/DatedJourneyOutboxPublishInternalTest.kt`

- [L13](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/output/internal/DatedJourneyOutboxPublishInternalTest.kt#L13): // **`TODO`**: Simulate exception in routine to check error handling

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/output/opensearch/DatedJourneyOutboxPublishOpenSearchTest.kt`

- [L13](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/output/opensearch/DatedJourneyOutboxPublishOpenSearchTest.kt#L13): // **`TODO`**: Simulate exception in routine to check error handling

### `src/test/kotlin/no/ruter/tranop/app/plan/journey/output/opensearch/OpenSearchDeleteTest.kt`

- [L9](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/journey/output/opensearch/OpenSearchDeleteTest.kt#L9):         // **`TODO`** implement me

### `src/test/kotlin/no/ruter/tranop/app/plan/link/output/OutboxPublishStopPointLinkTest.kt`

- [L14](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/link/output/OutboxPublishStopPointLinkTest.kt#L14): // **`TODO`**: Simulate exception in routine to check error handling

### `src/test/kotlin/no/ruter/tranop/app/plan/stop/output/OutboxPublishStopPointTest.kt`

- [L14](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/plan/stop/output/OutboxPublishStopPointTest.kt#L14): // **`TODO`**: Simulate exception in routine to check error handling

### `src/test/kotlin/no/ruter/tranop/app/variance/deviation/api/reason/ReasonCodeServiceTest.kt`

- [L22](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/variance/deviation/api/reason/ReasonCodeServiceTest.kt#L22):         object : TraceInfoContext { // **`TODO`**: Find a better way to mock this.

### `src/test/kotlin/no/ruter/tranop/app/variance/mitigation/CancellationMitigationTest.kt`

- [L22](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/variance/mitigation/CancellationMitigationTest.kt#L22): // **`TODO`**: Add test for updating partial cancellation
- [L68](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/variance/mitigation/CancellationMitigationTest.kt#L68):         // **`TODO`**: Assert comment has been stored and is obtainable somewhere...
- [L91](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/variance/mitigation/CancellationMitigationTest.kt#L91):         // **`TODO`**: Assert comment has been stored and is obtainable somewhere...

### `src/test/kotlin/no/ruter/tranop/app/variance/mitigation/StandbyVehicleMitigationTest.kt`

- [L72](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/no/ruter/tranop/app/variance/mitigation/StandbyVehicleMitigationTest.kt#L72):         // **`TODO`**: Assert comment has been stored and is obtainable somewhere...

