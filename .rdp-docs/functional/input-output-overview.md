---
title: Input & Output Overview
type: docs
---


## Simplified Input / Output Overview

```mermaid
flowchart LR
    subgraph plandata-topics["Plandata Kafka Topics"]
        plandata-dto-kafka-stop["Plandata<br>DTO Stop Point"]
        plandata-dto-kafka-link["Plandata<br>DTO Stop Point Link"]
        plandata-dto-kafka-journey["Plandata<br>DTO Dated Journey"]
    end

    journey-event["Assignment<br>DTO Journey Event"]
    traffic-event["Assignment<br>DTO Traffic Event"]

    api-client-deviation(("Deviation API<br>Clients"))
    api-client-mitigation(("Mitigation API<br>Clients"))

    assignment-manager("assignment-manager")
    traffic-event-mapper("dated-journey-<br>traffic-event-<br>entity-mapper")

    subgraph stop-outputs ["Stop Point Outputs"]
        entity-kafka-stop["<em>Kafka Entity Topic</em>"]
        assignment-dto-kafka-stop["<em>Kafka Internal Topic</em>"]
    end
    subgraph link-outputs ["Stop Point Link Outputs"]
        entity-kafka-link["<em>Kafka Entity Topic</em>"]
        assignment-dto-kafka-link["<em>Kafka Internal Topic</em>"]
    end
    subgraph dated-journey-outputs ["Dated Journey Outputs"]
        open-search[("OpenSearch")]
        entity-kafka-journey["<em>Kafka Entity Topic</em>"]
        assignment-dto-kafka-journey["<em>Kafka Internal Topic</em>"]
    end
    subgraph deviation-outputs ["Deviation Outputs"]
        deviation-snowflake[("Snowflake<br><em>ingest table</em>")]
        entity-kafka-deviation["<em>Kafka Entity Topic</em>"]
    end
    subgraph mitigation-outputs ["Mitigation Outputs"]
        mitigation-snowflake[("Snowflake<br><me>ingest table</em>")]
        entity-kafka-mitigation["<em>Kafka Entity Topic</em>"]
    end
    subgraph journey-event-outputs ["Dated(?) Journey Event Outputs"]
        journey-event-snowflake[("Snowflake<br><em>ingest table</em>")]
    end

    subgraph manager-magic ["assignment-journey-manager"]
        stop-input[["Stop Point<br>Consumer"]]
        link-input[["Stop Point Link<br>Consumer"]]
        journey-input[["Dated Journey<br>Consumer"]]

        deviation-api[["Deviation API<br>Controller"]]
        mitigation-api[["Mitigation API<br>Controller"]]

        journey-event-consumer[["Journey Event<br>Consumer"]]
        traffic-event-consumer[["Traffic Event<br>Consumer"]]

        internal-processing((<em>Internal<br>Processing</em>))

        stop-output[["Stop Point<br><em>Outbox</em>"]]
        link-output[["Stop Point<br>Link <em>Outbox</em>"]]
        journey-output[["Dated Journey<br><em>Outbox</em>"]]
        journey-event-output[["Dated Journey<br>Event <em>Outbox</em>"]]

        deviation-output[["Deviation<br><em>Outbox</em>"]]
        mitigation-output[["Mitigation<br><em>Outbox</em>"]]
    end
    traffic-event --> traffic-event-consumer
    traffic-event-consumer --> internal-processing
    traffic-event-mapper --> traffic-event

    assignment-manager --> journey-event
    internal-processing .-> journey-output
    internal-processing .-> journey-event-output
    journey-event --> journey-event-consumer
    journey-event-consumer --> internal-processing
    journey-event-output --> journey-event-snowflake

    api-client-deviation --> deviation-api
    deviation-api -..-> deviation-output
    deviation-api --> internal-processing

    api-client-mitigation --> mitigation-api
    mitigation-api -..-> mitigation-output
    mitigation-api --> internal-processing

    plandata-dto-kafka-stop --> stop-input
    plandata-dto-kafka-link --> link-input
    plandata-dto-kafka-journey --> journey-input

    stop-input ---> stop-output
    stop-output -.-> entity-kafka-stop
    stop-output -.-> assignment-dto-kafka-stop

    link-input ---> link-output
    link-output -.-> entity-kafka-link
    link-output -.-> assignment-dto-kafka-link

    journey-input --> journey-output
    journey-output -.-> entity-kafka-journey
    journey-output -.-> assignment-dto-kafka-journey
    journey-output -.-> open-search

    deviation-output -.-> entity-kafka-deviation
    deviation-output -.-> deviation-snowflake
    mitigation-output -.-> mitigation-snowflake
    mitigation-output -.-> entity-kafka-mitigation
```

| Record Type         |     Kafka / JSON     |           Kafka / Avro           |    OpenSearch    |       Snowflake       |     REST API / JSON     |
|:--------------------|:--------------------:|:--------------------------------:|:----------------:|:---------------------:|:-----------------------:|
| **Inputs**          |                      |                                  |                  |                       |                         |
| Stop Point          |     Plandata DTO     |                -                 |        -         |           -           |            -            |
| Stop Point Link     |     Plandata DTO     |                -                 |        -         |           -           |            -            |
| Dated Journey       |     Plandata DTO     |                                  |        -         |           -           |            -            |
| Traffic Event       |  _Assignment DTO_ ¹  |                                  |        -         |           -           |            -            |
| Journey Event       |    Assignment DTO    |                                  |        -         |           -           |            -            |
| Service Deviation   |          -           |                -                 |        -         |           -           |  ADT v4 Deviation API   |
| Service Mitigation  |          -           |                -                 |        -         |           -           |  ADT v4 Mitigation API  |
| **Outputs**         |                      |                                  |                  |                       |            -            |
| Stop Point          |          -           |   _DatedJourneyStopPointKeyV2_   |        -         |       _TODO_ ²        |            -            |
| Stop Point Link     |          -           | _DatedJourneyStopPointLinkKeyV2_ |        -         |       _TODO_ ²        |            -            |
| Dated Journey       |    Assignment DTO    |       _DatedJourneyKeyV2_        |  Assignment DTO  |       _TODO_ ²        |            -            |
| Service Deviation   |          -           |              _WiP?_              |        -         |        _WiP?_         | ADT v4 Deviation API ¹  |
| Service Mitigation  |          -           |              _WiP?_              |        -         |        _WiP?_         | ADT v4 Mitigation API ¹ |
| Dated Journey Event |          -           |                -                 |        -         |  Assignment DTO / BI  |            -            |

¹ Will be mapped to Deviation / Mitigation API calls by _dated-journey-traffic-event-entity-mapper_ in the (near) future.<br/>
² _Returned via API on client request._<br/>
³ _Currently transferred to Snowflake via Kafka / Avro entity output. Should be replaced with direct Kafka streaming ingestion in the future.<br/>
