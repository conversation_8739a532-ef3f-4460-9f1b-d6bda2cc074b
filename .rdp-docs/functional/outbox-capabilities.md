# Outbox Capabilities

## Outbox Pattern Progress

### TO OUTBOX

|               | Kafka Internal | Kafka Entity | SnowFlake | OpenSearch |
|:--------------|:--------------:|:------------:|:---------:|:----------:|
| Deviation     |      n/a       |   V1 sync    |   sync    |    n/a     |
| Mitigation    |      n/a       |   V1 sync    |   sync    |    n/a     |
| DatedJourney  |      sync      |   V2 sync    |   sync    |    sync    |
| StopPoint     |      sync      |   V2 sync    |    n/a    |    n/a     |
| StopPointLink |      sync      |   V2 sync    |    n/a    |    n/a     |
| JourneyEvents |      n/a       |     n/a      |   sync    |    n/a     |

* `sync`: The record is written to the outbox as a part of processing requests

### FROM OUTBOX

|               | Kafka Internal | Kafka Entity | SnowFlake | OpenSearch |
|:--------------|:--------------:|:------------:|:---------:|:----------:|
| Deviation     |      n/a       |              |           |    n/a     |
| Mitigation    |      n/a       |              |           |    n/a     |
| DatedJourney  |      done      |     done     |           |    done    |
| StopPoint     |      done      |     done     |    n/a    |    n/a     |
| StopPointLink |      done      |     done     |    n/a    |    n/a     |
| JourneyEvents |      n/a       |     n/a      |   done    |    n/a     |
