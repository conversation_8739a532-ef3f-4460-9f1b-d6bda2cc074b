package no.ruter.tranop.assignment.common.db.record.base;

import no.ruter.tranop.assignment.common.db.RecordHeader;

import java.time.OffsetDateTime;

public interface BaseRecord extends RecordHeader {
    void setId(Integer value);

    void setRef(String value);

    default Integer getRevision() { return null; };
    default void setRevision(Integer value) { };

    default String getOperatorId() { return null; }
    default void setOperatorId(String value) { }

    default String getAuthorityId() { return null; }
    default void setAuthorityId(String value) { }

    OffsetDateTime getCreatedAt();
    void setCreatedAt(OffsetDateTime value);

    String getCreatedBy();
    void setCreatedBy(String value);

    default OffsetDateTime getModifiedAt() { return getCreatedAt(); }
    default void setModifiedAt(OffsetDateTime value) { }

    default String getModifiedBy() { return getCreatedBy(); }
    default void setModifiedBy(String value) { }

    default OffsetDateTime getDeletedAt() { return null; }
    default void setDeletedAt(OffsetDateTime value) { }

    default Integer getDeletedRevision() { return null; }
    default void setDeletedRevision(Integer value) { }
}
