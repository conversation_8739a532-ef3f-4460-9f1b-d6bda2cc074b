import com.github.benmanes.gradle.versions.updates.DependencyUpdatesTask
import no.ruter.tranop.gradle.DependencyUtils
import no.ruter.tranop.gradle.GradleExtensions.Companion.gitlabGroup
import no.ruter.tranop.gradle.codegen.JooqUtils.Companion.configureCodegen
import no.ruter.tranop.gradle.todos.ScanForTodosTask
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    jacoco

    // Kotlin
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.jmailen.kotlinter) // ensure "pretty" source code... :)

    // Spring Boot
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)

    // Utilities
    alias(libs.plugins.versions) // for checking  out-of-date dependencies
    alias(libs.plugins.test.logger) // improved build output for tests

    // Code generators
    alias(libs.plugins.jooq.codegen)
}

group = "no.ruter.tranop"
val javaLangVersion = JavaLanguageVersion.of(libs.versions.java.get())

java {
    toolchain {
        languageVersion = javaLangVersion
    }
}

kotlin {
    jvmToolchain(javaLangVersion.asInt())

    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
    }
}

jacoco {
    toolVersion = "0.8.11"
}
tasks.jacocoTestReport {
    reports {
        csv.required.set(true)
        xml.required.set(true)
        html.required.set(true)
    }
}
tasks.withType<Test> {
    useJUnitPlatform()
    testlogger {
        showPassed = true
        showExceptions = true
        showFullStackTraces = true
        showFailedStandardStreams = true
    }
}

repositories {
    mavenLocal()
    gitlabGroup(project, "9909697", "RDP")
    gitlabGroup(project, "9351422", "Sanntid")
    gitlabGroup(project, "57160432", "Transportoppdrag")
    gitlabGroup(project, "9068256", "Plandata")
    maven {
        url = uri("https://repo.spring.io/milestone")
        content {
            includeGroupByRegex("org\\.spring.*")
        }
    }
    mavenCentral()
    maven {
        url = uri("https://packages.confluent.io/maven/")
        content {
            includeGroupByRegex("io.confluent")
        }
    }
}

springBoot {
    buildInfo()
}

tasks.getByName<org.springframework.boot.gradle.tasks.run.BootRun>("bootRun") {
    args("--spring.profiles.active=local")
}

// Make JOOQ version known to JOOQ code generator plugin.
val jooqVersion =
    libs.versions.jooq
        .asProvider()
        .get()
ext["jooq.version"] = jooqVersion

// https://github.com/etiennestuder/gradle-jooq-plugin
jooq {
    configureCodegen(project, jooqVersion = jooqVersion)
}

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
    testRuntimeOnly {
        exclude("org.springframework.boot", "spring-boot-starter-logging")
    }
}

dependencies {
    // Unclassified Spring Boot
    implementation(libs.spring.boot.starter)
    implementation(libs.spring.boot.starter.web)
    implementation(libs.spring.boot.starter.security)
    testImplementation(libs.spring.boot.starter.test)
    annotationProcessor(libs.spring.boot.configuration.processor)
    testAnnotationProcessor(libs.spring.boot.configuration.processor)

    implementation(libs.javax.annotation.api)

    // Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib.jdk8)

    // JOOQ code generator.
    jooqGenerator(libs.jooq.codegen)
    jooqGenerator(libs.jooq.meta.extensions)
    jooqGenerator(libs.jakarta.xml.bind.api)
    jooqGenerator(libs.h2)

    // Database
    implementation(libs.postgresql)
    implementation(libs.spring.boot.starter.jooq)
    implementation(libs.flyway.core)
    implementation(libs.flyway.database.postgresql)
    implementation(libs.bundles.shedlock)
    testImplementation(enforcedPlatform(libs.embedded.postgres.binaries.bom))
    testImplementation(libs.bundles.embedded.postgres)

    // Snowflake
    implementation(libs.snowflake.ingest)
    // jaxb is added to support faster S3 uploads in Snowflake
    // WARN: JAXB is unavailable. Will fallback to SDK implementation which may be less performant.If you are using Java 9+, you will need to include javax.xml.bind:jaxb-api as a dependency.
    implementation(libs.jaxb.api)

    // Kafka
    implementation(libs.spring.kafka)
    implementation(libs.kafka.clients)
    implementation(libs.rdp.kafka.streams.common)

    // OpenSearch
    implementation(libs.bundles.aws)
    implementation(libs.opensearch.client)

    // Insight
    implementation(libs.rdp.insight)
    implementation(libs.logstash.logback.encoder)
    implementation(libs.spring.boot.starter.logging)
    implementation(libs.jcl.over.slf4j)
    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.micrometer.registry.prometheus)

    // Models
    implementation(libs.bundles.json)
    implementation(libs.bundles.tranop.models)
    implementation(libs.bundles.avro)

    // Swagger
    implementation(libs.swagger.models)
    implementation(libs.swagger.ui)

    // RestClient
    implementation(libs.rest.client)
    implementation(libs.caffeine)
    implementation(libs.spring.context.support)

    // Rest assured
    testImplementation(libs.bundles.rest.assured)

    testImplementation(libs.bundles.kotlin.test)
    testImplementation(libs.mockito.kotlin)

    testImplementation(libs.opensearch)
    testImplementation(libs.opensearch.geo)
    testImplementation(libs.opensearch.netty)
    // we use REST HTTP client in tests and AWS client in prod
    testImplementation(libs.opensearch.rest.client)
    testImplementation(libs.awaitility)

    testImplementation(libs.okhttp3.mockwebserver)

    implementation(libs.kotlin.coroutines)

    jooqGenerator(project(":codegen"))
    implementation(project(":codegen"))
}

val codegen =
    tasks.register("codegen") {
        dependsOn(tasks.named("generateJooq"))
    }
tasks.withType<KotlinCompile> {
    dependsOn(codegen)
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<DependencyUpdatesTask> {
    rejectVersionIf {
        DependencyUtils.isNonStable(candidate.version)
    }
}

val scanTodos =
    tasks.register<ScanForTodosTask>("scanTodos") {
        repoWebBase.set("https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager")
        repoBranch.set("main")
        outputFile.set(layout.projectDirectory.file(ScanForTodosTask.TARGET_FILE))

        dependsOn(codegen)
    }

tasks.named("check") {
    dependsOn(scanTodos)
}
