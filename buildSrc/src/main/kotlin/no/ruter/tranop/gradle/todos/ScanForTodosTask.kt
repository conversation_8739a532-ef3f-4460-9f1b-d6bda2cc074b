package no.ruter.tranop.gradle.todos

// buildSrc/src/main/kotlin/ScanForTodosTask.kt
import org.gradle.api.DefaultTask
import org.gradle.api.file.ConfigurableFileCollection
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.CacheableTask
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.InputFiles
import org.gradle.api.tasks.Internal
import org.gradle.api.tasks.Optional
import org.gradle.api.tasks.OutputFile
import org.gradle.api.tasks.PathSensitive
import org.gradle.api.tasks.PathSensitivity
import org.gradle.api.tasks.TaskAction
import java.nio.charset.StandardCharsets
import java.nio.file.FileSystems
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardOpenOption

@CacheableTask
abstract class ScanForTodosTask : DefaultTask() {
    companion object {
        const val TARGET_FILE = ".rdp-docs/developer/TODOs.md"
    }

    /** Regex (case-insensitive) som matcher TODO/FIXME. */
    @get:Input
    abstract val pattern: Property<String>

    /** Filendelser å skanne (uten punktum). Tom liste => alle tekstfiler. */
    @get:Input
    abstract val includeExts: ListProperty<String>

    /** Ekstra ekskluderinger i tillegg til .gitignore. Globs støttes. Relativt til prosjektroten. */
    @get:Input
    abstract val extraExcludeDirs: ListProperty<String>

    /** Eksplisitt kildeliste – disse filene skannes. */
    @get:InputFiles
    @get:PathSensitive(PathSensitivity.RELATIVE)
    abstract val sourceFiles: ConfigurableFileCollection

    /** Brukes kun for relativisering / .gitignore – ikke en task-input. */
    @get:Internal
    abstract val rootDirProp: DirectoryProperty

    /** .gitignore i rot (kun denne leses). Valgfri. */
    @get:InputFile
    @get:Optional
    @get:PathSensitive(PathSensitivity.RELATIVE)
    abstract val gitignoreFile: RegularFileProperty

    /** Outputfil (Markdown). */
    @get:OutputFile
    abstract val outputFile: RegularFileProperty

    /** Valgfri: GitLab base-URL. Hvis satt, brukes full-URL-lenker; ellers relative lenker. */
    @get:Input
    @get:Optional
    abstract val repoWebBase: Property<String>

    /** Branch for GitLab-lenker (hvis repoWebBase er satt). */
    @get:Input
    abstract val repoBranch: Property<String>

    private val internalDefaultExcludes =
        listOf(
            // Alltid ekskludert (relativt til prosjektrot):
            "no/ruter/tranop/gradle/todos/",
        )

    init {
        group = "verification"
        description = "Skann prosjektet for TODO/FIXME og skriv konsolidert TODOs.md"

        pattern.convention("(?:TODO|FIXME)")
        includeExts.convention(emptyList())
        extraExcludeDirs.convention(internalDefaultExcludes)

        rootDirProp.convention(project.layout.projectDirectory)
        gitignoreFile.convention(project.layout.projectDirectory.file(".gitignore"))
        // ⬇️ Default output: .rdp-docs/TODOs.md
        outputFile.convention(project.layout.projectDirectory.file(TARGET_FILE))

        // Standard: skann kun prosjektets kilder. Utvid i build.gradle ved behov (docs/, scripts/ ...)
        sourceFiles.from(
            project.layout.projectDirectory
                .dir("src")
                .asFileTree,
        )

        repoWebBase.convention("") // tom => bruk relative lenker
        repoBranch.convention("main")
    }

    // ----------------- .gitignore + ekstra ekskluderinger (enkel parser) -----------------

    private data class Rule(
        val matcherGlob: String,
        val negated: Boolean,
        val dirOnly: Boolean,
        val matcher: java.nio.file.PathMatcher,
    )

    private fun toSystemGlob(pat: String): String {
        val sep = FileSystems.getDefault().separator
        return if (sep == "/") pat else pat.replace("/", sep)
    }

    private fun rulesFromLines(lines: List<String>): List<Rule> =
        lines
            .asSequence()
            .map { it.trim() }
            .filter { it.isNotEmpty() && !it.startsWith("#") }
            .map { raw ->
                val neg = raw.startsWith("!")
                val p0 = if (neg) raw.drop(1) else raw
                val dirOnly = p0.endsWith("/")
                val p = p0.removeSuffix("/")
                val sys = toSystemGlob(p)
                val glob = "glob:$sys"
                Rule(
                    matcherGlob = glob,
                    negated = neg,
                    dirOnly = dirOnly,
                    matcher = FileSystems.getDefault().getPathMatcher(glob),
                )
            }.toList()

    private fun parseGitignore(file: Path?): List<Rule> =
        if (file == null || !Files.exists(file)) {
            emptyList()
        } else {
            rulesFromLines(Files.readAllLines(file, StandardCharsets.UTF_8))
        }

    private fun parseExtra(patterns: List<String>): List<Rule> = rulesFromLines(patterns)

    private fun isIgnored(
        path: Path,
        projectRoot: Path,
        rules: List<Rule>,
        isDir: Boolean,
    ): Boolean {
        val rel = projectRoot.relativize(path)
        var ignored = false
        for (r in rules) {
            val hit = r.matcher.matches(rel) || r.matcher.matches(rel.fileName)
            if (hit && (!r.dirOnly || isDir)) {
                ignored = !r.negated // siste regel vinner; negasjon tar tilbake
            }
        }
        return ignored
    }

    // ----------------- Filskann / binærdeteksjon -----------------

    private fun looksBinary(path: Path): Boolean =
        try {
            Files.newInputStream(path).use { input ->
                val buf = ByteArray(1024)
                val n = input.read(buf)
                if (n <= 0) return false
                if (buf.take(n).any { it == 0.toByte() }) return true
                val printable = (9..13).map(Int::toByte).toSet() + (32..126).map(Int::toByte).toSet()
                val nonText = buf.take(n).count { it !in printable }
                nonText.toDouble() / n > 0.30
            }
        } catch (_: Exception) {
            true
        }

    private fun scanFileForPattern(
        file: Path,
        pat: Regex,
    ): List<Pair<Int, String>> {
        val out = mutableListOf<Pair<Int, String>>()
        Files.newBufferedReader(file, StandardCharsets.UTF_8).useLines { lines ->
            var ln = 0
            lines.forEach { line ->
                ln++
                if (pat.containsMatchIn(line)) out += ln to line.trimEnd()
            }
        }
        return out
    }

    private fun urlPathEncode(path: String): String =
        path.split('/').joinToString("/") { seg ->
            java.net.URLEncoder
                .encode(seg, "UTF-8")
                .replace("+", "%20")
        }

    // ----------------- Markdown (relative eller GitLab-lenker) -----------------

    private fun buildMarkdown(
        findings: Map<Path, List<Pair<Int, String>>>,
        root: Path,
        baseUrl: String?,
        branch: String,
    ): String {
        val sb = StringBuilder()
        sb.appendLine("### Consolidated TODO/FIXME")
        sb.appendLine()
        val total = findings.values.sumOf { it.size }
        sb.appendLine("**Totalt funn:** $total")
        sb.appendLine()

        findings.toSortedMap(compareBy { root.relativize(it).toString().lowercase() }).forEach { (path, entries) ->
            val rel = root.relativize(path).toString().replace('\\', '/')
            sb.appendLine("### `$rel`")
            sb.appendLine()
            entries.forEach { (ln, text) ->
                val highlighted =
                    text.replace(Regex("\\b(TODO|FIXME)\\b", RegexOption.IGNORE_CASE)) { m ->
                        "**`${m.value.uppercase()}`**"
                    }
                val link =
                    if (!baseUrl.isNullOrBlank()) {
                        "${baseUrl.trimEnd('/')}/-/blob/${urlPathEncode(branch)}/${urlPathEncode(rel)}#L$ln"
                    } else {
                        "$rel#L$ln"
                    }
                sb.appendLine("- [L$ln]($link): $highlighted")
            }
            sb.appendLine()
        }

        if (total == 0) sb.appendLine("_Ingen TODO/FIXME funnet._")
        return sb.toString()
    }

    // ----------------- Task action -----------------

    @TaskAction
    fun run() {
        val root = rootDirProp.get().asFile.toPath()
        val out = outputFile.get().asFile.toPath()

        val exts = includeExts.get().map { it.lowercase().removePrefix(".") }.toSet()
        val pat = Regex(pattern.get(), RegexOption.IGNORE_CASE)

        val rules =
            parseGitignore(gitignoreFile.orNull?.asFile?.toPath()) +
                parseExtra(extraExcludeDirs.get())

        val findings = linkedMapOf<Path, MutableList<Pair<Int, String>>>()

        // Iterér kun over deklarerte input-filer (unngår å lese build-outputs)
        sourceFiles.files.forEach { f ->
            val p = f.toPath()
            if (!f.isFile) return@forEach
            if (p == out) return@forEach
            if (isIgnored(p, root, rules, false)) return@forEach

            if (exts.isNotEmpty()) {
                val ext = f.name.substringAfterLast('.', "")
                if (ext.lowercase() !in exts) return@forEach
            }
            if (looksBinary(p)) return@forEach

            val matches = scanFileForPattern(p, pat)
            if (matches.isNotEmpty()) findings.computeIfAbsent(p) { mutableListOf() }.addAll(matches)
        }

        val base = repoWebBase.orNull?.trim()?.takeIf { it.isNotEmpty() }
        val branch = repoBranch.get()

        val md = buildMarkdown(findings, root, base, branch)
        Files.createDirectories(out.parent)
        Files.writeString(
            out,
            md,
            StandardCharsets.UTF_8,
            StandardOpenOption.CREATE,
            StandardOpenOption.TRUNCATE_EXISTING,
        )

        logger.lifecycle("Skrev ${findings.values.sumOf { it.size }} funn til ${root.relativize(out)}")
    }
}
