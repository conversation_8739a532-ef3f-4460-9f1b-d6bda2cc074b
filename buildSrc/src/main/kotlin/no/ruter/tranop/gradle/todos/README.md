# ScanForTodosTask - Gradle Plugin

En avansert Gradle-task som skanner prosjektet for TODO og FIXME kommentarer og genererer en konsolidert markdown-rapport med GitLab-integrasjon.

## Oversikt

`ScanForTodosTask` er en cacheable Gradle-task som:
- Skan<PERSON> kildekodefiler for TODO/FIXME kommentarer med konfigurerbare regex-mønstre
- Respekterer `.gitignore` regler automatisk
- Genererer en strukturert markdown-rapport med alle funn
- Støtter filtrering på filtyper og ekskludering av mapper
- Inkluderer automatisk binærfildeteksjon
- Genererer lenker til GitLab (eller relative lenker)
- Optimalisert for Gradle's build cache og inkrementelle bygg

## Konfigurasjon

### Egenskaper

| Egenskap           | Type                         | Default                | Beskrivelse                                                             |
|--------------------|------------------------------|------------------------|-------------------------------------------------------------------------|
| `pattern`          | `Property<String>`           | `"(?:TODO\|FIXME)"`    | Regex-mønster (case-insensitive) for å finne kommentarer                |
| `includeExts`      | `ListProperty<String>`       | `[]` (alle tekstfiler) | Liste over filtyper å inkludere (uten punktum, f.eks. `["kt", "java"]`) |
| `extraExcludeDirs` | `ListProperty<String>`       | Se under               | Ekstra mapper å ekskludere (glob-mønstre, relativt til prosjektrot)     |
| `sourceFiles`      | `ConfigurableFileCollection` | `src/`                 | Filer som skal skannes                                                  |
| `rootDirProp`      | `DirectoryProperty`          | Prosjektrot            | Rotmappe for relativering av stier (intern bruk)                        |
| `gitignoreFile`    | `RegularFileProperty`        | `.gitignore`           | .gitignore-fil å respektere (valgfri)                                   |
| `outputFile`       | `RegularFileProperty`        | `.rdp-docs/TODOs.md`   | Utdatafil for rapporten                                                 |
| `repoWebBase`      | `Property<String>`           | `""` (tom)             | GitLab base-URL for full-URL-lenker                                     |
| `repoBranch`       | `Property<String>`           | `"main"`               | Branch for GitLab-lenker                                                |

### Standard ekskluderinger

Tasken ekskluderer alltid:
- `no/ruter/tranop/gradle/todos/` (plugin-koden selv)
- Binære filer (automatisk deteksjon basert på innhold)
- Filer som matcher `.gitignore` regler
- Filer som matcher `extraExcludeDirs` mønstre

## Bruk

### Grunnleggende bruk

```kotlin
tasks.register<ScanForTodosTask>("scanTodos")
```

### Tilpasset konfigurasjon

```kotlin
tasks.register<ScanForTodosTask>("scanTodos") {
    // Kun Kotlin og Java filer
    includeExts.set(listOf("kt", "java", "kts"))
    
    // Tilpasset mønster som inkluderer flere typer kommentarer
    pattern.set("(?:TODO|FIXME|HACK|XXX|NOTE)")
    
    // Ekstra mapper å ekskludere
    extraExcludeDirs.addAll(listOf("generated/", "temp/", "build/"))
    
    // Tilpasset utdatafil
    outputFile.set(layout.projectDirectory.file("docs/todos.md"))
    
    // GitLab-integrasjon
    repoWebBase.set("https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager")
    repoBranch.set("main")
    
    // Inkluder flere mapper
    sourceFiles.from(
        project.layout.projectDirectory.dir("src").asFileTree,
        project.layout.projectDirectory.dir("docs").asFileTree,
        project.layout.projectDirectory.dir("scripts").asFileTree
    )
}
```

### Kjøring

```bash
# Kjør TODO-skanning
./gradlew scanTodos

# Kjør med verbose logging
./gradlew scanTodos --info
```

## Utdataformat

Tasken genererer en markdown-fil med følgende struktur:

```markdown
### Consolidated TODO/FIXME

**Totalt funn:** 5

### `src/main/kotlin/MyClass.kt`

- [L42](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/MyClass.kt#L42): **`TODO`** Implementer validering
- [L67](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/main/kotlin/MyClass.kt#L67): **`FIXME`** Håndter edge case

### `src/test/kotlin/MyTest.kt`

- [L23](https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager/-/blob/main/src/test/kotlin/MyTest.kt#L23): **`TODO`** Legg til flere test cases
```

## Avanserte funksjoner

### GitIgnore-støtte

Tasken implementerer en enkel `.gitignore` parser som støtter:
- Grunnleggende glob-mønstre
- Negasjon med `!`
- Mappe-spesifikke regler med `/`
- Kommentarer (linjer som starter med `#`)

### Binærfildeteksjon

Automatisk deteksjon av binære filer basert på:
- Null-bytes i innholdet
- Prosentandel av ikke-printable karakterer (> 30%)
- Første 1024 bytes av filen

### URL-encoding

Støtter korrekt URL-encoding av filstier for GitLab-lenker, inkludert:
- Spesialtegn i filnavn
- Unicode-karakterer
- Mellomrom (konvertert til `%20`)

## Ytelse og optimalisering

Tasken er optimalisert for ytelse ved å:
- **Cacheable**: Støtter Gradle's build cache for raskere påfølgende kjøringer
- **Inkrementell**: Kun prosesserer endrede filer
- **Effektiv fillesing**: Hopper over binære filer tidlig i prosessen
- **Begrenset scope**: Kun prosesserer deklarerte input-filer
- **Regex-optimalisering**: Bruker kompilerte regex-mønstre

## Feilsøking

### Vanlige problemer

1. **Ingen funn**: Sjekk at `sourceFiles` inkluderer riktige mapper
2. **Filer ekskludert**: Verifiser `.gitignore` og `extraExcludeDirs` innstillinger
3. **Binære filer**: Tasken hopper automatisk over binære filer
4. **GitLab-lenker**: Sørg for at `repoWebBase` er korrekt satt

### Debug-logging

```bash
./gradlew scanTodos --debug
```

## Integrasjon i CI/CD

```yaml
# .gitlab-ci.yml
scan-todos:
  stage: verify
  script:
    - ./gradlew scanTodos
  artifacts:
    reports:
      # Publiser TODO-rapport som artefakt
    paths:
      - .rdp-docs/TODOs.md
    expire_in: 1 week
```

## Eksempel på build.gradle.kts

```kotlin
tasks.register<ScanForTodosTask>("scanTodos") {
    group = "verification"
    description = "Skann for TODO/FIXME kommentarer"
    
    // Konfigurasjon
    includeExts.set(listOf("kt", "java", "kts", "sql", "md"))
    pattern.set("(?:TODO|FIXME|HACK|XXX|BUG)")
    
    // GitLab-integrasjon
    repoWebBase.set("https://gitlab.com/ruter-as/tranop/assignment/assignment-journey-manager")
    repoBranch.set("main")
    
    // Ekskluder test-ressurser og genererte filer
    extraExcludeDirs.addAll(listOf(
        "src/test/resources/",
        "build/",
        "target/",
        ".gradle/"
    ))
}

// Kjør automatisk som del av check-task
tasks.named("check") {
    dependsOn("scanTodos")
}

## Lisens

Dette scriptet er en del av Ruter's interne verktøy og følger organisasjonens retningslinjer for kodebruk.
