package no.ruter.tranop.app.plan.stop.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.plan.stop.lifecycle.StopPointLifeCycleConfig
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.dated.dto.metadata
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Component

@Component
class StopPointOutboxEntityV2PublishingRoutine(
    outboxService: OutboxService,
    val mapper: StopPointEntityOutputMapper,
    val publisher: StopPointOutboxEntityV2PublishingService,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    stopPointLifeCycleConfig: StopPointLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        StopPointTable,
        StopPointRecord,
        DTOStopPoint,
        DatedJourneyStopPointKeyV2,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.STOP,
        targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
        payloadClass = DTOStopPoint::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = stopPointLifeCycleConfig,
    ) {
    override fun map(
        data: DTOStopPoint?,
        context: MappingContext,
    ): DatedJourneyStopPointKeyV2? = mapper.mapValue(data, context)

    override fun publish(
        messages: List<OutboxOutputMessage<DatedJourneyStopPointKeyV2>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        messages.forEach { msg ->
            val key = msg.outbox.data.payloadRef
            publisher.publishToKafka(key, msg.value, postProcessing)
        }
    }

    override fun metadata(data: DTOStopPoint?): Map<String, Any?> = data?.metadata() ?: emptyMap()
}
