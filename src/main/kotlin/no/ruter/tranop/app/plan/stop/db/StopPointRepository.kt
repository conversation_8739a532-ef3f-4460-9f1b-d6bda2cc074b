package no.ruter.tranop.app.plan.stop.db

import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder.Pagination
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord
import org.jooq.DSLContext
import org.jooq.exception.DataChangedException
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        StopPointTable,
        StopPointRecord,
        InternalStopPoint,
        StopPointRecordMapper,
        StopPointQueryBuilder,
        BaseRecordTableMetadata<StopPointTable, StopPointRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = StopPointRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = StopPointQueryBuilder.TYPE
        val TABLE = TYPE.table

        private val published =
            TABLE.PUBLISHED_REVISION.isNotNull
                .and(TABLE.REVISION.isNotNull)
                .and(TABLE.REVISION.eq(TABLE.PUBLISHED_REVISION))

        private val notPublished = published.not()

        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
            )

        const val PUBLISH_OWNER = "STOP_POINT_DTO"
    }

    override fun queryBuilder() = StopPointQueryBuilder()

    fun exists(
        ref: String?,
        hash: String?,
    ): Boolean {
        ref ?: return false
        hash ?: return false
        return dslContext.fetchExists(table, table.REF.eq(ref).and(table.JSON_HASH.eq(hash)))
    }

    fun store(
        ctx: StopPointInputContext,
        now: OffsetDateTime = timeService.now(),
    ): Boolean {
        val input = ctx.stopPoint
        val ref = input.ref
        val hash = input.hash()
        if (exists(ref, hash)) {
            ctx.duplicate = true
            return false
        }
        val stored = fetchByRef(ref)

        val record =
            if (stored == null) {
                ctx.insert = true
                dslContext.newRecord(table)
            } else {
                ctx.update = true
                ctx.jsonDiff = JsonDiff.of(stored.data, ctx.stopPoint)
                stored.record
            }
        recordMapper.update(record, ctx, now)
        val res = record.store() > 0

        if (res) {
            val payload = record.jsonData
            addToOutbox(
                ref = ref,
                payload = payload,
            )
        }
        return res
    }

    fun findUnpublished(
        pagination: Pagination,
        excludeRefs: MutableSet<String>,
        olderThanDateTime: OffsetDateTime,
    ): List<InternalStopPoint> {
        val olderThan = table.MODIFIED_AT.lessThan(olderThanDateTime)
        val notExcludedRef = table.REF.notIn(excludeRefs)
        val republish = table.REPUBLISH.eq(true)
        val publish = notPublished.or(republish)

        return fetch(publish.and(olderThan).and(notExcludedRef), pagination)
    }

    fun findDTOUnpublished(
        pagination: Pagination,
        excludeRefs: Set<String> = emptySet(),
        olderThanDateTime: OffsetDateTime,
    ): List<InternalStopPoint> {
        val t2 = Tables.DATED_JOURNEY_PUBLISH_STATUS
        val cond =
            table.REF
                .notIn(excludeRefs)
                .and(
                    t2.REF.isNull.or(table.REVISION.gt(t2.REVISION).and(t2.OWNER.eq(PUBLISH_OWNER))),
                ).and(table.MODIFIED_AT.lessOrEqual(olderThanDateTime))

        val query =
            dslContext
                .select(table.asterisk())
                .from(table.leftJoin(t2).on(table.REF.eq(t2.REF)))
                .where(cond)
                .limit(pagination.limit)
                .offset(pagination.offset)
        return fetch(query)
    }

    fun markPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime = timeService.now(),
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .update(table)
                .set(table.PUBLISHED_AT, now)
                .set(table.PUBLISHED_REVISION, revision)
                .set(table.REPUBLISH, false)
                .where(table.REF.eq(key))
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update published_at timestamp on dated-journey [id=$key]"
            log.warn(message, exception)
            null
        }

    fun markDTOPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime = timeService.now(),
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .insertInto(Tables.DATED_JOURNEY_PUBLISH_STATUS)
                .columns(
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.OWNER,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REF,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION,
                ).values(PUBLISH_OWNER, key, revision)
                .onDuplicateKeyUpdate()
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.PUBLISHED_AT, now)
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION, revision)
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update publish to DTO topic status timestamp on stop point [id=$key]"
            log.warn(message, exception)
            null
        }

    fun fetchByQuayId(quayId: String): List<InternalStopPoint> =
        fetch(
            TABLE.QUAY_ID.eq(quayId),
        )

    /** Return map of quay ids keyed by stop point refs. **/
    fun resolveQuayIds(stopPointRefs: Collection<String>): Map<String, String> {
        val res = LinkedHashMap<String, String>()
        dslContext
            .select(table.REF, table.QUAY_ID)
            .from(table)
            .where(table.REF.`in`(stopPointRefs))
            .fetch { row ->
                val ref = row.value1()
                val quayId = row.value2()
                res[ref] = quayId
            }
        return res
    }
}
