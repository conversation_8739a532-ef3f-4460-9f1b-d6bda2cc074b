package no.ruter.tranop.app.plan.stop.output.internal

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointSerde
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

// TODO: Gather the impls of all these Publishing services
@Service
class StopPointDTOPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointLinkConfigProperties: StopPointLinkConfigProperties,
    val repository: StopPointRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DTOStopPoint,
        DTOStopPointSerde,
    >(
        producerBinding = kafkaConfigService.stopPointDtoOutputProducer,
        config = stopPointLinkConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_KEY_V2_REF,
    ) {
    fun publish(
        key: String,
        input: DTOStopPoint?,
        currentRevision: Int,
    ): Int {
        var res = 0
        publishToKafka(key, input) { exception ->
            if (exception == null) {
                res += repository.markDTOPublished(key, currentRevision) ?: 0
            }
        }

        return res
    }
}
