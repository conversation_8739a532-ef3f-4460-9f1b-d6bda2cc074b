package no.ruter.tranop.app.plan.link.input

import no.ruter.plandata.journey.dated.v2.dto.kafka.PDJStopPointLinkDeserializer
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaConsumer
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConsumerConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    name = [
        KafkaConsumerConfig.CONF_KEY_ENABLED,
        "${StopPointLinkInputConsumer.CONF_PREFIX}.enabled",
    ],
    havingValue = "true",
)
class StopPointLinkInputConsumer(
    private val linkService: StopPointLinkInputService,
    insightService: InsightService,
) : AbstractKafkaConsumer(
        channel = linkService.defaultChannel,
        insightService = insightService,
    ) {
    companion object {
        const val CONF_PREFIX = "${StopPointLinkConfigProperties.CONF_PREFIX}.input.kafka.dto.consumer"
    }

    private val deserializer = PDJStopPointLinkDeserializer()

    @KafkaListener(
        topics = ["\${$CONF_PREFIX.topic}"],
        groupId = "\${$CONF_PREFIX.group}",
    )
    fun listenGroup(
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
        ack: Acknowledgment,
    ) {
        try {
            process(
                record = consumerRecord,
                deserializer = deserializer,
                handler = linkService::process,
            )
        } catch (e: Exception) {
            logger.error("Not able to process stop point link [${consumerRecord.key()} / ${consumerRecord.headers()}]", e)
        } finally {
            ack.acknowledge()
        }
    }
}
