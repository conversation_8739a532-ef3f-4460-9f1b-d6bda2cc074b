package no.ruter.tranop.app.plan.stop.output.internal

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointSerde
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class StopPointOutboxInternalPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    configProperties: StopPointConfigProperties,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DTOStopPoint,
        DTOStopPointSerde,
    >(
        producerBinding = kafkaConfigService.stopPointDtoOutputProducer,
        config = configProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_KEY_V2_REF,
    )
