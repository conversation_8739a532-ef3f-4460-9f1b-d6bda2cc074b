package no.ruter.tranop.app.plan.stop.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint

class InternalStopPoint(
    data: DTOStopPoint,
    record: StopPointRecord,
) : JSONRecordData<
        StopPointTable,
        StopPointRecord,
        DTOStopPoint,
    >(RecordType.STOP, data, record) {
    override val dataRef: String?
        get() = data.ref
}
