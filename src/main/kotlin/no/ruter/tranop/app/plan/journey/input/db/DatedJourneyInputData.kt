package no.ruter.tranop.app.plan.journey.input.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyInputTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyInputRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney

class DatedJourneyInputData(
    data: DTODatedJourney,
    record: DatedJourneyInputRecord,
) : JSONRecordData<
        DatedJourneyInputTable,
        DatedJourneyInputRecord,
        DTODatedJourney,
    >(RecordType.DATED_JOURNEY_INPUT, data, record) {
    override val dataRef: String?
        get() = data.ref

    val datedJourneyV2Ref: String?
        get() = ref
}
