package no.ruter.tranop.app.plan.link.db

import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder.Pagination
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.link.input.StopPointLinkInputContext
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointLinkTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord
import org.jooq.DSLContext
import org.jooq.exception.DataChangedException
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointLinkRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        StopPointLinkTable,
        StopPointLinkRecord,
        InternalStopPointLink,
        StopPointLinkRecordMapper,
        StopPointLinkQueryBuilder,
        BaseRecordTableMetadata<StopPointLinkTable, StopPointLinkRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = StopPointLinkRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = StopPointLinkQueryBuilder.TYPE
        val TABLE = TYPE.table

        private val published =
            TABLE.PUBLISHED_REVISION.isNotNull
                .and(TABLE.REVISION.isNotNull)
                .and(TABLE.REVISION.eq(TABLE.PUBLISHED_REVISION))

        private val notPublished = published.not()

        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
            )

        const val PUBLISH_OWNER = "STOP_POINT_LINK_DTO"
    }

    override fun queryBuilder() = StopPointLinkQueryBuilder()

    fun exists(
        ref: String?,
        hash: String?,
    ): Boolean {
        ref ?: return false
        hash ?: return false
        return dslContext.fetchExists(table, table.REF.eq(ref).and(table.JSON_HASH.eq(hash)))
    }

    fun store(
        ctx: StopPointLinkInputContext,
        now: OffsetDateTime = timeService.now(),
    ): Boolean {
        val input = ctx.link
        val ref = input.ref
        val hash = input.hash()
        if (exists(ref, hash)) {
            ctx.duplicate = true
            return false
        }
        val stored = fetchByRef(ref)

        val record =
            if (stored == null) {
                ctx.insert = true
                dslContext.newRecord(table)
            } else {
                ctx.update = true
                ctx.jsonDiff = JsonDiff.of(stored.data, ctx.link)
                stored.record
            }
        recordMapper.update(record, ctx, now)
        val res = record.store() > 0
        if (res) {
            addToOutbox(
                ref = ref,
                payload = record.jsonData,
            )
        }
        return res
    }

    fun findUnpublished(
        pagination: Pagination,
        excludeRefs: MutableSet<String>,
        olderThanDateTime: OffsetDateTime,
    ): List<InternalStopPointLink> {
        val olderThan = table.MODIFIED_AT.lessThan(olderThanDateTime)
        val notExcludedRef = table.REF.notIn(excludeRefs)
        val republish = table.REPUBLISH.eq(true)
        val publish = notPublished.or(republish)

        return fetch(publish.and(olderThan).and(notExcludedRef), pagination)
    }

    fun markPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime = timeService.now(),
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .update(table)
                .set(table.PUBLISHED_AT, now)
                .set(table.PUBLISHED_REVISION, revision)
                .set(table.REPUBLISH, false)
                .where(table.REF.eq(key))
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update published_at timestamp on dated-journey [id=$key]"
            log.warn(message, exception)
            null
        }

    fun findDTOUnpublished(
        pagination: Pagination,
        excludeRefs: Set<String> = emptySet(),
        olderThanDateTime: OffsetDateTime,
    ): List<InternalStopPointLink> {
        val t2 = Tables.DATED_JOURNEY_PUBLISH_STATUS
        val cond =
            table.REF
                .notIn(excludeRefs)
                .and(
                    t2.REF.isNull.or(table.REVISION.gt(t2.REVISION).and(t2.OWNER.eq(PUBLISH_OWNER))),
                ).and(table.MODIFIED_AT.lessOrEqual(olderThanDateTime))

        val query =
            dslContext
                .select(table.asterisk())
                .from(table.leftJoin(t2).on(table.REF.eq(t2.REF)))
                .where(cond)
                .limit(pagination.limit)
                .offset(pagination.offset)
        return fetch(query)
    }

    fun markDTOPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime = timeService.now(),
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .insertInto(Tables.DATED_JOURNEY_PUBLISH_STATUS)
                .columns(
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.OWNER,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REF,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION,
                ).values(PUBLISH_OWNER, key, revision)
                .onDuplicateKeyUpdate()
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.PUBLISHED_AT, now)
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION, revision)
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update publish to DTO topic status timestamp on stop point link [id=$key]"
            log.warn(message, exception)
            null
        }
}
