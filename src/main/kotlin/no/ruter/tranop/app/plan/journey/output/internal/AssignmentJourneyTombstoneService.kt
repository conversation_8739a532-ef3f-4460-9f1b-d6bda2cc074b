package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serdes
import org.springframework.stereotype.Service

@Service
class AssignmentJourneyTombstoneService(
    kafkaConfigService: KafkaConfigService,
    assignmentJourneyConfigProperties: AssignmentJourneyConfigProperties,
    insightService: InsightService,
) : AbstractKafkaPublisherService<String?, Serde<String?>, ByteArray, Serdes.ByteArraySerde>(
        producerBinding = kafkaConfigService.assignmentJourneyTombstoneProducer,
        config = assignmentJourneyConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF,
    )
