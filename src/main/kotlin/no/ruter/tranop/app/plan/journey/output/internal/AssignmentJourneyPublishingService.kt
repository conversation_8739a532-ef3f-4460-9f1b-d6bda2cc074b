package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.dated.journey.dto.kafka.DTODatedJourneySerde
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class AssignmentJourneyPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    assignmentJourneyProperties: AssignmentJourneyConfigProperties,
    val datedJourneyService: DatedJourneyService,
) : AbstractKafkaPublisherService<String?, Serde<String?>, DTODatedJourney, DTODatedJourneySerde>(
        producerBinding = kafkaConfigService.assignmentJourneyOutputProducer,
        config = assignmentJourneyProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF,
    ) {
    fun publish(
        key: String,
        datedJourney: DTODatedJourney?,
        currentRevision: Int,
    ): Int {
        var res = 0
        publishToKafka(key, datedJourney) { exception ->
            if (exception == null) {
                res += datedJourneyService.markKafkaPublished(key, currentRevision) ?: 0
            }
        }

        return res
    }
}
