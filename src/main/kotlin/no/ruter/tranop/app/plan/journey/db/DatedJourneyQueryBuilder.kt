package no.ruter.tranop.app.plan.journey.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord

class DatedJourneyQueryBuilder :
    BaseRecordQueryBuilder<
        DatedJourneyTable,
        DatedJourneyRecord,
        BaseRecordTableMetadata<DatedJourneyTable, DatedJourneyRecord>,
        DatedJourneyQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.DATED_JOURNEY
    }
}
