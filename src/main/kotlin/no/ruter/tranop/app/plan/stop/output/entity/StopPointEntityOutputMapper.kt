package no.ruter.tranop.app.plan.stop.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2Data
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.output.entity.toInputMetadata
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.springframework.stereotype.Component

@Component
class StopPointEntityOutputMapper(
    config: StopPointConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTOStopPoint,
        StopPointInputContext,
        DatedJourneyStopPointKeyV2,
        StopPointOutputEntityContext,
    >(
        RecordType.STOP,
        config,
        timeService,
        insightService,
    ) {
    override fun getOwnerId(value: DatedJourneyStopPointKeyV2?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTOStopPoint, StopPointInputContext>): StopPointOutputEntityContext =
        StopPointOutputEntityContext(
            input = input,
        )

    @Deprecated("Make use of the mapValue without the InputMessage pattern")
    override fun mapValue(
        input: InputMessage<DTOStopPoint, StopPointInputContext>,
        context: StopPointOutputEntityContext,
    ): DatedJourneyStopPointKeyV2? {
        val value = input.value!! // Tombstones never get here, so value is always non-null.
        return mapEntity(value, context)
    }

    override fun mapValue(
        input: DTOStopPoint?,
        context: MappingContext,
    ): DatedJourneyStopPointKeyV2? = input?.let { mapEntity(input, context) }

    private fun mapEntity(
        value: DTOStopPoint,
        context: MappingContext,
    ): DatedJourneyStopPointKeyV2? {
        val path = PATH_ROOT
        val stop = mapStopPoint(value, path, context)
        val events = context.mapList(value.events, PATH_ROOT_EVENTS, this::mapEvent)
        return context.build(path) {
            val data =
                DatedJourneyStopPointKeyV2Data
                    .newBuilder()
                    .setStop(stop)
                    .setEvents(events)
                    .build()

            val header =
                createEntityHeader(
                    inputMetadata = value.header.toInputMetadata(timeService),
                    key = value.ref,
                )
            DatedJourneyStopPointKeyV2
                .newBuilder()
                .setEntityData(data)
                .setEntityHeader(header)
                .build()
        }
    }
}
