package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.metadata
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Component

@Component
class DatedJourneyOutboxInternalPublishingRoutine(
    outboxService: OutboxService,
    val publisher: DatedJourneyOutboxInternalPublishingService,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    lifeCycleConfig: DatedJourneyLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        DatedJourneyTable,
        DatedJourneyRecord,
        DTODatedJourney,
        DTODatedJourney,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.DATED_JOURNEY,
        targetType = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
        payloadClass = DTODatedJourney::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = lifeCycleConfig,
    ) {
    override fun map(
        data: DTODatedJourney?,
        context: MappingContext,
    ): DTODatedJourney? = data

    override fun publish(
        messages: List<OutboxOutputMessage<DTODatedJourney>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        messages.forEach { msg ->
            val key = msg.outbox.data.payloadRef
            publisher.publishToKafka(key, msg.value, postProcessing)
        }
    }

    override fun metadata(data: DTODatedJourney?): Map<String, Any?> = data?.metadata() ?: emptyMap()
}
