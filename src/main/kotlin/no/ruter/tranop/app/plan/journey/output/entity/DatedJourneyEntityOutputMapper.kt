package no.ruter.tranop.app.plan.journey.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2Data
import no.ruter.avro.entity.datedjourney.v2.common.DestinationDisplay
import no.ruter.avro.entity.datedjourney.v2.common.ExternalDatedJourneyId
import no.ruter.avro.entity.datedjourney.v2.common.JourneyDeviationState
import no.ruter.avro.entity.datedjourney.v2.common.JourneyMitigationState
import no.ruter.avro.entity.datedjourney.v2.common.JourneyReferences
import no.ruter.avro.entity.datedjourney.v2.common.JourneyServiceVarianceReference
import no.ruter.avro.entity.datedjourney.v2.common.JourneyState
import no.ruter.avro.entity.datedjourney.v2.common.LifeCycleInfo
import no.ruter.avro.entity.datedjourney.v2.common.Line
import no.ruter.avro.entity.datedjourney.v2.common.Lineage
import no.ruter.avro.entity.datedjourney.v2.common.Operator
import no.ruter.avro.entity.datedjourney.v2.common.OperatorContract
import no.ruter.avro.entity.datedjourney.v2.common.Replaces
import no.ruter.avro.entity.datedjourney.v2.common.Vehicle
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2Call
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2CallInterchange
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2CallInterchangeFrom
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2CallInterchangeReferences
import no.ruter.avro.entity.datedjourney.v2.journey.DatedJourneyV2CallInterchangeTo
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.input.InputMetadata
import no.ruter.tranop.app.common.mapping.map
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.mapping.output.OutputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.common.dto.model.DTOLifeCycleInfo
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchange
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeAttributes
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeFrom
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeReferences
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeTo
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReferences
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyRelatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReplaces
import no.ruter.tranop.dated.journey.dto.model.common.DTODestination
import no.ruter.tranop.dated.journey.dto.model.common.DTOJourneyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOLine
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineage
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageOriginatingFrom
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageType
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperatorContract
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportModeProperty
import no.ruter.tranop.dated.journey.dto.model.common.DTOVehicle
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyDeviationState
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyMitigationState
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyServiceVarianceReference
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyState
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class DatedJourneyEntityOutputMapper(
    config: DatedJourneyConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTODatedJourney,
        DatedJourneyEntityInputContext,
        DatedJourneyKeyV2,
        DatedJourneyEntityOutputContext,
    >(
        RecordType.DATED_JOURNEY,
        config,
        timeService,
        insightService,
    ) {
    companion object {
        val JOURNEY_TYPES =
            entityValues(
                DTOJourneyType.DEAD_RUN to "DEAD_RUN",
                DTOJourneyType.SERVICE_JOURNEY to "SERVICE_JOURNEY",
                DTOJourneyType.UNKNOWN to UNKNOWN,
            )
    }

    private val toggles = config.toggles

    override fun getOwnerId(value: DatedJourneyKeyV2?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTODatedJourney, DatedJourneyEntityInputContext>) =
        DatedJourneyEntityOutputContext(input)

    @Deprecated("Make use of the mapValue without the InputMessage pattern")
    override fun mapValue(
        input: InputMessage<DTODatedJourney, DatedJourneyEntityInputContext>,
        context: DatedJourneyEntityOutputContext,
    ): DatedJourneyKeyV2? {
        val value = input.value!! // Tombstones never get here, so value is always non-null.

        return map(value, context)
    }

    private fun map(
        value: DTODatedJourney,
        context: MappingContext,
    ): DatedJourneyKeyV2? {
        val path = PATH_ROOT
        val datedJourney = mapDatedJourney(value, path, context)
        val events = context.mapList(value.events, PATH_ROOT_EVENTS, this::mapEvent)
        return context.build(path) {
            val data =
                DatedJourneyKeyV2Data
                    .newBuilder()
                    .setEvents(events)
                    .setJourney(datedJourney)
                    .build()

            val header = createEntityHeader(inputMetadata = value.header.toInputMetadata(timeService), key = value.ref)
            DatedJourneyKeyV2
                .newBuilder()
                .setEntityData(data)
                .setEntityHeader(header)
                .build()
        }
    }

    override fun mapValue(
        input: DTODatedJourney?,
        context: MappingContext,
    ): DatedJourneyKeyV2? = input?.let { map(input, context) }

    private fun mapDatedJourney(
        input: DTODatedJourney,
        path: String,
        context: MappingContext,
    ): DatedJourneyV2? {
        val type =
            input.type.toEntityValue(
                context = context,
                path = "$path.type",
                insight = "dated.journey.type",
                values = JOURNEY_TYPES,
                errorType = MappingDetails.Type.ERROR,
                unmappedPassThrough = false,
            )
        val operatingDate =
            input.operatingDate.toLocalDate(
                context = context,
                path = "$path.operatingDate",
                insight = "dated.journey.operating.date",
                errorType = MappingDetails.Type.ERROR,
            )
        val plan =
            input.plan.notNull(
                context = context,
                path = "$path.plan",
                insight = "missing.datedjourney.plan",
                errorType = MappingDetails.Type.ERROR,
            )
        val journeyReferences =
            input.journeyReferences.notNull(
                context = context,
                path = "$path.journeyreferences",
                insight = "missing.datedjourney.journeyreferences",
                errorType = MappingDetails.Type.WARN,
            )

        val externalDatedServiceJourney =
            journeyReferences?.datedServiceJourneyId.notEmpty(
                context = context,
                path = "$path.journeyreferences.datedServiceJourneyId",
                insight = "missing.datedjourney.journeyreferences.datedServiceJourneyId",
                errorType = MappingDetails.Type.INFO,
            )

        val runtimePatternRef =
            journeyReferences?.runtimePatternRef.notEmpty(
                context = context,
                path = "$path.journeyreferences.runtimepatternref",
                insight = "missing.datedjourney.journeyreferences.runtimepatternref",
                errorType = MappingDetails.Type.INFO,
            )
        val journeyPatternRef =
            journeyReferences?.journeyPatternRef.notEmpty(
                context = context,
                path = "$path.journeyreferences.journeypatternref",
                insight = "missing.datedjourney.journeyreferences.journeypatternref",
                errorType = MappingDetails.Type.INFO,
            )

        val calls = context.mapList(plan?.calls, path = "$path.plan.calls", this::mapPlannedCall)
        val stops = context.mapList(plan?.stops, path = "$path.plan.stops", this::mapStopPoint)
        val links = context.mapList(plan?.links, path = "$path.plan.links", this::mapStopPointLink)

        val line = mapLine(input.line, path = "$path.line", context)
        val vehicles = context.mapList(input.vehicles, path = "$path.vehicles", this::mapVehicle)
        val operators = context.mapList(input.operators, path = "$path.operators", this::mapOperator)
        val contracts =
            context.mapList(input.operatorContracts, path = "$path.operatorContracts", this::mapOperatorContract)
        val lineage = context.mapList(input.lineage, path = "$path.lineage", this::mapLineage)

        val lifeCycleInfo = context.map(input.lifeCycleInfo, path = "$path.lifeCycleInfo", this::mapLifeCycleInfo)
        val journeyState = context.map(input.journeyState, path = "$path.journeyState", this::mapJourneyState)

        val externalJourneyId = mapExternalJourneyId(journeyReferences, path, context)
        val replaces = input.replaces?.let { mapReplaces(it, operatingDate, path = "$path.replaces", context) }

        return context.build(path) {
            val builder =
                DatedJourneyV2
                    .newBuilder()
                    .setType(type)
                    .setDataSource(getDataSource(input.lifeCycleInfo))
                    .setEntityDatedJourneyKeyV2Ref(input.ref) // Already verified by input mapper.
                    .setRuntimePatternRef(runtimePatternRef)
                    .setJourneyPatternRef(journeyPatternRef)
                    .setName(input.name)
                    .setOperatingDate(operatingDate!!.toString()) // Coercion is OK, we never get here if journeyDate is null.
                    .setLine(line) // null is OK, line is optional
                    .setLineDirection(input.direction?.value)
                    .setVehicleTaskId(input.vehicleTask ?: journeyReferences?.vehicleTaskRef)
                    .setVehicleJourneyId(journeyReferences?.vehicleJourneyId)
                    .setOperators(operators)
                    .setOperatorContracts(contracts)
                    .setVehicles(vehicles)
                    .setLineage(lineage)
                    .setCallSequence(calls)
                    .setStopPoints(stops)
                    .setStopPointLinks(links)
                    .setFirstDepartureDateTime(plan?.firstDepartureDateTime)
                    .setLastArrivalDateTime(plan?.lastArrivalDateTime)
                    .setExternalJourneyId(externalJourneyId)
                    .setExternalDatedServiceJourneyId(externalDatedServiceJourney)
                    .setIsPublic(input.public)
                    .setDeleted(input.deleted ?: false) // null value equal to false value here
                    .setLifeCycleInfo(lifeCycleInfo)
                    .setJourneyState(journeyState)
                    .setExtra(input.extra)
                    .setReplaces(replaces) // optional

            if (toggles.omitEnabled) {
                builder.omitted = input.omitted
            }
            if (toggles.cancellationEnabled) {
                builder.cancelled = input.cancelled
            }

            builder.build()
        }
    }

    private fun mapLifeCycleInfo(
        input: DTOLifeCycleInfo,
        path: String,
        context: MappingContext,
    ): LifeCycleInfo? {
        val revision =
            input.revision.notNull(
                context = context,
                path = "$path.lifeCycleInfoRef",
                insight = "missing.lifecycle.info.revision",
                errorType = MappingDetails.Type.WARN,
            )

        val created =
            input.created.notEmpty(
                context = context,
                path = "$path.lifeCycleInfoCreated",
                insight = "missing.lifecycle.info.created",
                errorType = MappingDetails.Type.WARN,
            )

        val modified =
            input.modified.notEmpty(
                context = context,
                path = "$path.lifeCycleInfoModified",
                insight = "missing.lifecycle.info.modified",
                errorType = MappingDetails.Type.WARN,
            )

        val dataSource =
            input.dataSource.notEmpty(
                context = context,
                path = "$path.lifeCycleInfoDataSource",
                insight = "missing.lifecycle.info.datasource",
                errorType = MappingDetails.Type.WARN,
            )

        if (revision == null || created == null) {
            return null
        }

        return context.build(path) {
            val builder =
                LifeCycleInfo
                    .newBuilder()
                    .setRevision(revision)
                    .setCreated(created)
                    .setModified(modified)
                    .setDataSource(dataSource)
            builder.build()
        }
    }

    private fun mapJourneyState(
        input: DTOJourneyState,
        path: String,
        context: MappingContext,
    ): JourneyState? {
        val journeyDeviationState =
            context.map(
                input.journeyDeviationState,
                path = "$path.journeyDeviationState",
                this::mapJourneyDeviationState,
            )
        val journeyMitigationState =
            context.map(
                input.journeyMitigationState,
                path = "$path.journeyMitigationState",
                this::mapJourneyMitigationState,
            )
        return context.build(path) {
            JourneyState
                .newBuilder()
                .setJourneyDeviationState(journeyDeviationState)
                .setJourneyMitigationState(journeyMitigationState)
                .build()
        }
    }

    private fun mapJourneyMitigationState(
        input: DTOJourneyMitigationState,
        path: String,
        context: MappingContext,
    ): JourneyMitigationState? {
        val cancelled =
            input.cancelled.notNull(
                context = context,
                path = "$path.cancelled",
                insight = "missing.journey.mitigation.state.cancelled",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )
        val partiallyCancelled =
            input.partiallyCancelled.notNull(
                context = context,
                path = "$path.partiallyCancelled",
                insight = "missing.journey.mitigation.state.partiallyCancelled",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )
        val contingencyVehiclePlanned =
            input.contingencyVehiclePlanned.notNull(
                context = context,
                path = "$path.contingencyVehiclePlanned",
                insight = "missing.journey.mitigation.state.contingencyVehiclePlanned",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )

        val mitigations = context.mapList(input.mitigations, path = "$path.mitigations", mapper = this::mapVarianceReference)
        return context.build(path) {
            JourneyMitigationState
                .newBuilder()
                .setCancelled(cancelled ?: false)
                .setPartiallyCancelled(partiallyCancelled ?: false)
                .setContingencyVehiclePlanned(contingencyVehiclePlanned ?: false)
                .setMitigations(mitigations)
                .build()
        }
    }

    private fun mapJourneyDeviationState(
        input: DTOJourneyDeviationState,
        path: String,
        context: MappingContext,
    ): JourneyDeviationState? {
        val omitted =
            input.omitted.notNull(
                context = context,
                path = "$path.omitted",
                insight = "missing.journey.deviation.state.omitted",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )
        val partiallyOmitted =
            input.partiallyOmitted.notNull(
                context = context,
                path = "$path.partiallyOmitted",
                insight = "missing.journey.deviation.state.partiallyOmitted",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )

        val deviations = context.mapList(input.deviations, path = "$path.deviations", mapper = this::mapVarianceReference)
        return context.build(path) {
            JourneyDeviationState
                .newBuilder()
                .setOmitted(omitted)
                .setPartiallyOmitted(partiallyOmitted)
                .setDeviations(deviations)
                .build()
        }
    }

    private fun mapVarianceReference(
        input: DTOJourneyServiceVarianceReference,
        path: String,
        context: MappingContext,
    ): JourneyServiceVarianceReference? =
        context.build(path) {
            JourneyServiceVarianceReference
                .newBuilder()
                .setRef(input.ref)
                .setCode(input.code)
                .build()
        }

    private fun mapReplaces(
        input: DTODatedJourneyReplaces,
        operatingDate: LocalDate?,
        path: String,
        context: MappingContext,
    ): Replaces? {
        val journeys =
            input.journeys.mapIndexedNotNull { idx, journey ->
                mapReplacesJourney(journey, operatingDate, "$path.journeys[$idx]", context)
            }
        return context.build(path) {
            Replaces
                .newBuilder()
                .setJourneys(journeys)
                .build()
        }
    }

    private fun mapReplacesJourney(
        input: DTODatedJourneyRelatedJourney,
        operatingDate: LocalDate?,
        path: String,
        context: MappingContext,
    ): JourneyReferences? {
        val externalJourneyId = mapExternalJourneyId(input.journeyReferences, path, context)

        val entityDatedJourneyKeyV2Ref =
            input.entityDatedJourneyKeyV2Ref.notEmpty(
                context = context,
                path = "$path.entityDatedJourneyKeyV2Ref",
                insight = "missing.replaces.journeys.entityDatedJourneyKeyV2Ref",
                errorType = MappingDetails.Type.WARN,
            )

        val externalDatedJourneyId =
            externalJourneyId
                ?.let { id ->
                    operatingDate?.toString()?.let { date ->
                        ExternalDatedJourneyId(id, date)
                    }
                }.notNull(
                    context = context,
                    path = "$path.externalDatedJourneyId",
                    insight = "not.able.to.construct.externalDatedJourneyId",
                    errorType = MappingDetails.Type.WARN,
                )

        return context.build(path) {
            JourneyReferences
                .newBuilder()
                .setEntityDatedJourneyKeyV2Ref(entityDatedJourneyKeyV2Ref)
                .setExternalDatedJourneyId(externalDatedJourneyId)
                .build()
        }
    }

    private fun mapPlannedCall(
        input: DTODatedJourneyCall,
        path: String,
        context: MappingContext,
    ): DatedJourneyV2Call? {
        val ref =
            input.ref.notEmpty(
                context = context,
                path = "$path.plannedCallRef",
                insight = "missing.planned.call.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        val stopRef =
            input.stopPointRef.notEmpty(
                context = context,
                path = "$path.stopPointRef",
                insight = "missing.planned.call.stop.point.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        val stopPointBehaviourType =
            input.stopPointBehaviourType.toEntityValue(
                context = context,
                path = "$path.stopPointBehaviourType",
                insight = "call.behavior.type",
                values = STOP_POINT_BEHAVIOUR_TYPE,
                // TODO: Should this be stricter?
                errorType = MappingDetails.Type.WARN,
                unmappedPassThrough = true,
            )

        val originalStopPointBehaviourType =
            input.originalStopPointBehaviourType.toEntityValue(
                context = context,
                path = "$path.originalStopPointBehaviourType",
                insight = "call.original.behavior.type",
                values = STOP_POINT_BEHAVIOUR_TYPE,
                // TODO: Should this be stricter?
                errorType = MappingDetails.Type.WARN,
                unmappedPassThrough = true,
            )

        val display = context.map(input.destination, path = "$path.destination", this::mapDestinationDisplay)
        return context.build(path) {
            val builder =
                DatedJourneyV2Call
                    .newBuilder()
                    .setId(ref)
                    .setEntityDatedJourneyStopPointKeyV2Ref(stopRef)
                    .setDestinationDisplay(display)
                    .setBehaviourType(stopPointBehaviourType)
                    .setOriginalBehaviourType(originalStopPointBehaviourType)
                    .setPlannedArrivalDateTime(input.plannedArrival)
                    .setPlannedDepartureDateTime(input.plannedDeparture)
                    .setExpectedArrivalDateTime(input.expectedArrival)
                    .setExpectedDepartureDateTime(input.expectedDeparture)
                    .setPreviousDatedJourneyStopPointLinkKeyV2Ref(input.previousCallStopPointLinkRef)
                    .setNextDatedJourneyStopPointLinkKeyV2Ref(input.nextCallStopPointLinkRef)

            if (toggles.callOmitEnabled) {
                builder.omitted = input.omitted
            }
            if (toggles.callCancellationEnabled) {
                builder.cancelled = input.cancelled
            }

            if (toggles.callInterchangeEnabled) {
                val interchange = mapCallInterchange(input.interchange, "$path.interchange", context)
                builder.interchange = interchange
            }

            builder.build()
        }
    }

    private fun mapCallInterchange(
        input: DTODatedJourneyCallInterchange?,
        path: String,
        ctx: MappingContext,
    ): DatedJourneyV2CallInterchange? {
        if (input == null) {
            return null
        }
        val builder = DatedJourneyV2CallInterchange.newBuilder()
        val outputInterchangeFrom =
            input.from.mapIndexedNotNull { idx, it -> mapCallInterchangeFrom(it, "$path.from[$idx]", ctx) }
        builder.setFromSequence(outputInterchangeFrom)

        val outputInterchangeTo =
            input.to.mapIndexedNotNull { idx, it -> mapCallInterchangeTo(it, "$path.to[$idx]", ctx) }
        builder.setToSequence(outputInterchangeTo)

        return builder.build()
    }

    private fun mapCallInterchangeTo(
        input: DTODatedJourneyCallInterchangeTo?,
        path: String,
        ctx: MappingContext,
    ): DatedJourneyV2CallInterchangeTo? {
        if (input == null) {
            return null
        }
        val builder = DatedJourneyV2CallInterchangeTo.newBuilder()

        val intId =
            input.id.notEmpty(
                context = ctx,
                path = "$path.id",
                insight = "missing.planned.call.interchange.to.id",
                errorType = MappingDetails.Type.WARN,
            )
        val guaranteed =
            input.guaranteed.notNull(
                context = ctx,
                path = "$path.guaranteed",
                insight = "missing.planned.call.interchange.to.guaranteed",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )
        val staySeated =
            input.staySeated.notNull(
                context = ctx,
                path = "$path.staySeated",
                insight = "missing.planned.call.interchange.to.stay.seated",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )

        builder
            .setId(intId)
            .setGuaranteed(guaranteed ?: false)
            .setStaySeated(staySeated ?: false)
            .setReferences(
                mapCallInterchangeReferences(
                    input.references,
                    input.attributes,
                    "$path.references",
                    ctx,
                ),
            )

        return builder.build()
    }

    private fun mapCallInterchangeFrom(
        input: DTODatedJourneyCallInterchangeFrom?,
        path: String,
        ctx: MappingContext,
    ): DatedJourneyV2CallInterchangeFrom? {
        if (input == null) {
            return null
        }
        val builder = DatedJourneyV2CallInterchangeFrom.newBuilder()

        val intId =
            input.id.notEmpty(
                context = ctx,
                path = "$path.id",
                insight = "missing.planned.call.interchange.from.id",
                errorType = MappingDetails.Type.WARN,
            )
        val guaranteed =
            input.guaranteed.notNull(
                context = ctx,
                path = "$path.guaranteed",
                insight = "missing.planned.call.interchange.from.guaranteed",
                errorType = MappingDetails.Type.WARN,
                defaultValue = false,
            )

        builder
            .setId(intId)
            .setGuaranteed(guaranteed ?: false)
            .setMaximumWaitTime(input.maximumWaitTimeSec)
            .setReferences(
                mapCallInterchangeReferences(
                    input.references,
                    input.attributes,
                    "$path.references",
                    ctx,
                ),
            )

        return builder.build()
    }

    private fun mapCallInterchangeReferences(
        references: DTODatedJourneyCallInterchangeReferences?,
        attributes: DTODatedJourneyCallInterchangeAttributes?,
        path: String,
        ctx: MappingContext,
    ): DatedJourneyV2CallInterchangeReferences? {
        val builder = DatedJourneyV2CallInterchangeReferences.newBuilder()

        val mappedInput =
            references.notNull(
                context = ctx,
                path = path,
                insight = "missing.planned.call.interchange.references",
                errorType = MappingDetails.Type.WARN,
            )

        if (mappedInput == null) {
            return null
        }

        val externalInterchangeRef =
            mappedInput.externalInterchangeRef.notEmpty(
                context = ctx,
                path = "$path.externalInterchangeRef",
                insight = "missing.planned.call.interchange.references.external.interchange.ref",
                errorType = MappingDetails.Type.WARN,
            )

        val externalJourneyId =
            mappedInput.externalJourneyId.notEmpty(
                context = ctx,
                path = "$path.externalJourneyId",
                insight = "missing.planned.call.interchange.references.external.journey.id",
                errorType = MappingDetails.Type.WARN,
            )

        val operatingDate =
            (attributes?.operatingDate).toLocalDate(
                context = ctx,
                path = "$path.operatingDate",
                insight = "missing.planned.call.interchange.attributes.operating.date",
                errorType = MappingDetails.Type.WARN,
            )

        val externalDatedJourneyId =
            externalJourneyId
                ?.let { id ->
                    operatingDate?.toString()?.let { date ->
                        ExternalDatedJourneyId(
                            id,
                            date,
                        )
                    }
                }.notNull(
                    context = ctx,
                    path = "$path.externalDatedJourneyId",
                    insight = "not.able.to.construct.externalDatedJourneyId",
                    errorType = MappingDetails.Type.WARN,
                )

        val lineRef =
            mappedInput.lineRef.notEmpty(
                context = ctx,
                path = "$path.lineRef",
                insight = "missing.planned.call.interchange.references.line.ref",
                errorType = MappingDetails.Type.WARN,
            )

        val entityDatedJourneyV2Ref =
            mappedInput.entityDatedJourneyV2Ref.notEmpty(
                context = ctx,
                path = "$path.entityDatedJourneyV2Ref",
                insight = "missing.planned.call.interchange.references.entity.dated.journey.v2.ref",
                errorType = MappingDetails.Type.WARN,
            )
        val entityDatedJourneyV2CallRef =
            mappedInput.entityDatedJourneyV2CallRef.notEmpty(
                context = ctx,
                path = "$path.entityDatedJourneyV2CallRef",
                insight = "missing.planned.call.interchange.references.entity.dated.journey.v2.call.ref",
                errorType = MappingDetails.Type.WARN,
            )

        val quayRef =
            mappedInput.quayRef.notEmpty(
                context = ctx,
                path = "$path.quayRef",
                insight = "missing.planned.call.interchange.references.quay.ref",
                errorType = MappingDetails.Type.WARN,
            )

        val legacyQuayRef =
            mappedInput.legacyQuayRef.notEmpty(
                context = ctx,
                path = "$path.legacyQuayRef",
                insight = "missing.planned.call.interchange.references.legacy.quay.ref",
                errorType = MappingDetails.Type.WARN,
            )

        builder
            .setExternalDatedJourneyId(externalDatedJourneyId)
            .setExternalJourneyId(externalJourneyId)
            .setExternalRef(externalInterchangeRef)
            .setLineRef(lineRef)
            .setEntityDatedJourneyV2Ref(entityDatedJourneyV2Ref)
            .setEntityDatedJourneyV2CallRef(entityDatedJourneyV2CallRef)
            .setQuayRef(quayRef)
            .setLegacyQuayRef(legacyQuayRef)

        return builder.build()
    }

    private fun mapDestinationDisplay(
        input: DTODestination,
        path: String,
        context: MappingContext,
    ): DestinationDisplay? {
        // Note: We allow an empty text (blank display), but it can not be null!
        val text =
            input.text.notNull(
                context = context,
                path = "$path.text",
                insight = "missing.destination.text",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            ) // TODO: Should we downgrade this a warning and us a blank / empty fall-back text?
        return context.build(path) {
            DestinationDisplay
                .newBuilder()
                .setText(text)
                .setSubText(input.subText) // Sub-text is optional.
                .build()
        }
    }

    private fun mapLine(
        maybeInput: DTOLine?,
        path: String,
        context: MappingContext,
    ): Line? {
        val input =
            maybeInput.notNull(
                context = context,
                path = path,
                insight = "missing.line",
                errorType = MappingDetails.Type.INFO,
            ) ?: return null

        val ref =
            input.lineRef.notEmpty(
                context = context,
                path = "$path.lineRef",
                insight = "missing.line.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )

        val mode =
            input.transportMode.toEntityValue(
                context = context,
                path = "$path.transportMode",
                insight = "line.transport.mode",
                values = TRANSPORT_MODES,
                errorType = MappingDetails.Type.WARN,
                unmappedPassThrough = true,
            ) ?: DTOTransportMode.UNKNOWN.value()

        val properties =
            input.transportModeProperties?.mapIndexedNotNull { i, prop ->
                prop.toEntityValue(
                    context = context,
                    path = "$path.transportModeProperties[$i]",
                    insight = "line.transport.mode.properties",
                    values = TRANSPORT_MODE_PROPERTIES,
                    errorType = MappingDetails.Type.INFO,
                    unmappedPassThrough = true,
                ) ?: DTOTransportModeProperty.UNKNOWN.value()
            }

        return context.build(path) {
            Line
                .newBuilder()
                .setLineRef(ref)
                .setName(input.name)
                .setTransportMode(mode)
                .setTransportModeProperties(properties)
                .setPublicCode(input.publicCode)
                .setPrivateCode(input.privateCode)
                .setBackgroundColour(input.backgroundColour)
                .setTextColour(input.textColour)
                .build()
        }
    }

    private fun mapOperator(
        input: DTOOperator,
        path: String,
        context: MappingContext,
    ): Operator? {
        val ref =
            input.operatorRef.notEmpty(
                context = context,
                path = "$path.operatorRef",
                insight = "missing.operator.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        return context.build(path) {
            Operator
                .newBuilder()
                .setName(input.name) // Name is optional.
                .setOperatorRef(ref)
                .build()
        }
    }

    private fun mapOperatorContract(
        input: DTOOperatorContract,
        path: String,
        context: MappingContext,
    ): OperatorContract? {
        val ref =
            input.operatorContractRef.notEmpty(
                context = context,
                path = "$path.operatorContractRef",
                insight = "missing.operator.contract.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )

        val operatorPath = "$path.operator"
        val inputOperator =
            input.operator.notNull(
                context = context,
                path = operatorPath,
                insight = "missing.operator.contract.operator",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        val operator = context.map(inputOperator, operatorPath, this::mapOperator)
        return context.build(path) {
            OperatorContract
                .newBuilder()
                .setName(input.name) // Name is optional.
                .setOperator(operator)
                .setOperatorContractRef(ref)
                .build()
        }
    }

    private fun mapVehicle(
        input: DTOVehicle,
        path: String,
        context: MappingContext,
    ): Vehicle? {
        val ref =
            input.vehicleRef.notEmpty(
                context = context,
                path = "$path.vehicleRef",
                insight = "missing.vehicle.ref",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        return context.build(path) {
            Vehicle
                .newBuilder()
                .setVehicleRef(ref)
                .build()
        }
    }

    private fun mapLineage(
        input: DTOLineage,
        path: String,
        context: MappingContext,
    ): Lineage? {
        val type =
            input.type.toEntityValue(
                context = context,
                path = "$path.type",
                insight = "lineage.type",
                values = LINEAGE_TYPE,
                errorType = MappingDetails.Type.INFO,
                unmappedPassThrough = true,
            ) ?: DTOLineageType.UNKNOWN.value()

        val source =
            input.source.toEntityValue(
                context = context,
                path = "$path.type",
                insight = "lineage.originating.from",
                values = LINEAGE_SOURCE_TYPE,
                errorType = MappingDetails.Type.INFO,
                unmappedPassThrough = true,
            ) ?: DTOLineageOriginatingFrom.UNKNOWN.value()

        val value =
            input.value.notEmpty(
                context = context,
                path = "$path.value",
                insight = "missing.value",
                // Error, since value is required by Avro schema.
                errorType = MappingDetails.Type.ERROR,
            )
        return context.build(path) {
            Lineage
                .newBuilder()
                .setType(type)
                .setSource(source)
                .setValue(value)
                .build()
        }
    }

    private fun mapExternalJourneyId(
        journeyReferences: DTODatedJourneyReferences?,
        path: String,
        context: MappingContext,
    ): String? =
        if (toggles.useExternalJourneyRefV2) {
            journeyReferences?.externalJourneyRefV2.notEmpty(
                context = context,
                path = "$path.journeyReferences.externalJourneyRefV2",
                insight = "missing.journeyReferences.externalJourneyRefV2",
                errorType = MappingDetails.Type.WARN,
            )
        } else {
            journeyReferences?.externalJourneyRef.notEmpty(
                context = context,
                path = "$path.journeyReferences.externalJourneyRef",
                insight = "missing.journeyReferences.externalJourneyRef",
                errorType = MappingDetails.Type.WARN,
            )
        }

    fun createOutputMessage(
        journey: DTODatedJourney,
    ): OutputMessage<DTODatedJourney, DatedJourneyEntityInputContext, DatedJourneyKeyV2, DatedJourneyEntityOutputContext> {
        val now = timeService.now()

        // TODO: Why on earth are creating an input message to create an output message?
        val djInputContext =
            DatedJourneyEntityInputContext(
                channel = type.channels.output, // TODO: Pick correct output channel for correct version of output entity.
                journey = journey,
                received = now,
            )

        val inputMessage = InputMessage(djInputContext, journey.ref, journey, ok = true)
        // todo verify result
        val outputMessage = mapOutput(inputMessage)
        return outputMessage
    }
}

fun DTOMessageHeader?.toInputMetadata(timeService: TimeService): InputMetadata {
    val header = this
    val now = timeService.now()
    return InputMetadata().apply {
        this.messageTimestamp = header?.messageTimestamp?.toOffsetDateTime() ?: now
        this.receivedTimestamp = header?.receivedTimestamp?.toOffsetDateTime() ?: now
        this.expiresTimestamp = header?.expiresTimestamp?.toOffsetDateTime() ?: now
        this.traceId = header?.traceId
        this.publisherId = header?.publisherId
        this.ownerId = header?.ownerId
        this.originId = header?.originId
    }
}
