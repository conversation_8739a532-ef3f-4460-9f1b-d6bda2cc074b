package no.ruter.tranop.app.plan.stop.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

// TODO: Gather the impls of all these Publishing services
@Service
class StopPointEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointConfigProperties: StopPointConfigProperties,
    val repository: StopPointRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DatedJourneyStopPointKeyV2,
        SpecificAvroSerde<DatedJourneyStopPointKeyV2>,
    >(
        producerBinding = kafkaConfigService.stopPointOutputProducer,
        config = stopPointConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_KEY_V2_REF,
    ) {
    fun publish(
        key: String,
        input: DatedJourneyStopPointKeyV2?,
        currentRevision: Int,
    ): Int {
        var res = 0
        publishToKafka(key, input) { exception ->
            if (exception == null) {
                repository.markPublished(key, currentRevision)
                res++
            }
        }

        return res
    }
}
