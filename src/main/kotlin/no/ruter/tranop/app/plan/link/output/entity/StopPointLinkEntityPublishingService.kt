package no.ruter.tranop.app.plan.link.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

// TODO: Gather the impls of all these Publishing services
@Service
class StopPointLinkEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointLinkConfigProperties: StopPointLinkConfigProperties,
    private val repository: StopPointLinkRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DatedJourneyStopPointLinkKeyV2,
        SpecificAvroSerde<DatedJourneyStopPointLinkKeyV2>,
    >(
        producerBinding = kafkaConfigService.stopPointLinkOutputProducer,
        config = stopPointLinkConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_LINK_KEY_V2_REF,
    ) {
    fun publish(
        key: String,
        input: DatedJourneyStopPointLinkKeyV2?,
        currentRevision: Int,
    ): Int {
        var res = 0
        publishToKafka(key, input) { exception ->
            if (exception == null) {
                res += repository.markPublished(key, currentRevision) ?: 0
            }
        }

        return res
    }
}
