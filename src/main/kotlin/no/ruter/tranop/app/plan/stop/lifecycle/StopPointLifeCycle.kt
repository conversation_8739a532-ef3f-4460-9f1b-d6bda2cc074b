package no.ruter.tranop.app.plan.stop.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.stop.output.entity.StopPointEntityPublishRoutine
import no.ruter.tranop.app.plan.stop.output.entity.StopPointOutboxEntityV2PublishingRoutine
import no.ruter.tranop.app.plan.stop.output.internal.StopPointDTOPublishRoutine
import no.ruter.tranop.app.plan.stop.output.internal.StopPointOutboxInternalPublishingRoutine
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = StopPointLifeCycleConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class StopPointLifeCycle(
    val timeService: TimeService,
    publishStopPointRoutine: StopPointEntityPublishRoutine,
    publishStopPointDTORoutine: StopPointDTOPublishRoutine,
    stopPointOutboxEntityV2PublishingRoutine: StopPointOutboxEntityV2PublishingRoutine,
    stopPoiInternalOutboxPublishingRoutine: StopPointOutboxInternalPublishingRoutine,
) : AbstractLifeCycle(
        routines =
            listOf(
                publishStopPointRoutine,
                publishStopPointDTORoutine,
                stopPointOutboxEntityV2PublishingRoutine,
                stopPoiInternalOutboxPublishingRoutine,
            ),
    ) {
    companion object {
        const val FREQ_PREFIX = "${StopPointLifeCycleConfig.CONF_PREFIX}.frequent"
    }

    @Scheduled(
        fixedRateString = "\${$FREQ_PREFIX.fixedRate}",
        initialDelayString = "\${$FREQ_PREFIX.initialDelay}",
    )
    @SchedulerLock(
        name = "\${$FREQ_PREFIX.schedulerLockName}",
        lockAtMostFor = "\${$FREQ_PREFIX.lockAtMostFor}",
        lockAtLeastFor = "\${$FREQ_PREFIX.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }
}
