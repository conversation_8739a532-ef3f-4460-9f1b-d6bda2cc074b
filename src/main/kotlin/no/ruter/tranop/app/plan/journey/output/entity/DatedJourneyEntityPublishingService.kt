package no.ruter.tranop.app.plan.journey.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class DatedJourneyEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    datedJourneyConfigProperties: DatedJourneyConfigProperties,
    val datedJourneyService: DatedJourneyService,
    val datedJourneyOutputMapper: DatedJourneyEntityOutputMapper,
) : AbstractKafkaPublisherService<String?, Serde<String?>, DatedJourneyKeyV2, SpecificAvroSerde<DatedJourneyKeyV2>>(
        producerBinding = kafkaConfigService.datedJourneyOutputProducer,
        config = datedJourneyConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF,
    ) {
    fun publish(
        key: String,
        datedJourney: DatedJourneyKeyV2?,
        currentRevision: Int,
    ): Int {
        var counter = 0
        publishToKafka(key, datedJourney) { exception ->
            if (exception == null) {
                counter += datedJourneyService.markKafkaPublished(key, currentRevision) ?: 0
            }
        }

        return counter
    }

    fun publish(datedJourney: InternalDatedJourney) {
        val journey = datedJourney.data
        val revision = datedJourney.record.revision
        datedJourneyOutputMapper.createOutputMessage(journey).value?.let { message ->
            publish(message.entityHeader.key, message, revision)
        }
    }
}
