package no.ruter.tranop.app.plan.journey

import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyDeviationState
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyMitigationState
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyState
import java.time.OffsetDateTime

class JourneyUtils private constructor() {
    companion object {
        fun createJourneyState(
            omitted: Boolean? = null,
            cancelled: Boolean? = null,
        ): DTOJourneyState {
            val state = DTOJourneyState(DTOJourneyDeviationState(), DTOJourneyMitigationState())
            omitted?.let { state.journeyDeviationState.omitted = omitted }
            cancelled?.let { state.journeyMitigationState.cancelled = cancelled }
            return state
        }

        fun resolveArrivals(input: DTODatedJourney?): List<OffsetDateTime> =
            input?.plan?.calls?.mapNotNull { it?.plannedArrival?.toOffsetDateTime() } ?: emptyList()

        fun resolveDepartures(input: DTODatedJourney?): List<OffsetDateTime> =
            input?.plan?.calls?.mapNotNull { it?.plannedDeparture?.toOffsetDateTime() } ?: emptyList()
    }
}
