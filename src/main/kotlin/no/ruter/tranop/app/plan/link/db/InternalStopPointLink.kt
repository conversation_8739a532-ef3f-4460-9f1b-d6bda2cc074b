package no.ruter.tranop.app.plan.link.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointLinkTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink

class InternalStopPointLink(
    data: DTOStopPointLink,
    record: StopPointLinkRecord,
) : JSONRecordData<
        StopPointLinkTable,
        StopPointLinkRecord,
        DTOStopPointLink,
    >(RecordType.LINK, data, record) {
    override val dataRef: String?
        get() = data.ref
}
