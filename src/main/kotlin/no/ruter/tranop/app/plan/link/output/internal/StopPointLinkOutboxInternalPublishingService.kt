package no.ruter.tranop.app.plan.link.output.internal

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.output.internal.AssignmentJourneyConfigProperties
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointLinkSerde
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class StopPointLinkOutboxInternalPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    configProperties: StopPointLinkConfigProperties,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DTOStopPointLink,
        DTOStopPointLinkSerde,
    >(
        producerBinding = kafkaConfigService.stopPointLinkDtoOutputProducer,
        config = configProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_LINK_KEY_V2_REF,
    )
