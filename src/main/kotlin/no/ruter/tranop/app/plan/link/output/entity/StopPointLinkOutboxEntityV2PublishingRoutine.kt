package no.ruter.tranop.app.plan.link.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.plan.link.lifecycle.StopPointLinkLifeCycleConfig
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointLinkTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.journey.dated.dto.metadata
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Component

@Component
class StopPointLinkOutboxEntityV2PublishingRoutine(
    outboxService: OutboxService,
    val mapper: StopPointLinkEntityOutputMapper,
    val publisher: StopPointLinkOutboxEntityV2PublishingService,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    config: StopPointLinkLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        StopPointLinkTable,
        StopPointLinkRecord,
        DTOStopPointLink,
        DatedJourneyStopPointLinkKeyV2,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.LINK,
        targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
        payloadClass = DTOStopPointLink::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = config,
    ) {
    override fun map(
        data: DTOStopPointLink?,
        context: MappingContext,
    ): DatedJourneyStopPointLinkKeyV2? = mapper.mapValue(data, context)

    override fun publish(
        messages: List<OutboxOutputMessage<DatedJourneyStopPointLinkKeyV2>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        messages.forEach { msg ->
            val key = msg.outbox.data.payloadRef
            publisher.publishToKafka(key, msg.value, postProcessing)
        }
    }

    override fun metadata(data: DTOStopPointLink?): Map<String, Any?> = data?.metadata() ?: emptyMap()
}
