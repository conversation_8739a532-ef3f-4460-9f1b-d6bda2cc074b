package no.ruter.tranop.app.plan.stop.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord

class StopPointQueryBuilder :
    BaseRecordQueryBuilder<
        StopPointTable,
        StopPointRecord,
        BaseRecordTableMetadata<StopPointTable, StopPointRecord>,
        StopPointQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.STOP
    }
}
