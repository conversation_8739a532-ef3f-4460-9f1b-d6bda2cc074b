package no.ruter.tranop.app.plan.link.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2Data
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.output.entity.toInputMetadata
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import org.springframework.stereotype.Component

@Component
class StopPointLinkEntityOutputMapper(
    config: StopPointLinkConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTOStopPointLink,
        StopPointLinkInputContext,
        DatedJourneyStopPointLinkKeyV2,
        StopPointLinkOutputEntityContext,
    >(
        RecordType.LINK,
        config,
        timeService,
        insightService,
    ) {
    override fun getOwnerId(value: DatedJourneyStopPointLinkKeyV2?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTOStopPointLink, StopPointLinkInputContext>): StopPointLinkOutputEntityContext =
        StopPointLinkOutputEntityContext(
            input = input,
        )

    @Deprecated("Make use of the mapValue without the InputMessage pattern")
    override fun mapValue(
        input: InputMessage<DTOStopPointLink, StopPointLinkInputContext>,
        context: StopPointLinkOutputEntityContext,
    ): DatedJourneyStopPointLinkKeyV2? {
        val value = input.value!! // Tombstones never get here, so value is always non-null.
        return mapEntity(value, context)
    }

    private fun mapEntity(
        value: DTOStopPointLink,
        context: MappingContext,
    ): DatedJourneyStopPointLinkKeyV2? {
        val path: String = PATH_ROOT
        val link = mapStopPointLink(value, path, context)
        val events = context.mapList(value.events, PATH_ROOT_EVENTS, this::mapEvent)
        return context.build(path) {
            val data =
                DatedJourneyStopPointLinkKeyV2Data
                    .newBuilder()
                    .setLink(link)
                    .setEvents(events)
                    .build()

            val header =
                createEntityHeader(
                    inputMetadata = value.header.toInputMetadata(timeService),
                    key = value.ref,
                )
            DatedJourneyStopPointLinkKeyV2
                .newBuilder()
                .setEntityData(data)
                .setEntityHeader(header)
                .build()
        }
    }

    override fun mapValue(
        input: DTOStopPointLink?,
        context: MappingContext,
    ): DatedJourneyStopPointLinkKeyV2? = input?.let { mapEntity(input, context) }
}
