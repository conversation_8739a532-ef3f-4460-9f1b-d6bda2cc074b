package no.ruter.tranop.app.plan.link.output.internal

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import no.ruter.tranop.app.plan.link.lifecycle.StopPointLinkBaseRoutine
import no.ruter.tranop.app.plan.link.lifecycle.StopPointLinkLifeCycleConfig
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointLinkDTOPublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    stopPointLinkLifeCycleConfig: StopPointLinkLifeCycleConfig,
    private val repository: StopPointLinkRepository,
    private val publishingService: StopPointLinkDTOPublishingService,
) : StopPointLinkBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        stopPointLinkLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 256
        val skip = 0
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)

        try {
            generateSequence {
                repository.findDTOUnpublished(AbstractQueryBuilder.Pagination(batchSize, skip), seenFailedRefs, olderThanDateTime)
            }.takeWhile { it.isNotEmpty() }
                .forEach { stopPointLinks ->
                    stopPointLinks.forEach { internal ->
                        val currentRevision = internal.record.revision
                        val ref = internal.data.ref
                        val result = publishingService.publish(ref, internal.data, currentRevision)
                        if (result == 0) {
                            seenFailedRefs.add(ref)
                            recordInsight("failed")
                        } else {
                            totalPublished++
                            recordInsight("published")
                        }
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }
        return totalPublished
    }
}
