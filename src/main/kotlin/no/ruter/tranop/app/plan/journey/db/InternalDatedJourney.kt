package no.ruter.tranop.app.plan.journey.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import java.time.LocalDate
import java.time.OffsetDateTime

class InternalDatedJourney(
    data: DTODatedJourney,
    val firstDeparture: OffsetDateTime?,
    val firstArrival: OffsetDateTime?,
    val lastDeparture: OffsetDateTime?,
    val lastArrival: OffsetDateTime?,
    record: DatedJourneyRecord,
) : JSONRecordData<
        DatedJourneyTable,
        DatedJourneyRecord,
        DTODatedJourney,
    >(RecordType.DATED_JOURNEY, data, record) {
    // TODO: This should not be needed. Used for printing out status details atm.
    override fun toString(): String {
        val refs = data.journeyReferences
        return "DatedJourney(" +
            "vehicleTaskRef=${refs.vehicleTaskRef}, " +
            "operatingDate=$operatingDate, " +
            "firstDeparture=$firstDeparture, " +
            "firstArrival=$firstArrival, " +
            "lastDeparture=$lastDeparture, " +
            "lastArrival=$lastArrival, " +
            "type=${data.type}, " +
            "lineId=${data.line?.lineRef}"
    }

    override val dataRef: String?
        get() = data.ref

    val lineId: String?
        get() = record.lineId ?: data.line?.lineRef

    val datedBlockRef: String?
        get() = record.datedBlockRef

    val vehicleTaskRef: String?
        get() = record.vehicleTaskRef

    val operatingDate: LocalDate?
        get() = record.operatingDate

    val cancelled: Boolean
        get() = record.cancelled == true

    val partiallyCancelled: Boolean
        get() = record.partiallyCancelled == true

    val datedJourneyV2Ref: String?
        get() = ref

    val vehicleJourneyId: String?
        get() = record.vehicleJourneyId ?: data.journeyReferences?.vehicleJourneyId

    val datedServiceJourneyId: String?
        get() = record.datedServiceJourneyId
}
