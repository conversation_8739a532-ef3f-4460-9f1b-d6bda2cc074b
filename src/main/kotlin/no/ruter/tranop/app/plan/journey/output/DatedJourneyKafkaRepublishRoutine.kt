package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.OffsetDateTime

/**
 * Life-cycle routine for flagging dated journeys for re-publishing to Kafka.
 *
 * Note that this routine affects whats is published on two different Kafka topics:
 * 1. Entity topic
 * 2. Internal DTO topic
 *
 * TODO: Split re-publishing to different Kafka topics into separate routines?
 */
@Component
class DatedJourneyKafkaRepublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
    val datedJourneyRepo: DatedJourneyRepository,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_REPUBLISH,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    val chunkSize = 100
    val retryLimit = 2
    var retries = 0

    override fun execute(started: OffsetDateTime): Int {
        val operatingDate = started.plusDays(1).toLocalDate()
        try {
            makeOutboxes(operatingDate, started)
        } catch (e: Exception) {
            logger.warn("Unable to republish outboxes (retries: $retries / $retryLimit)", e)
            retries++
            makeOutboxes(operatingDate, started)
        }

        return datedJourneyRepo.republish(operatingDate)
    }

    private fun makeOutboxes(
        operatingDate: LocalDate,
        started: OffsetDateTime,
    ) {
        if (retries >= retryLimit) {
            logger.error("Gave up republishing after $retryLimit retries")
            return
        }

        val refs = datedJourneyRepo.fetchRefsForRepublishing(operatingDate, started)
        refs.chunked(chunkSize).forEach { chunk ->
            val journeys = datedJourneyRepo.fetchDatedJourneys(chunk)
            journeys.forEach { journey ->
                val record = journey.record
                datedJourneyRepo.addToOutbox(
                    ref = record.ref,
                    payload = record.jsonData,
                    tombstone = record.tombstonedAt == null,
                )
            }
        }
    }
}
