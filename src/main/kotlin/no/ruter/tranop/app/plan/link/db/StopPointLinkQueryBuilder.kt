package no.ruter.tranop.app.plan.link.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointLinkTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord

class StopPointLinkQueryBuilder :
    BaseRecordQueryBuilder<
        StopPointLinkTable,
        StopPointLinkRecord,
        BaseRecordTableMetadata<StopPointLinkTable, StopPointLinkRecord>,
        StopPointLinkQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.LINK
    }
}
