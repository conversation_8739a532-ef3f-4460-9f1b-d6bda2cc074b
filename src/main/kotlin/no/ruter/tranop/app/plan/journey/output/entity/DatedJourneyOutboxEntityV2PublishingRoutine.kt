package no.ruter.tranop.app.plan.journey.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.metadata
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Component

@Component
class DatedJourneyOutboxEntityV2PublishingRoutine(
    outboxService: OutboxService,
    val mapper: DatedJourneyEntityOutputMapper,
    val publisher: DatedJourneyOutboxEntityV2PublishingService,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    lifecycleConfig: DatedJourneyLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        DatedJourneyTable,
        DatedJourneyRecord,
        DTODatedJourney,
        DatedJourneyKeyV2,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.DATED_JOURNEY,
        targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
        payloadClass = DTODatedJourney::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = lifecycleConfig,
    ) {
    override fun map(
        data: DTODatedJourney?,
        context: MappingContext,
    ): DatedJourneyKeyV2? = mapper.mapValue(data, context)

    override fun publish(
        messages: List<OutboxOutputMessage<DatedJourneyKeyV2>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        messages.forEach { msg ->
            val key = msg.outbox.data.payloadRef
            publisher.publishToKafka(key, msg.value, postProcessing)
        }
    }

    override fun metadata(data: DTODatedJourney?): Map<String, Any?> = data?.metadata() ?: emptyMap()
}
