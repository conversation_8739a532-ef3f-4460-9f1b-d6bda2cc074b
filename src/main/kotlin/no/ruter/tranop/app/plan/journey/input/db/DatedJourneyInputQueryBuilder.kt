package no.ruter.tranop.app.plan.journey.input.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.db.DatedJourneyQueryBuilder
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyInputTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyInputRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import org.jooq.DSLContext
import org.jooq.SortField

class DatedJourneyInputQueryBuilder :
    BaseRecordQueryBuilder<
        DatedJourneyInputTable,
        DatedJourneyInputRecord,
        BaseRecordTableMetadata<DatedJourneyInputTable, DatedJourneyInputRecord>,
        DatedJourneyInputQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.DATED_JOURNEY_INPUT
    }
}
