package no.ruter.tranop.app.plan.journey.output

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.output.OutputMessage
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityInputContext
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityOutputContext
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityOutputMapper
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityPublishingService
import no.ruter.tranop.app.plan.journey.output.internal.AssignmentJourneyPublishingService
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

/**
 * Life-cycle routine for publishing (and re-publishing) dated journeys to Kafka.
 *
 * Note that this routine publishes to two different Kafka topics:
 * 1. Entity topic
 * 2. Internal DTO topic
 *
 * TODO: Split publishing to different Kafka topics into separate routines?
 */
@Component
class DatedJourneyKafkaPublishRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    private val datedJourneyService: DatedJourneyService,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
    val datedJourneyMapper: DatedJourneyEntityOutputMapper,
    val datedJourneyEntityPublishingService: DatedJourneyEntityPublishingService,
    val assignmentJourneyPublishingService: AssignmentJourneyPublishingService,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 256
        val skip = 0
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)

        try {
            generateSequence {
                datedJourneyService.findKafkaUnpublished(
                    AbstractQueryBuilder.Pagination(batchSize, skip),
                    seenFailedRefs,
                    olderThanDateTime,
                )
            }.takeWhile { it.isNotEmpty() }
                .forEach { journeysToPublish ->
                    journeysToPublish.forEach { internalJourney ->
                        publishJourney(internalJourney, seenFailedRefs)
                        totalPublished++
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }
        return totalPublished
    }

    private fun publishJourney(
        internalJourney: InternalDatedJourney,
        seenFailedRefs: MutableSet<String>,
    ) {
        val outputMessage = createOutputMessage(internalJourney.data)
        val currentRevision = internalJourney.record.revision
        val ref = internalJourney.data.ref
        if (!outputMessage.ok) {
            seenFailedRefs.add(outputMessage.key)
            return
        }
        outputMessage.value?.let {
            val kafkaPublishResult = datedJourneyEntityPublishingService.publish(ref, it, currentRevision)

            if (kafkaPublishResult == 0) {
                seenFailedRefs.add(ref)
            } else {
                datedJourneyService.markKafkaPublished(ref, currentRevision) ?: 0
            }
        }
        val dtoPublishResult = assignmentJourneyPublishingService.publish(ref, internalJourney.data, currentRevision)
        if (dtoPublishResult == 0) {
            seenFailedRefs.add(ref)
        } else {
            datedJourneyService.markKafkaPublished(ref, currentRevision) ?: 0
        }
    }

    fun createOutputMessage(
        journey: DTODatedJourney,
    ): OutputMessage<DTODatedJourney, DatedJourneyEntityInputContext, DatedJourneyKeyV2, DatedJourneyEntityOutputContext> =
        datedJourneyMapper.createOutputMessage(journey)
}
