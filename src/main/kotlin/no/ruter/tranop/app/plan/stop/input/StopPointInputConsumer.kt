package no.ruter.tranop.app.plan.stop.input

import no.ruter.plandata.journey.dated.v2.dto.kafka.PDJStopPointDeserializer
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaConsumer
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConsumerConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    name = [
        KafkaConsumerConfig.CONF_KEY_ENABLED,
        "${StopPointInputConsumer.CONF_PREFIX}.enabled",
    ],
    havingValue = "true",
)
class StopPointInputConsumer(
    private val stopPointInputService: StopPointInputService,
    insightService: InsightService,
) : AbstractKafkaConsumer(
        channel = stopPointInputService.defaultChannel,
        insightService = insightService,
    ) {
    companion object {
        const val CONF_PREFIX = "${StopPointConfigProperties.CONF_PREFIX}.input.kafka.dto.consumer"
    }

    private val deserializer = PDJStopPointDeserializer()

    @KafkaListener(
        topics = ["\${$CONF_PREFIX.topic}"],
        groupId = "\${$CONF_PREFIX.group}",
    )
    fun listenGroup(
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
        ack: Acknowledgment,
    ) {
        try {
            process(
                record = consumerRecord,
                deserializer = deserializer,
                handler = stopPointInputService::process,
            )
        } catch (e: Exception) {
            logger.error("Not able to process stop point [${consumerRecord.key()} / ${consumerRecord.headers()}]", e)
        } finally {
            ack.acknowledge()
        }
    }
}
