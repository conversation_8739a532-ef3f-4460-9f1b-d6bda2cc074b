package no.ruter.tranop.app.plan.journey.output.opensearch

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchProperties
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchService
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import org.opensearch.client.opensearch.core.BulkResponse
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.OffsetDateTime

@Component
class DatedJourneyOpenSearchPublishRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    val datedJourneyService: DatedJourneyService,
    datedJourneyOpenSearchLifeCycleConfig: DatedJourneyOpenSearchLifeCycleConfig,
    val openSearchService: OpenSearchService,
    val openSearchProperties: OpenSearchProperties,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        datedJourneyOpenSearchLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 64
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)
        val currentDate = LocalDate.now()
        val fromDate = currentDate.minusDays(openSearchProperties.publishDaysBeforeOperatingDate)
        val toDate = currentDate.plusDays(openSearchProperties.publishDaysAfterOperatingDate)
        try {
            generateSequence {
                val batch =
                    datedJourneyService.findOpenSearchUnpublished(
                        AbstractQueryBuilder.Pagination(batchSize, 0),
                        seenFailedRefs,
                        olderThanDateTime,
                        fromDate,
                        toDate,
                    )

                batch.ifEmpty { null }
            }.takeWhile { it.isNotEmpty() }
                .forEach { fetchedJourneyBatch ->
                    record(
                        type = "publishing",
                        journeyRefs = fetchedJourneyBatch.mapNotNull { it.datedJourneyV2Ref }.toSet(),
                    )
                    val publishResponse = publishJourneyBatch(fetchedJourneyBatch)

                    publishResponse.items().forEach { probablyPublishedItem ->
                        if (probablyPublishedItem.error() == null) {
                            probablyPublishedItem.id()?.let { publishedId ->
                                val revision = fetchedJourneyBatch.find { it.datedJourneyV2Ref == publishedId }?.record?.revision
                                if (revision == null) {
                                    val metadata =
                                        mapOf(
                                            LogKey.MESSAGE_SUBJECT to "output-opensearch-journey",
                                            LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF to publishedId,
                                            LogKey.MESSAGE_DIRECTION to DataChannel.Direction.OUT.value,
                                        )
                                    logger.error(
                                        "couldn't find revision for ${probablyPublishedItem.toJsonString()}",
                                        metadata = metadata,
                                    )
                                }
                                revision?.let { r ->
                                    datedJourneyService.markOpenSearchPublished(publishedId, r)
                                }
                            }
                            totalPublished++
                        } else {
                            val ref = probablyPublishedItem.id()
                            val error = probablyPublishedItem.error()
                            if (ref != null && error != null) {
                                seenFailedRefs.add(ref)
                                val metadata =
                                    mapOf(
                                        LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF to ref,
                                        LogKey.MESSAGE_SUBJECT to "output-opensearch-journey",
                                        LogKey.MESSAGE_DIRECTION to DataChannel.Direction.OUT.value,
                                    )
                                logger.warn(
                                    "couldn't publish $ref due to an error: ${error.toJsonString()}",
                                    metadata = metadata,
                                )
                            }
                        }
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }

        return totalPublished
    }

    private fun publishJourneyBatch(fetchedJourneyBatch: List<InternalDatedJourney>): BulkResponse {
        val osIndex = openSearchProperties.journeyIndexName
        return openSearchService.bulkIndexDocumentBatch(fetchedJourneyBatch.map { it.data }, osIndex) { doc -> doc.ref }
    }

    private fun publishJourney(
        internalJourney: InternalDatedJourney,
        seenFailedRefs: MutableSet<String>,
    ) {
        val ref = internalJourney.datedJourneyV2Ref ?: return
        val currentRevision = internalJourney.record.revision
        val journey = internalJourney.data
        val osIndex = openSearchProperties.journeyIndexName

        val openSearchIndexResult = openSearchService.indexDocument(journey, ref, osIndex)

        if (openSearchIndexResult.isFailure) {
            val exc = openSearchIndexResult.exceptionOrNull()
            val metadata =
                mapOf(
                    LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF to ref,
                )
            exc?.let {
                handleException(
                    e = exc as Exception,
                    metadata = metadata,
                )
            }

            seenFailedRefs.add(ref)
        } else {
            record(
                type = "marking-published",
                journeyRefs = setOfNotNull(internalJourney.datedJourneyV2Ref),
            )
            datedJourneyService.markOpenSearchPublished(ref, currentRevision) ?: 0
        }
    }
}
