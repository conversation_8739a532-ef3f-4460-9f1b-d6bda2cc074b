package no.ruter.tranop.app.plan.journey.output.opensearch

import no.ruter.rdp.logging.LogKey
import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchIngestException
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchProperties
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchService
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.springframework.stereotype.Service

@Service
class DatedJourneyOutboxOpenSearchPublishingService(
    val openSearchService: OpenSearchService,
    val openSearchProperties: OpenSearchProperties,
) {
    private val log: Logger = LoggerFactory.getLogger(javaClass)

    fun publishToOpenSearch(
        messages: List<OutboxOutputMessage<DTODatedJourney>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        try {
            publishJourneys(messages)
            postProcessing(null)
        } catch (e: OpenSearchIngestException) {
            val errors = e.errors
            val journeyRefs = errors.keys
            val metadata =
                mapOf(
                    LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF to journeyRefs,
                    "errors" to errors,
                )
            log.warn("Failed to publish journey batch", metadata, e)
            postProcessing(e)
        } catch (e: Exception) {
            postProcessing(e)
        }
    }

    private fun publishJourneys(messages: List<OutboxOutputMessage<DTODatedJourney>>) {
        val osIndex = openSearchProperties.journeyIndexName
        val (live, delete) = messages.partition { (_, _, value) -> value != null }

        if (live.isNotEmpty()) upsert(osIndex, live)
        if (delete.isNotEmpty()) delete(osIndex, delete)
    }

    private fun delete(
        osIndex: String,
        delete: List<OutboxOutputMessage<DTODatedJourney>>,
    ) {
        openSearchService.bulkDeleteDocumentFromIndex(
            index = osIndex,
            refs = delete.map { it.ref },
        )
    }

    private fun upsert(
        osIndex: String,
        live: List<OutboxOutputMessage<DTODatedJourney>>,
    ) {
        openSearchService.bulkIndexDocumentBatch(
            index = osIndex,
            input = live.map { it.value },
        ) { doc -> doc!!.ref }
    }
}
