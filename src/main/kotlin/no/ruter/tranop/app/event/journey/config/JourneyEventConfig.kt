package no.ruter.tranop.app.event.journey.config

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.config.ConfigToggle
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = JourneyEventConfig.CONF_PREFIX)
class JourneyEventConfig : AbstractSectionConfigProperties() {
    companion object {
        const val CONF_PREFIX = "app.config.journey-event"
    }

    lateinit var delay: DelayJourneyEventConfig
    lateinit var bypass: BypassJourneyEventConfig
    lateinit var omission: OmissionJourneyEventConfig
    lateinit var assignment: AssignmentEventConfig
    lateinit var cancellation: CancellationJourneyEventConfig
    lateinit var missingSignOn: MissingSignOnJourneyEventConfig
    lateinit var duplicateSignOn: DuplicateSignOnJourneyEventConfig
    lateinit var missingOperatorAction: MissingOperatorActionJourneyEventConfig
    lateinit var mitigation: MitigationJourneyEventConfig
    lateinit var operatingWithoutSignon: OperatingWithoutSignOnJourneyEventConfig
    lateinit var replacement: ReplacementJourneyEventConfig

    class DelayJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class BypassJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class OmissionJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class MitigationJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class AssignmentEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class CancellationJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class MissingSignOnJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class MissingOperatorActionJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class DuplicateSignOnJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class OperatingWithoutSignOnJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }

    class ReplacementJourneyEventConfig : ConfigToggle {
        override var enabled: Boolean = true
    }
}
