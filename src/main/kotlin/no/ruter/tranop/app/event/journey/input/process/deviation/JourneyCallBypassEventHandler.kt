package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class JourneyCallBypassEventHandler(
    config: JourneyEventConfig.BypassJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.bypass?.let { bypass ->
            val calls = bypass.calls
            val recalled = bypass.bypass == false

            val quayRefs = getCallQuayRefs(calls)
            val metadata =
                createBasicEventMetadata(event).apply {
                    append(DTOEventMetadataKeyType.QUAY_REFS, quayRefs)
                }

            val eventType = if (recalled) DTOEventType.CALL_BYPASS_RECALLED else DTOEventType.CALL_BYPASS
            createEvent(
                event = event,
                type = eventType,
                metadata = metadata,
                msg = "Bypass event",
                source = "journey-event/operational",
                context = context,
            )?.let {
                context.deviationResult(
                    input = event,
                    output = it,
                    code = DTOServiceDeviationCode.BYPASS,
                    includeDeviation = !recalled,
                )
            }
        }
}
