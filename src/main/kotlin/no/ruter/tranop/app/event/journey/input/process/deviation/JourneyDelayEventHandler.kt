package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventDelayReason
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventDelay
import java.time.OffsetDateTime

class JourneyDelayEventHandler(
    config: JourneyEventConfig.DelayJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val delayMetadataFields =
            mapOf<DTOEventMetadataKeyType, (DTOJourneyEventDelay) -> Any?>(
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_CASE_KEY_V2_REF to DTOJourneyEventDelay::getEntityTrafficCaseKeyV2Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF to DTOJourneyEventDelay::getEntityTrafficEventKeyV1Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF to DTOJourneyEventDelay::getEntityTrafficSituationKeyV2Ref,
                DTOEventMetadataKeyType.DELAY_MINUTES to DTOJourneyEventDelay::getDelayMinutes,
            )
    }

    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.delay?.let { delay ->
            val calls = delay.calls ?: emptyList()
            val partial = calls.isNotEmpty()

            val type =
                when (delay.reason) {
                    DTOJourneyEventDelayReason.DELAYED -> if (partial) DTOEventType.CALL_DELAY else DTOEventType.DELAYED
                    DTOJourneyEventDelayReason.UNDELAYED -> if (partial) DTOEventType.CALL_DELAY_RECALLED else DTOEventType.UNDELAYED
                    else -> DTOEventType.UNKNOWN
                }
            var quayRefs: Set<String>? = null
            val recalled = delay.reason == DTOJourneyEventDelayReason.UNDELAYED

            val delayMinutes = if (recalled) 0 else delay.delayMinutes ?: 0
            if (partial) {
                quayRefs = getCallQuayRefs(calls)
                val stopPoints = getStopPointMap(journey)
                journey.plan.calls.forEach { call ->
                    if (callMatches(call, stopPoints, calls)) {
                        updatedExpectedArrival(call, delayMinutes)
                        updateExpectedDeparture(call, delayMinutes)
                    }
                }
            } else {
                journey.plan.calls.forEach { call ->
                    updatedExpectedArrival(call, delayMinutes)
                    updateExpectedDeparture(call, delayMinutes)
                }
            }

            val metadata =
                collect(event, delayMetadataFields, delay).apply {
                    append(DTOEventMetadataKeyType.QUAY_REFS, quayRefs)
                }

            createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Delay event",
                source = "journey-event/operational",
                context = context,
            )?.let {
                context.deviationResult(
                    input = event,
                    output = it,
                    code = DTOServiceDeviationCode.DELAY,
                    includeDeviation = !recalled,
                )
            }
        }

    private fun updatedExpectedArrival(
        call: DTODatedJourneyCall?,
        delayMinutes: Int,
    ) {
        call?.plannedArrival?.toOffsetDateTime()?.let { time ->
            call.apply {
                expectedArrival = updateTime(delayMinutes, time)
            }
        }
    }

    private fun updateExpectedDeparture(
        call: DTODatedJourneyCall?,
        delayMinutes: Int,
    ) {
        call?.plannedDeparture?.toOffsetDateTime()?.let { time ->
            call.apply {
                expectedDeparture = updateTime(delayMinutes, time)
            }
        }
    }

    private fun updateTime(
        delayMinutes: Int,
        time: OffsetDateTime,
    ): String? =
        if (delayMinutes == 0) {
            null
        } else {
            time.plusMinutes(delayMinutes.toLong()).toUtcIsoString()
        }
}
