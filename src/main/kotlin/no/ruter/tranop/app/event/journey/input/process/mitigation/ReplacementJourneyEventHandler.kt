package no.ruter.tranop.app.event.journey.input.process.mitigation

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyRelatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReplacedBy
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class ReplacementJourneyEventHandler(
    config: JourneyEventConfig.ReplacementJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        event.replacement?.let { replacement ->
            val replaced = replacement.replaced == true
            if (replaced) {
                journey.replacedBy =
                    DTODatedJourneyReplacedBy(
                        replacement.replacementJourneyRefs.map {
                            DTODatedJourneyRelatedJourney().apply {
                                this.entityDatedJourneyKeyV2Ref = it // TODO sufficient with only entityDatedJourneyKeyV2Ref?
                            }
                        },
                    )
            } else {
                journey.replacedBy = null
            }

            val type = if (replaced) DTOEventType.REPLACED else DTOEventType.UNREPLACED
            val metadata = createBasicEventMetadata(event)
            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = event.status?.description,
                source = "journey-event/operational",
                context = context,
            )?.let {
                context.mitigationResult(
                    input = event,
                    output = it,
                    code = DTOServiceMitigationCode.REPLACEMENT_SERVICE,
                    includeMitigation = replaced,
                )
            }
        }

        return null
    }
}
