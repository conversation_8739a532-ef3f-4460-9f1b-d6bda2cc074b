package no.ruter.tranop.app.event.journey.input

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.insight.ErrorType
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.db.InternalJourneyEvent
import no.ruter.tranop.app.event.journey.db.JourneyEventRepository
import no.ruter.tranop.app.event.journey.input.process.deviation.JourneyCallBypassEventHandler
import no.ruter.tranop.app.event.journey.input.process.deviation.JourneyDelayEventHandler
import no.ruter.tranop.app.event.journey.input.process.deviation.OmitJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.deviation.OperatingWithoutSignOnJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.info.AssignmentJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.info.DuplicateSignOnJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.info.MissingOperatorActionJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.info.MissingSignOnJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.mitigation.JourneyCancellationEventHandler
import no.ruter.tranop.app.event.journey.input.process.mitigation.ReplacementJourneyEventHandler
import no.ruter.tranop.app.event.journey.input.process.mitigation.StandbyVehicleJourneyEventHandler
import no.ruter.tranop.app.event.journey.output.JourneyEventOutboxStreamingConfig
import no.ruter.tranop.app.event.journey.output.toBIJourneyEvent
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import org.jooq.exception.DataChangedException
import org.springframework.retry.support.RetryTemplateBuilder
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class JourneyEventInputService(
    private val config: JourneyEventConfig,
    private val journeyEventRepository: JourneyEventRepository,
    private val datedJourneyService: DatedJourneyService,
    private val timeService: TimeService,
    private val outboxConfig: JourneyEventOutboxStreamingConfig,
    private val outboxRepository: OutboxRepository,
) {
    private val log = LoggerFactory.getLogger(javaClass.canonicalName)

    val defaultChannel = journeyEventRepository.recordType.channels.input

    private val retryTemplate =
        RetryTemplateBuilder()
            // TODO: Make specific exception for this
            .retryOn(DataChangedException::class.java)
            .fixedBackoff(50)
            .maxAttempts(5)
            .build()

    val eventHandlers =
        listOf(
            // TODO: Order of handlers may(?) be important. Since we do not know for sure, we assume it is, for now.
            OmitJourneyEventHandler(config.omission),
            JourneyCancellationEventHandler(config.cancellation),
            AssignmentJourneyEventHandler(config.assignment),
            JourneyDelayEventHandler(config.delay),
            JourneyCallBypassEventHandler(config.bypass),
            MissingSignOnJourneyEventHandler(config.missingSignOn),
            MissingOperatorActionJourneyEventHandler(config.missingOperatorAction),
            DuplicateSignOnJourneyEventHandler(config.duplicateSignOn),
            StandbyVehicleJourneyEventHandler(config.mitigation),
            OperatingWithoutSignOnJourneyEventHandler(config.operatingWithoutSignon),
            ReplacementJourneyEventHandler(config.replacement),
        )

    val datedJourneyValidator = DatedJourneyInputValidator(timeService = timeService)

    fun store(
        now: OffsetDateTime?,
        event: DTOJourneyEvent,
        channel: DataChannel = defaultChannel,
        process: Boolean = true,
    ) {
        val context =
            JourneyEventInputContext(
                channel = channel,
                received = now ?: timeService.now(),
                internalEvent = InternalJourneyEvent(event = event),
            )
        store(context, process)
    }

    fun store(
        context: JourneyEventInputContext,
        process: Boolean = true,
    ) = store(listOf(context), process)

    fun store(
        contexts: List<JourneyEventInputContext>,
        process: Boolean = true,
    ) {
        contexts.forEach { context ->
            journeyEventRepository.store(context)
        }
        if (process) {
            process(contexts)
        }
    }

    fun processInput(
        key: String? = null,
        value: DTOJourneyEvent?,
        channel: DataChannel = defaultChannel,
    ): ProcessingContext {
        if (value == null) {
            return ProcessingContext.NOOPProcessingContext(channel)
        }

        val now = timeService.now()
        val internalJourneyEvent = InternalJourneyEvent(value)
        val context =
            JourneyEventInputContext(
                channel = channel,
                received = now,
                internalEvent = internalJourneyEvent,
            )
        if (!context.valid()) {
            return context
        }

        try {
            store(context, process = true)
        } catch (e: Exception) {
            context.addInternalException(
                e = e,
                log = log,
                type = ErrorType.DB_STORE,
                subject = channel.subject,
                error = false,
                metadata = mapOf(),
            )
        }
        return context
    }

    private fun process(contexts: List<JourneyEventInputContext>) {
        contexts.forEach { context ->
            try {
                retryTemplate.execute<DatedJourneyInputContext?, Exception> {
                    updateJourney(context)
                }
            } catch (e: Exception) {
                context.addInternalException(
                    e = e,
                    log = log,
                    type = ErrorType.DB_STORE,
                    subject = context.channel.name,
                    error = false,
                    metadata = context.metadata,
                )
            }
        }
    }

    private fun updateJourney(context: JourneyEventInputContext): DatedJourneyInputContext? {
        val datedJourney =
            datedJourneyService
                .fetchDatedJourneyByRef(context.datedJourneyV2Ref)

        val journey =
            datedJourney?.let {
                datedJourneyValidator.validate(
                    key = it.datedJourneyV2Ref,
                    input = it.data,
                )
            }

        return journey?.let {
            process(it)
            datedJourneyService.store(journey)
        }
    }

    fun process(context: DatedJourneyInputContext) {
        context.input?.let { journey ->
            applyJourneyEvents(journey, context)
        }
    }

    private fun applyJourneyEvents(
        journey: DTODatedJourney,
        context: ProcessingContext,
    ) {
        val events = mutableListOf<DTOEvent>()
        val context2 = JourneyEventHandlerContext(context)

        // Process all stored events by calling event handler(s) on all events.
        val storedEvents = journeyEventRepository.getAllForJourneyRef(journeyRef = journey.ref).map { it.event }
        storedEvents.forEach { event ->
            val id = event.id
            if (events.none { it.id == id }) {
                eventHandlers.forEach { handler ->
                    handler.handleJourneyEvent(context2, event, journey)?.let {
                        events.add(it)
                    }
                }
            }
        }

        // Add references to active deviations and mitigations to journey state.
        journey.journeyState?.let { state ->
            state.journeyDeviationState?.let { devState ->
                devState.deviations = context2.deviations.map { it.value }
            }
            state.journeyMitigationState?.let { mitState ->
                mitState.mitigations = context2.mitigations.map { it.value }
            }
        }

        events.map { it.type }.forEach {
            val value = it.value
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "applied.${value.lowercase()}",
                desc = "Applied journey event $value",
                field = "journey.event",
                value = value,
            )
        }

        journey.events?.let {
            it.forEach { event ->
                val id = event.id ?: MapperUtils.hashId(o = event, prefix = "jev-")
                event.id = id
                events.add(event)
            }
        }
        val sortedEvents = events.associateBy { it.id }.values.sortedBy { it.timestamp }
        journey.events =
            events
                .sortedBy { it.timestamp }
                .associateBy { it.id }
                .values
                .sortedBy { it.timestamp }
                .toMutableList()

        sortedEvents.lastOrNull()?.let { lastEvent ->
            journey.header?.apply {
                lastEvent.traceId?.let {
                    this.traceId = it
                }
                lastEvent.timestamp?.let {
                    this.messageTimestamp = it
                    this.receivedTimestamp = it
                }
            }
        }
    }
}
