package no.ruter.tranop.app.event.journey.input

import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyServiceVarianceReference
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class JourneyEventHandlerResult private constructor(
    val event: DTOEvent,
    val deviations: List<DTOJourneyServiceVarianceReference>? = null,
    val mitigations: List<DTOJourneyServiceVarianceReference>? = null,
) {
    companion object {
        fun eventResult(event: DTOEvent): JourneyEventHandlerResult = JourneyEventHandlerResult(event)

        fun deviationResult(
            input: DTOJourneyEvent,
            output: DTOEvent,
            code: DTOServiceDeviationCode,
            includeDeviation: Boolean,
        ): JourneyEventHandlerResult {
            val deviation =
                input.entityServiceDeviationKeyV1Ref?.let { ref ->
                    DTOJourneyServiceVarianceReference().apply {
                        this.ref = ref
                        this.code = code.value
                    }
                }

            return JourneyEventHandlerResult(
                event = output,
                deviations = if (includeDeviation) deviation?.let { listOf(deviation) } else null,
            )
        }

        fun mitigationResult(
            input: DTOJourneyEvent,
            output: DTOEvent,
            code: DTOServiceMitigationCode,
            includeMitigation: Boolean,
        ): JourneyEventHandlerResult {
            val mitigation =
                input.entityServiceMitigationKeyV1Ref?.let { ref ->
                    DTOJourneyServiceVarianceReference().apply {
                        this.ref = ref
                        this.code = code.value
                    }
                }

            return JourneyEventHandlerResult(
                event = output,
                mitigations = if (includeMitigation) mitigation?.let { listOf(mitigation) } else null,
            )
        }
    }
}
