package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaConsumer
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConsumerConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.event.traffic.config.TrafficEventConfig
import no.ruter.tranop.traffic.event.dto.kafka.DTOTrafficEventDeserializer
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    name = [
        KafkaConsumerConfig.CONF_KEY_ENABLED,
        "${TrafficEventInputConsumer.CONF_PREFIX}.enabled",
    ],
    havingValue = "true",
)
class TrafficEventInputConsumer(
    private val trafficEventService: TrafficEventInputService,
    insightService: InsightService,
) : AbstractKafkaConsumer(
        channel = DataChannel.TRAFFIC_EVENT_IN,
        insightService = insightService,
    ) {
    companion object {
        const val CONF_PREFIX = "${TrafficEventConfig.CONF_PREFIX}.input.kafka.dto.consumer"
    }

    private val deserializer = DTOTrafficEventDeserializer()

    @KafkaListener(
        topics = ["\${$CONF_PREFIX.topic}"],
        groupId = "\${$CONF_PREFIX.group}",
    )
    fun listenGroup(
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
        ack: Acknowledgment,
    ) {
        try {
            process(
                record = consumerRecord,
                deserializer = deserializer,
                handler = trafficEventService::processInput,
            )
        } catch (e: Exception) {
            logger.error("Not able to process traffic event [${consumerRecord.key()} / ${consumerRecord.headers()}]", e)
        } finally {
            ack.acknowledge()
        }
    }
}
