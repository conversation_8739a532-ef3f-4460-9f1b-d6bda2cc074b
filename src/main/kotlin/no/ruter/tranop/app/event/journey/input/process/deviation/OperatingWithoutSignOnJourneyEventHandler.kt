package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.OperatingWithoutSignOnJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class OperatingWithoutSignOnJourneyEventHandler(
    config: OperatingWithoutSignOnJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        val active = event.operatingWithoutSignon?.active ?: return null
        val deviationState = journey.journeyState.journeyDeviationState

        if (active == deviationState.operatingWithoutSignon) {
            return null // no change, no need to make another event.
        }

        deviationState.operatingWithoutSignon = active
        val type =
            if (active) {
                DTOEventType.OPERATING_WITHOUT_SIGN_ON
            } else {
                DTOEventType.OPERATING_WITHOUT_SIGN_ON_RECALLED
            }
        val metadata = createBasicEventMetadata(event)
        return createEvent(
            event = event,
            type = type,
            metadata = metadata,
            msg = "Operating without sign-on processed [$type / ${journey.ref} / $active]",
            source = "journey-event/operational",
            context = context,
        )?.let {
            context.deviationResult(
                input = event,
                output = it,
                code = DTOServiceDeviationCode.NO_SIGN_ON,
                includeDeviation = active,
            )
        }
    }
}
