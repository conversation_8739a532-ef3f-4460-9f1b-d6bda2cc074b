package no.ruter.tranop.app.event.journey.input.process

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.config.ConfigToggle
import no.ruter.tranop.app.common.time.TimeUtils
import no.ruter.tranop.app.event.journey.input.JourneyEventHandler
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadata
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.common.DTOJourneyEventCall
import java.time.OffsetDateTime

abstract class AbstractJourneyEventHandler(
    private val toggle: ConfigToggle,
) : JourneyEventHandler {
    fun <T> collect(
        event: DTOJourneyEvent,
        fields: Map<DTOEventMetadataKeyType, (T) -> Any?>,
        obj: T,
        includeDefaults: Boolean = true,
    ): ArrayList<DTOEventMetadata> =
        (if (includeDefaults) createBasicEventMetadata(event) else ArrayList()).apply {
            fields.forEach { (metadataKey, valueFunction) ->
                append(metadataKey, valueFunction(obj))
            }
        }

    fun eventMetadata(
        key: DTOEventMetadataKeyType,
        value: Any?,
        includeEmpty: Boolean = false,
    ): DTOEventMetadata? {
        val v =
            when (value) {
                is String -> value.trim()
                is OffsetDateTime -> value.toUtcIsoString()
                is Collection<*> -> value.joinToString(", ")
                else -> value?.toString()?.trim()
            }

        return if (includeEmpty || v?.isNotEmpty() == true) {
            DTOEventMetadata().apply {
                this.key = key
                this.value = v
            }
        } else {
            null
        }
    }

    fun MutableList<DTOEventMetadata>.append(
        key: DTOEventMetadataKeyType,
        value: Any?,
        includeEmpty: Boolean = false,
    ) = eventMetadata(key, value, includeEmpty)?.let(this::add)

    fun createBasicEventMetadata(event: DTOJourneyEvent): ArrayList<DTOEventMetadata> =
        ArrayList<DTOEventMetadata>().apply {
            append(DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF, event.entityServiceDeviationKeyV1Ref)
            append(DTOEventMetadataKeyType.ENTITY_SERVICE_MITIGATION_KEY_V1_REF, event.entityServiceMitigationKeyV1Ref)
        }

    fun createEvent(
        event: DTOJourneyEvent,
        type: DTOEventType?,
        metadata: List<DTOEventMetadata>,
        msg: String? = null,
        source: String? = null,
        context: ProcessingContext,
    ): DTOEvent? {
        if (!toggle.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "$type disabled",
            )
            return null
        }
        val header = event.header ?: null
        val status = event.status ?: null
        val descriptionParts =
            listOfNotNull(
                type?.value?.let { "[$it]" },
                msg ?: type?.description,
                status?.let { "${status.code}: ${status.description}" },
                source?.let { "[$it]" },
            )

        val description = descriptionParts.joinToString(" ")
        return DTOEvent().apply {
            this.id = event.id
            this.type = type
            this.traceId = header?.traceId
            this.timestamp = header?.messageTimestamp
            this.description = description
            this.metadata = metadata
            this.source = source ?: "assignment/internal"
        }
    }

    /** Returns true if provided call matches any call in event call list. **/
    protected fun callMatches(
        call: DTODatedJourneyCall,
        stopPoints: Map<String, DTOStopPoint>,
        eventCalls: List<DTOJourneyEventCall>,
    ): Boolean {
        val stopPoint = stopPoints[call.stopPointRef] ?: return false

        val arrival = TimeUtils.removeSeconds(call.plannedArrival?.toOffsetDateTime())
        val departure = TimeUtils.removeSeconds(call.plannedDeparture?.toOffsetDateTime())
        for (c in eventCalls) {
            if (c.nsrQuayId == stopPoint.quayRef) {
                TimeUtils.removeSeconds(c.time?.toOffsetDateTime())?.let { time ->
                    if (arrival?.isEqual(time) == true) return true
                    if (departure?.isEqual(time) == true) return true
                }
            }
        }
        return false
    }

    protected fun getStopPointMap(journey: DTODatedJourney): Map<String, DTOStopPoint> {
        val stops = journey.plan.stops ?: return emptyMap()
        return LinkedHashMap<String, DTOStopPoint>(stops.size).apply {
            stops.forEach { s -> s.ref?.let { put(it, s) } }
        }
    }

    protected fun getCallQuayRefs(calls: List<DTOJourneyEventCall>?): Set<String>? = calls?.mapNotNull { it.nsrQuayId }?.toSet()
}
