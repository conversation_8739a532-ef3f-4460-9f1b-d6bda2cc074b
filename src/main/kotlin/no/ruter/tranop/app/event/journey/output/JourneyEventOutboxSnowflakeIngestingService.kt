package no.ruter.tranop.app.event.journey.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTarget
import no.ruter.tranop.app.event.journey.input.JourneyEventType
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.bi.model.BIDatedJourneyV2JourneyEvent
import no.ruter.tranop.journey.event.dto.metadata
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@ConditionalOnProperty(prefix = JourneyEventOutboxStreamingConfig.CONF_PREFIX, name = ["enabled"], havingValue = "true")
@Service
class JourneyEventOutboxSnowflakeIngestingService(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<DTOJourneyEvent>(
        config,
        clientFactory,
        CLIENT_NAME,
        CHANNEL_NAME,
    ) {
    companion object {
        private const val PREFIX = "JOURNEY_EVENT_OUTBOX_SNOWFLAKE"
        const val CLIENT_NAME = "${PREFIX}_CLIENT"
        const val CHANNEL_NAME = "${PREFIX}_CHANNEL"
    }

    @Suppress("UNCHECKED_CAST")
    override fun DTOJourneyEvent.toIngestMap(): Map<String, Any> =
        BIDatedJourneyV2JourneyEvent(
            ref = this.id,
            type = getType(this).name,
            assignmentRef = this.assigned?.assignmentRef ?: this.unassigned?.assignmentRef,
            datedJourneyV2Ref = this.entityDatedJourneyKeyV2Ref,
            metadata = JsonUtils.toJsonNode(this.metadata()),
            createdAt = this.header?.messageTimestamp?.toOffsetDateTime(),
        ).toMap().filterValues { it != null } as Map<String, Any>

    fun getType(event: DTOJourneyEvent): JourneyEventType =
        with(event) {
            when {
                omission != null -> JourneyEventType.OMITTED
                assigned != null -> JourneyEventType.ASSIGNED
                unassigned != null -> JourneyEventType.UNASSIGNED
                updated != null -> JourneyEventType.UPDATED
                cancellation != null -> JourneyEventType.CANCELLATION
                delay != null -> JourneyEventType.DELAY
                bypass != null -> JourneyEventType.BYPASSED
                planned != null -> JourneyEventType.PLANNED
                missingSignOn != null -> JourneyEventType.MISSING_SIGN_ON
                missingOperatorAction != null -> JourneyEventType.MISSING_OPERATOR_ACTION
                duplicateSignOn != null -> JourneyEventType.DUPLICATE_SIGN_ON
                contingencyVehiclePlanned != null -> JourneyEventType.CONTINGENCY_VEHICLE_PLANNED
                operatingWithoutSignon != null -> JourneyEventType.OPERATING_WITHOUT_SIGNON
                replacement != null -> JourneyEventType.REPLACED
                else -> JourneyEventType.UNKNOWN
            }
        }
}
