package no.ruter.tranop.app.event.traffic.input

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.insight.ErrorType
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.db.InternalJourneyEvent
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.event.traffic.config.TrafficEventConfig
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.variance.common.api.adt.ADTAuthTrace
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.mitigation.api.ADTv4MitigationService
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindowOption
import no.ruter.tranop.assignment.adt.v4.model.APIMetadataEntry
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIMetadataKey
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceMitigationCode
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventCancellationReason
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventDelayReason
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventContingencyVehiclePlanned
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventDelay
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOperatingWithoutSignon
import no.ruter.tranop.journey.event.dto.model.common.DTOJourneyEventCall
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import org.springframework.stereotype.Component

@Component
class TrafficEventInputService(
    private val journeyEventService: JourneyEventInputService,
    private val timeService: TimeService,
    private val trafficEventConfig: TrafficEventConfig,
    private val datedJourneyService: DatedJourneyService,
    private val mitigationService: ADTv4MitigationService,
) {
    val delayConfig get() = trafficEventConfig.delay
    val cancellationConfig get() = trafficEventConfig.cancellation
    val mitigationConfig get() = trafficEventConfig.mitigation
    val operatingWithoutSignonConfig get() = trafficEventConfig.operatingWithoutSignon

    val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    fun processInput(
        key: String?,
        value: DTOTrafficEvent?,
    ): ProcessingContext {
        val now = timeService.now()
        val context = TrafficEventInputContext(traceId = value?.header?.traceId)
        val channel = context.channel

        try {
            value?.toInternalJourneyEvent(context)?.let { event ->
                if (context.valid()) {
                    journeyEventService.store(
                        listOf(
                            JourneyEventInputContext(
                                channel = channel,
                                received = now,
                                internalEvent = event,
                            ),
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            context.addInternalException(
                e = e,
                log = log,
                type = ErrorType.DB_STORE,
                subject = channel.subject,
                error = false,
                metadata = mapOf(),
            )
        }
        return context
    }

    private fun DTOTrafficEvent.toInternalJourneyEvent(context: TrafficEventInputContext): InternalJourneyEvent? {
        val trafficEvent = this

        val entityDatedJourneyKeyV2Ref =
            context.validateNotNull(
                trafficEvent.journeyRefs?.entityDatedJourneyKeyV2Ref,
                "traffic.event.journeyRefs.entityDatedJourneyKeyV2Ref",
                error = true,
            ) ?: return null

        val delay = handleDelay(context = context, trafficEvent = trafficEvent)
        val cancellation = handleCancellation(context = context, trafficEvent = trafficEvent)
        val contingencyVehiclePlanned = handleContingencyVehiclePlanned(context = context, trafficEvent = trafficEvent)
        val operatingWithoutSignon = handleOperatingWithoutSignon(context = context, trafficEvent = trafficEvent)
        handleOrganizedRailReplacementVehicles(context = context, trafficEvent = trafficEvent)

        val event =
            DTOJourneyEvent().apply {
                this.header = trafficEvent.header.toDTOMessageHeader()
                this.source = this.header.originId
                this.entityDatedJourneyKeyV2Ref = entityDatedJourneyKeyV2Ref
                this.delay = delay
                this.cancellation = cancellation
                this.contingencyVehiclePlanned = contingencyVehiclePlanned
                this.operatingWithoutSignon = operatingWithoutSignon
            }
        return InternalJourneyEvent(
            event = event,
        )
    }

    private fun handleOperatingWithoutSignon(
        context: TrafficEventInputContext,
        trafficEvent: DTOTrafficEvent,
    ): DTOJourneyEventOperatingWithoutSignon? {
        val operatingWithoutSignon = trafficEvent.operatingWithoutSignon ?: return null

        if (!operatingWithoutSignonConfig.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "Traffic event OPERATING_WITHOUT_SIGNON disabled",
            )
            return null
        }

        return DTOJourneyEventOperatingWithoutSignon().apply {
            this.active = operatingWithoutSignon.active
        }
    }

    private fun handleContingencyVehiclePlanned(
        context: TrafficEventInputContext,
        trafficEvent: DTOTrafficEvent,
    ): DTOJourneyEventContingencyVehiclePlanned? {
        val contingencyVehiclePlanned = trafficEvent.contingencyVehiclePlanned ?: return null

        if (!mitigationConfig.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "Traffic event MITIGATION disabled",
            )
            return null
        }

        return DTOJourneyEventContingencyVehiclePlanned().apply {
            this.planned = contingencyVehiclePlanned.planned
            this.operatorRefs = contingencyVehiclePlanned.operatorRefs ?: emptyList()
            this.standbyVehicleId = contingencyVehiclePlanned.standbyVehicleId
            this.vehicleRef = contingencyVehiclePlanned.vehicleRef
        }
    }

    private fun handleOrganizedRailReplacementVehicles(
        context: TrafficEventInputContext,
        trafficEvent: DTOTrafficEvent,
    ) {
        val organizedRailReplacementVehicles = trafficEvent.organizedRailReplacementVehicles ?: return

        if (!mitigationConfig.enabled || !trafficEventConfig.organizedRailReplacementVehicles.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "Traffic event MITIGATION disabled",
            )
            return
        }

        val datedJourney =
            datedJourneyService
                .fetchDatedJourneyByRef(trafficEvent.journeyRefs.entityDatedJourneyKeyV2Ref)

        if (datedJourney == null) {
            context.validateFailed(
                "traffic.event.journeyRefs.entityDatedJourneyKeyV2Ref",
                field = "entityDatedJourneyKeyV2Ref",
                error = true,
                desc = "No dated journey found for entityDatedJourneyKeyV2Ref " + trafficEvent.journeyRefs.entityDatedJourneyKeyV2Ref,
            )
            return
        }

        if (organizedRailReplacementVehicles.planned) {
            val spec = createAPIServiceMitigationSpec(trafficEvent, datedJourney)

            mitigationService.createServiceMitigation(
                ADTv4InputContext(
                    now = timeService.now(),
                    requestTrace =
                        ADTAuthTrace(
                            xADTAuth = null,
                            xTraceId = trafficEvent.header?.traceId,
                            xRequestId = null,
                            xOperatorId = null,
                            xAuthorityId = null,
                        ),
                    channel = DataChannel.TRAFFIC_EVENT_IN,
                    request = APIServiceMitigationPostRequest(spec, action = APIPostRequestType.CREATE.value),
                ),
            )
        } else {
            val spec = createAPIServiceMitigationSpec(trafficEvent, datedJourney)
            val serviceMitigationId = findServiceMitigationId(datedJourney)

            mitigationService.deleteServiceMitigation(
                ADTv4InputContext(
                    now = timeService.now(),
                    requestTrace =
                        ADTAuthTrace(
                            xADTAuth = null,
                            xTraceId = trafficEvent.header?.traceId,
                            xRequestId = null,
                            xOperatorId = null,
                            xAuthorityId = null,
                        ),
                    channel = DataChannel.TRAFFIC_EVENT_IN,
                    request = APIServiceMitigationPostRequest(spec, action = APIPostRequestType.DELETE.value),
                ),
                serviceMitigationId ?: "",
            )
        }
    }

    private fun createAPIServiceMitigationSpec(
        trafficEvent: DTOTrafficEvent,
        datedJourney: InternalDatedJourney,
    ): APIServiceMitigationSpec =
        APIServiceMitigationSpec(
            code = APIServiceMitigationCode.REPLACEMENT_SERVICE.value,
            impact =
                APIServiceImpact(
                    journeys =
                        listOf(
                            APIJourneySpecWindowOption(
                                journey =
                                    APIJourneySpecWindow(
                                        spec =
                                            APIJourneySpec(
                                                lineId = datedJourney.lineId,
                                                journeyId = datedJourney.datedServiceJourneyId,
                                            ),
                                        serviceWindow = null,
                                    ),
                            ),
                        ),
                ),
            metadata =
                listOf(
                    APIMetadataEntry(
                        APIMetadataKey.PTA_CASE_REF.value,
                        trafficEvent.trafficRefs?.caseRefs?.entityTrafficCaseKeyV2Ref,
                    ),
                ),
        )

    private fun handleDelay(
        context: TrafficEventInputContext,
        trafficEvent: DTOTrafficEvent,
    ): DTOJourneyEventDelay? {
        val delay = trafficEvent.delay ?: return null

        if (!delayConfig.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "Traffic event DELAY disabled",
            )
            return null
        }

        val delayMinutes =
            context.validateInt(
                value = delay.delayMinutes,
                field = "delay.delayMinutes",
                required = true,
            )

        val reason =
            if (delayMinutes != null && delayMinutes != 0) {
                DTOJourneyEventDelayReason.DELAYED
            } else {
                DTOJourneyEventDelayReason.UNDELAYED
            }
        val trafficRefs = trafficEvent.trafficRefs
        return DTOJourneyEventDelay().apply {
            this.reason = reason
            this.delayMinutes = delayMinutes
            this.entityTrafficCaseKeyV2Ref = trafficRefs?.caseRefs?.entityTrafficCaseKeyV2Ref
            this.entityTrafficEventKeyV1Ref = trafficRefs?.eventRefs?.entityTrafficEventKeyV1Ref
            this.entityTrafficSituationKeyV2Ref = trafficRefs?.situationRefs?.entityTrafficSituationKeyV2Ref
        }
    }

    private fun handleCancellation(
        context: TrafficEventInputContext,
        trafficEvent: DTOTrafficEvent,
    ): DTOJourneyEventCancellation? {
        val cancellation = trafficEvent.cancellation ?: return null
        if (!cancellationConfig.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "Traffic event CANCELLATION disabled",
            )
            return null
        }

        val cancelled = cancellation.cancelled ?: null
        val partial = cancellation.partial ?: null

        val calls = trafficEvent.cancellation?.calls
        val quayRefs = trafficEvent.cancellation?.quayRefs

        if (partial != null && partial && calls.isNullOrEmpty()) {
            context.validateFailed(
                value = "traffic.event.cancellation.partial",
                field = partial.toString(),
                error = true,
                desc = "Partial cancellation specified, but no calls or stopPlaceOrQuayRefs provided",
            )
            return null
        }

        val reason =
            if (cancelled == true) {
                if (partial == true) {
                    DTOJourneyEventCancellationReason.CALLS_CANCELLED
                } else {
                    DTOJourneyEventCancellationReason.CANCELLED
                }
            } else {
                if (partial == true) {
                    DTOJourneyEventCancellationReason.CALLS_UNCANCELLED
                } else {
                    DTOJourneyEventCancellationReason.UNCANCELLED
                }
            }

        val trafficRefs = trafficEvent.trafficRefs
        return DTOJourneyEventCancellation().apply {
            this.cancelled = cancelled
            this.partial = partial
            this.quayRefs = quayRefs
            this.calls =
                calls?.map { c ->
                    DTOJourneyEventCall().apply {
                        this.time = c.departureDateTime
                        this.nsrQuayId = c.quayRef
                        additionalProperties.forEach { (k, v) -> setAdditionalProperty(k, v) }
                    }
                }
            this.reason = reason
            this.entityTrafficCaseKeyV2Ref = trafficRefs?.caseRefs?.entityTrafficCaseKeyV2Ref
            this.entityTrafficEventKeyV1Ref = trafficRefs?.eventRefs?.entityTrafficEventKeyV1Ref
            this.entityTrafficSituationKeyV2Ref = trafficRefs?.situationRefs?.entityTrafficSituationKeyV2Ref
        }
    }

    private fun findServiceMitigationId(datedJourney: InternalDatedJourney): String? =
        datedJourney.data.journeyState.journeyMitigationState.mitigations
            .lastOrNull { it.code == DTOServiceMitigationCode.REPLACEMENT_SERVICE.value }
            ?.ref
}

private fun DTOMessageHeader?.toDTOMessageHeader(): DTOMessageHeader {
    val input = this
    return DTOMessageHeader()
        .apply {
            // TODO: Fill in the blanks
            this.traceId = input?.traceId
            this.ownerId = input?.ownerId
            this.originId = input?.originId
            this.publisherId = input?.publisherId

            this.expiresTimestamp = input?.expiresTimestamp
            this.publishedTimestamp = input?.publishedTimestamp
            this.messageTimestamp = input?.messageTimestamp
            this.receivedTimestamp = input?.receivedTimestamp
        }
}
