package no.ruter.tranop.app.event.journey.output

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.event.journey.lifecycle.JourneyEventLifeCycleConfig
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.assignmentmanager.db.sql.tables.JourneyEventTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.JourneyEventRecord
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component

@ConditionalOnProperty(prefix = JourneyEventOutboxStreamingConfig.CONF_PREFIX, name = ["enabled"], havingValue = "true")
@Component
class JourneyEventOutboxSnowflakeIngestingRoutine(
    outboxService: OutboxService,
    val publisher: JourneyEventOutboxSnowflakeIngestingService,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    lifeCycleConfig: JourneyEventLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        JourneyEventTable,
        JourneyEventRecord,
        DTOJourneyEvent,
        DTOJourneyEvent,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.DATED_JOURNEY_EVENT,
        targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
        payloadClass = DTOJourneyEvent::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = lifeCycleConfig,
    ) {
    // TODO: Mapping is performed in the ingest client JourneyEventOutboxSnowflakeIngestingService. Make mappers and move the logic here
    override fun map(
        data: DTOJourneyEvent?,
        context: MappingContext,
    ): DTOJourneyEvent? = data

    override fun publish(
        messages: List<OutboxOutputMessage<DTOJourneyEvent>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        try {
            val events = messages.map { Pair(it.ref, it.value) }
            publisher.ingest(events, throwExceptionOnError = true)
            postProcessing(null)
        } catch (e: Exception) {
            postProcessing(e)
        }
    }

    override fun metadata(data: DTOJourneyEvent?): Map<String, Any?> = emptyMap()
}
