package no.ruter.tranop.app.event.journey.db

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.assignmentmanager.db.sql.tables.JourneyEventTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.JourneyEventRecord
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class JourneyEventRepository(
    dslContext: DSLContext,
    appInfoProperties: AppInfoProperties,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        JourneyEventTable,
        JourneyEventRecord,
        InternalJourneyEvent,
        JourneyEventRecordMapper,
        JourneyEventQueryBuilder,
        BaseRecordTableMetadata<JourneyEventTable, JourneyEventRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = JourneyEventRecordMapper(appInfoProperties, timeService),
    ) {
    companion object {
        val TYPE = JourneyEventQueryBuilder.TYPE
        private val TABLE = TYPE.table

        val ORDER_FIELDS =
            listOf(
                TABLE.ID.asc(),
            )

        private val published =
            TABLE.PUBLISHED_REVISION.isNotNull
                .and(TABLE.REVISION.isNotNull)
                .and(TABLE.REVISION.eq(TABLE.PUBLISHED_REVISION))
    }

    override fun queryBuilder() = JourneyEventQueryBuilder()

    fun store(context: JourneyEventInputContext): Int {
        val event = context.internalEvent
        val ref: String = context.ref
        val existing = fetchByRef(ref)
        if (existing != null) {
            context.validateFailed(
                context.ref,
                field = "journey-event",
                error = true,
                desc = "The journey event is already processed",
            )
            return 0
        } else {
            val record = dslContext.newRecord(table)
            recordMapper.update(record, event.event, context)
            addToOutbox(
                ref = ref,
                payload = record.jsonData,
                tombstone = false,
            )
            return record.store()
        }
    }

    // TODO: Rename this to correspond with actual deletion criteria.
    // TODO: Find a more suitable logic than published-state to decide deletion (events are never published).
    fun deleteUnpublished(olderThan: Duration): Int =
        delete(
            published.and(oldEnoughGiven(olderThan = olderThan)),
        )

    fun deleteByDatedJourneyV2Refs(journeyRefs: List<String>) = delete(table.DATED_JOURNEY_V2_REF.`in`(journeyRefs))

    private fun oldEnoughGiven(olderThan: Duration) = TABLE.MODIFIED_AT.lessThan(timeService.now().minus(olderThan))

    fun getAllForJourneyRef(journeyRef: String?): List<InternalJourneyEvent> =
        journeyRef?.let {
            val cond = TABLE.DATED_JOURNEY_V2_REF.isNotNull.and(TABLE.DATED_JOURNEY_V2_REF.equal(journeyRef))
            fetch(cond)
        } ?: emptyList()
}
