package no.ruter.tranop.app.event.journey.input.process.info

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.DuplicateSignOnJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadata
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class DuplicateSignOnJourneyEventHandler(
    config: DuplicateSignOnJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.duplicateSignOn?.let { duplicateSignOn ->
            val type =
                if (duplicateSignOn.resolved != null && duplicateSignOn.resolved) {
                    DTOEventType.DUPLICATE_SIGN_ON_RESOLVED
                } else {
                    DTOEventType.DUPLICATE_SIGN_ON
                }

            val meta =
                duplicateSignOn.assignedEvents.flatMap {
                    listOf(
                        DTOEventMetadata().apply {
                            this.key = DTOEventMetadataKeyType.ASSIGNMENT_REF
                            this.value = it.assignmentRef
                        },
                        DTOEventMetadata().apply {
                            this.key = DTOEventMetadataKeyType.VEHICLE_REF
                            this.value = it.vehicleRef
                        },
                    )
                }

            createEvent(
                event,
                type,
                meta,
                context = context,
            )
        }
}
