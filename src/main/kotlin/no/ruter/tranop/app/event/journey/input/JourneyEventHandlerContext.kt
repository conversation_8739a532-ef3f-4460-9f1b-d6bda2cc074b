package no.ruter.tranop.app.event.journey.input

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyServiceVarianceReference
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class JourneyEventHandlerContext(
    val parent: ProcessingContext,
) : ProcessingContext() {
    override val channel: DataChannel
        get() = parent.channel
    override val traceId: String?
        get() = parent.traceId

    val deviations = LinkedHashMap<String, DTOJourneyServiceVarianceReference>()
    val mitigations = LinkedHashMap<String, DTOJourneyServiceVarianceReference>()

    fun deviationResult(
        input: DTOJourneyEvent,
        output: DTOEvent,
        code: DTOServiceDeviationCode,
        includeDeviation: Boolean,
    ): DTOEvent {
        val deviation =
            input.entityServiceDeviationKeyV1Ref?.let { ref ->
                DTOJourneyServiceVarianceReference().apply {
                    this.ref = ref
                    this.code = code.value
                }
            }

        if (includeDeviation) {
            deviation?.let { deviations.put(it.ref, it) }
        } else {
            deviation?.let { deviations.remove(it.ref) }
        }
        return output
    }

    fun mitigationResult(
        input: DTOJourneyEvent,
        output: DTOEvent,
        code: DTOServiceMitigationCode,
        includeMitigation: Boolean,
    ): DTOEvent {
        val mitigation =
            input.entityServiceMitigationKeyV1Ref?.let { ref ->
                DTOJourneyServiceVarianceReference().apply {
                    this.ref = ref
                    this.code = code.value
                }
            }

        if (includeMitigation) {
            mitigation?.let { mitigations.put(it.ref, it) }
        } else {
            mitigation?.let { mitigations.remove(it.ref) }
        }
        return output
    }
}
