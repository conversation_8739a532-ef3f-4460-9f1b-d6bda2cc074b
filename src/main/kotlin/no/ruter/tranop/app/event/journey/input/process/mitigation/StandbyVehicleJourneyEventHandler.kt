package no.ruter.tranop.app.event.journey.input.process.mitigation

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.app.plan.journey.doesNotContain
import no.ruter.tranop.assignment.dto.util.DTOVehicleUtil
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.dated.journey.dto.model.common.DTOVehicle
import no.ruter.tranop.dated.journey.dto.model.common.state.DTOJourneyMitigationState
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventContingencyVehiclePlanned

class StandbyVehicleJourneyEventHandler(
    config: JourneyEventConfig.MitigationJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        const val CONTINGENCY_OPERATOR_NAME = "BEREDSKAPSBUSS"
    }

    private val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        val journeyState = journey.journeyState

        event.contingencyVehiclePlanned?.let { standbyVehiclePlanned ->
            if (journeyState == null) {
                log.warn("Journey state is null for journey ref: ${journey.ref}")
                return null
            }

            val journeyMitigationState = journeyState.journeyMitigationState

            val planned = standbyVehiclePlanned.planned
            val recalled = planned == false
            if (!hasTrafficEventChanges(journeyMitigationState, journey, standbyVehiclePlanned)) {
                return null
            }

            journeyMitigationState.contingencyVehiclePlanned = planned

            // TODO: Remove operator from list when deviation is recalled?
            // TODO: If we remove operator, how do we ensure original operator still has access?
            val journeyOperators = journey.operators
            standbyVehiclePlanned.operatorRefs.forEach { operatorRef ->
                if (journeyOperators.doesNotContain(operatorRef)) {
                    // TODO: Resolve proper operator name instead of using same hard-coded name for all operators.
                    journeyOperators.add(
                        DTOOperator()
                            .withOperatorRef(operatorRef)
                            .withName(CONTINGENCY_OPERATOR_NAME),
                    )
                }
            }

            val vehicles = journey.vehicles
            val standbyVehicleId = standbyVehiclePlanned.standbyVehicleId
            val vehicleRef = validateVehicleRefLength(standbyVehiclePlanned.vehicleRef)
            if (standbyVehicleId != null) {
                vehicles.removeIf { v ->
                    v.standbyVehicleId == standbyVehicleId
                }
                vehicles.add(
                    DTOVehicle().apply {
                        this.standbyVehicleId = standbyVehicleId
                        this.vehicleRef = vehicleRef ?: DTOVehicleUtil.createVehicleRef(standbyVehicleId)
                    },
                )
            }

            val type =
                if (recalled) {
                    DTOEventType.CONTINGENCY_VEHICLE_RECALLED
                } else {
                    DTOEventType.CONTINGENCY_VEHICLE_PLANNED
                }
            val metadata = createBasicEventMetadata(event)

            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Mitigation processed [$type / ${journey.ref}]",
                source = "journey-event/operational",
                context = context,
            )?.let {
                context.mitigationResult(
                    input = event,
                    output = it,
                    code = DTOServiceMitigationCode.STANDBY_VEHICLE_PLANNED,
                    includeMitigation = !recalled,
                )
            }
        }

        return null
    }

    private fun validateVehicleRefLength(vehicleRef: String?): String? {
        if (vehicleRef == null) return null
        if (vehicleRef.length in 6..7) {
            return vehicleRef
        }
        return null
    }

    private fun hasTrafficEventChanges(
        journeyMitigationState: DTOJourneyMitigationState?,
        journey: DTODatedJourney,
        contingency: DTOJourneyEventContingencyVehiclePlanned,
    ): Boolean {
        val currentPlannedStatus = journeyMitigationState?.contingencyVehiclePlanned
        if (currentPlannedStatus != contingency.planned) {
            return true
        }
        val standbyVehicleId = contingency.standbyVehicleId
        val vehicleRef = contingency.vehicleRef

        if (standbyVehicleId == null && vehicleRef == null) {
            return false
        }

        if (journey.vehicles.isEmpty()) {
            return true
        }

        val exactMatch =
            journey.vehicles.any { vehicle ->
                vehicle.standbyVehicleId == standbyVehicleId && vehicle.vehicleRef == vehicleRef
            }

        return !exactMatch
    }
}
