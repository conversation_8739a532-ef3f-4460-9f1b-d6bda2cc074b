package no.ruter.tranop.app.event.journey.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.JourneyEventTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.JourneyEventRecord

class JourneyEventQueryBuilder :
    BaseRecordQueryBuilder<
        JourneyEventTable,
        JourneyEventRecord,
        BaseRecordTableMetadata<JourneyEventTable, JourneyEventRecord>,
        JourneyEventQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.DATED_JOURNEY_EVENT
    }
}
