package no.ruter.tranop.app.event.journey.input.process.info

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.MissingSignOnJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventMissingSignOn

class MissingSignOnJourneyEventHandler(
    config: MissingSignOnJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val metaFields =
            mapOf(
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to DTOJourneyEventMissingSignOn::getDepartureDateTime,
                DTOEventMetadataKeyType.TIME_BOUNDARY to DTOJourneyEventMissingSignOn::getThreshold,
            )
    }

    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.missingSignOn?.let { missingSignOn ->
            val type =
                if (missingSignOn.resolved != null && missingSignOn.resolved) {
                    DTOEventType.MISSING_SIGN_ON_RESOLVED
                } else {
                    DTOEventType.MISSING_SIGN_ON
                }

            createEvent(
                event = event,
                type = type,
                metadata = collect(event, metaFields, missingSignOn),
                context = context,
            )
        }
}
