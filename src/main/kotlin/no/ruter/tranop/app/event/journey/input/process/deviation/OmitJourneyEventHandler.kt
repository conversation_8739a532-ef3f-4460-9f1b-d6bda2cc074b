package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.OmissionJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.JourneyEventHandlerContext
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOmission
import java.time.OffsetDateTime

class OmitJourneyEventHandler(
    config: OmissionJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val omitMetadataFields =
            mapOf(
                DTOEventMetadataKeyType.VEHICLE_TASK_ID to DTOJourneyEventOmission::getVehicleTaskId,
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to { event -> event.firstDepartureDateTime?.toOffsetDateTime() },
                DTOEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME to { event -> event.lastArrivalDateTime?.toOffsetDateTime() },
            )
    }

    override fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        event.omission?.let { omission ->
            val omitted = omission.omitted
            val calls = journey.plan.calls
            val lastArrival = omission.lastArrivalDateTime?.toOffsetDateTime()
            val firstDeparture = omission.firstDepartureDateTime?.toOffsetDateTime()
            val eventCalls = omission.calls
            var anyCallMatched = false

            if (eventCalls.isNullOrEmpty() && lastArrival == null && firstDeparture == null) {
                calls.forEach { it.omitted = omitted }
            } else {
                val callsWithinServiceWindow = calls.filter { it.isWithin(firstDeparture, lastArrival) }
                callsWithinServiceWindow.forEach { it.omitted = omitted }

                if (!eventCalls.isNullOrEmpty()) {
                    val stopPoints = getStopPointMap(journey)
                    val callsMatching = calls.filter { callMatches(it, stopPoints, eventCalls) }
                    callsMatching.forEach { call -> call.omitted = omitted }
                    anyCallMatched = callsMatching.isNotEmpty()
                }
            }

            val allCallsOmitted = calls.all { it.omitted == true }
            val anyCallOmitted = calls.any { it.omitted == true }
            val noCallsOmitted = calls.none { it.omitted == true }

            journey.omitted = allCallsOmitted

            journey.journeyState?.journeyDeviationState?.apply {
                this.omitted = allCallsOmitted
                this.partiallyOmitted = !allCallsOmitted && anyCallOmitted
            }

            val type =
                when {
                    omitted && allCallsOmitted -> DTOEventType.OMITTED
                    omitted && anyCallOmitted -> DTOEventType.CALLS_OMITTED
                    !omitted && anyCallMatched -> DTOEventType.CALLS_UN_OMITTED
                    !omitted && noCallsOmitted -> DTOEventType.UN_OMITTED
                    else -> DTOEventType.UNKNOWN
                }

            val metadata = collect(event, omitMetadataFields, omission)
            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Omission processed [$type / ${journey.ref}]",
                source = "journey-event/operational",
                context = context,
            )?.let {
                context.deviationResult(
                    input = event,
                    output = it,
                    code = DTOServiceDeviationCode.NO_SERVICE,
                    includeDeviation = type != DTOEventType.UN_OMITTED && type != DTOEventType.CALLS_UN_OMITTED,
                )
            }
        }
        return null
    }

    // TODO: Should we do minute-resolution matching here instead of exact, like we do for cancelled calls?
    private fun DTODatedJourneyCall.isWithin(
        start: OffsetDateTime?,
        end: OffsetDateTime?,
    ): Boolean {
        start ?: return false
        end ?: return false
        val tmpArrival = this.plannedArrival?.toOffsetDateTime()
        val tmpDeparture = this.plannedDeparture?.toOffsetDateTime()

        val callArrival = tmpArrival ?: tmpDeparture ?: return false
        val callDeparture = tmpDeparture ?: tmpArrival ?: return false

        return ((start.isBefore(callDeparture) || start.isEqual(callDeparture)) && (end.isAfter(callArrival) || end.isEqual(callDeparture)))
    }
}
