package no.ruter.tranop.app.event.journey.input

import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

interface JourneyEventHandler {
    fun handleJourneyEvent(
        context: JourneyEventHandlerContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent?
}
