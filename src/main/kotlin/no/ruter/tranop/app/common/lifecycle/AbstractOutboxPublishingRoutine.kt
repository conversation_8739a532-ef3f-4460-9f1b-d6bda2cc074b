package no.ruter.tranop.app.common.lifecycle

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.OutboxContext
import no.ruter.tranop.app.outbox.OutboxMappingException
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.assignment.common.db.record.base.BaseRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.jooq.Table
import org.jooq.UpdatableRecord
import java.time.OffsetDateTime
import kotlin.reflect.KClass

abstract class AbstractOutboxPublishingRoutine<
    T : Table<R>,
    R,
    D : Any,
    O : Any,
>(
    appInfoProperties: AppInfoProperties,
    name: String,
    insightService: InsightService,
    abstractLifeCycleConfig: AbstractLifeCycleConfig,
    channel: DataChannel = recordType.channels.output,
    val targetType: DBOutboxTargetType,
    val recordType: RecordType<T, R, *>,
    val payloadClass: KClass<D>,
    val outboxService: OutboxService,
) : AbstractLifeCycleRoutine(
        appInfoProperties = appInfoProperties,
        name = "$name-${targetType.value}",
        insightService = insightService,
        abstractLifeCycleConfig = abstractLifeCycleConfig,
        channel = channel,
    ) where R : BaseRecord, R : UpdatableRecord<R> {
    val outputs = recordType.outputs
    val dataType: DBOutboxDataType = outputs?.type ?: throw IllegalStateException("OutboxDataType not configured for $recordType")

    // TODO: Is this supposed to be configured per target type?
    val retryCount = 5

    abstract fun map(
        data: D?,
        context: MappingContext,
    ): O?

    abstract fun publish(
        messages: List<OutboxOutputMessage<O>>,
        postProcessing: (Exception?) -> Unit = {},
    )

    abstract fun metadata(data: D?): Map<String, Any?>

    override fun execute(started: OffsetDateTime): Int {
        var count = 0
        generateSequence {
            findUnpublished(limit = batchSize).takeIf { it.isNotEmpty() }
        }.forEach { batch ->
            try {
                val output =
                    batch.map { outbox ->
                        val outboxData = outbox.data
                        val ref = outboxData.payloadRef
                        val data = mapOutput(outbox)
                        OutboxOutputMessage(outbox, ref, data)
                    }

                publish(output) { e ->
                    if (e == null) {
                        handleResult(
                            desc = "Publish OK",
                            outputs = output,
                            handler = outboxService::markOutboxesAsPublished,
                        )
                        count += output.size
                    } else {
                        handleResult(
                            desc = "Publish Failed",
                            outputs = output,
                            handler = outboxService::markAsFailed,
                            e = e,
                        )
                    }
                }
            } catch (e: Exception) {
                handleResult(
                    desc = "Failed",
                    outputs =
                        batch.map { outbox ->
                            OutboxOutputMessage(
                                outbox = outbox,
                                value = null,
                            )
                        },
                    outboxService::markAsFailed,
                    e,
                )
            }

            count++
        }
        return count
    }

    private fun mapOutput(outbox: InternalOutbox): O? {
        val outboxDataPayload = outbox.data.payload
        if (outboxDataPayload.isNullOrBlank() || outboxDataPayload == "null") {
            return null
        }

        val context = mapPayload(outbox)
        val payload = context.payload
        val mapped = payload?.let { map(payload, context) }
        if (context.hasErrors || mapped == null) {
            val metadata =
                mapOf(
                    "errors" to context.errors,
                ) + metadata(payload) + outboxMetadata(payload, outbox.data.payload)
            val msg = "Failed to parse [$targetType, $recordType]"
            logger.warn(msg, metadata = metadata)
            throw OutboxMappingException(msg + context.errors)
        }
        return mapped
    }

    fun findUnpublished(limit: Int? = null): List<InternalOutbox> =
        outboxService
            .findUnpublished(
                dataType = dataType,
                targetType = targetType,
                retryCount = retryCount,
                pagination =
                    limit?.let {
                        AbstractQueryBuilder.Pagination(limit = it)
                    } ?: AbstractQueryBuilder.Pagination(),
            )

    fun mapPayload(outbox: InternalOutbox): OutboxContext<T, R, D?> {
        val data = outbox.data
        val dataPayload = data.payload
        val payload = parseJson(dataPayload)

        val context =
            OutboxContext(
                payload = payload,
                recordType = recordType,
            )
        return context
    }

    private fun parseJson(data: String?) =
        try {
            JsonUtils.toObject(data, payloadClass.java)
        } catch (e: Exception) {
            logger.warn("Unable to parse payload: ${data?.take(100)}", e)
            null
        }

    fun outboxMetadata(
        data: D? = null,
        ref: String?,
    ): Map<String, Any?> =
        metadata(data) +
            mapOf(
                "ref" to ref,
                "recordType" to recordType.desc,
                "targetType" to targetType.description(),
            )

    private fun handleResult(
        desc: String,
        outputs: List<OutboxOutputMessage<O>>,
        handler: (Collection<InternalOutbox>) -> Int?,
        e: Throwable? = null,
    ): Int {
        var counter = 0
        outputs.forEach { output ->
            val outboxData = output.outbox.data
            val metadata =
                outboxMetadata(
                    ref = outboxData.ref,
                )
            logger.info(
                msg = "Outbox Publishing: $desc ${outboxData.dataType}-${outboxData.targetType}",
                metadata = metadata,
                e = e,
            )
            e?.let { handleException(e = it) }
            counter += handler(listOf(output.outbox)) ?: 0
        }

        return counter
    }
}
