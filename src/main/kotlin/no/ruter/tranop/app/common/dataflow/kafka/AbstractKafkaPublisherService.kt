package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaOutputConfigProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.delayMillis
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.Serde
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit

abstract class AbstractKafkaPublisherService<K, KS : Serde<K>, V, VS : Serde<V>>(
    producerBinding: KafkaProducerBinding<K, KS, V, VS>,
    val config: KafkaOutputConfigProperties,
    val insightService: InsightService,
    val keyType: String,
) {
    protected val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)
    private val producer = producerBinding.producer
    val topic = producerBinding.topicBinding.name

    private val enabled get() = config.enabled

    private val maxRetries: Int get() = config.maxRetires

    fun publishToKafka(
        key: K,
        value: V?,
        postProcessing: (Exception?) -> Unit = {},
    ) {
        if (enabled) {
            publishToKafka(key, value, postProcessing, currentRetry = 0)
        }
    }

    fun tombstone(key: K): Result<Unit> {
        val future = CompletableFuture<Result<Unit>>()

        publishToKafka(key, null) { exception ->
            when (exception) {
                null -> {
                    future.complete(Result.success(Unit))
                }

                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }

    private fun publishToKafka(
        key: K,
        value: V?,
        postProcessing: (Exception?) -> Unit = {},
        currentRetry: Int = 0,
    ) {
        val record = ProducerRecord(topic, key, value)

        try {
            producer.send(record).get(1, TimeUnit.SECONDS)
            insightService.messageSent(topic)
            postProcessing(null)
        } catch (e: Exception) {
            handleRetry(key, value, postProcessing, e, currentRetry = currentRetry + 1)
        }
    }

    // TODO: Replace with KafkaRetryTemplate
    private fun handleRetry(
        key: K,
        value: V?,
        postProcessing: (Exception?) -> Unit,
        exception: Exception,
        currentRetry: Int,
    ) {
        val msg = "Failed sending message to topic [$key / $topic][$currentRetry / $maxRetries]"
        val metadata = mapOf(keyType to key)
        if (currentRetry <= maxRetries) {
            log.error(msg, metadata, exception)
            val delay = delayMillis(currentRetry)
            Thread.sleep(delay)
            publishToKafka(key, value, postProcessing, currentRetry)
        } else {
            log.error("$msg - Retries exhausted [$maxRetries]", metadata, exception)
            postProcessing(exception)
        }
    }
}
