package no.ruter.tranop.app.common.lifecycle

import java.time.Duration

class RoutineConfig {
    companion object {
        const val OPERATOR_CONTRACTS: String = "operator-contracts"
        const val JOURNEY_TYPES: String = "journey-types"
    }

    var enabled: Boolean = false
    var frequent: Boolean = false
    var olderThan: Duration = Duration.ofSeconds(30L)
    var arrivalAfter: Duration? = null
    var batchSize: Int? = null
    var excludes: Map<String, List<String>> = emptyMap()

    fun getExcludes(type: String): List<String>? = excludes[type]
}
