package no.ruter.tranop.app.common.dataflow.opensearch

import org.opensearch.client.opensearch.OpenSearchClient
import org.opensearch.client.opensearch._types.OpenSearchException
import org.opensearch.client.opensearch._types.query_dsl.QueryBuilders
import org.opensearch.client.opensearch.core.BulkRequest
import org.opensearch.client.opensearch.core.BulkResponse
import org.opensearch.client.opensearch.core.DeleteRequest
import org.opensearch.client.opensearch.core.IndexRequest
import org.opensearch.client.opensearch.core.SearchRequest
import org.opensearch.client.opensearch.indices.CreateIndexRequest
import org.opensearch.client.opensearch.indices.GetIndexRequest
import org.springframework.stereotype.Service

@Service
class OpenSearchService(
    val openSearchClient: OpenSearchClient,
) {
    fun createIndexIfNotExists(name: String): <PERSON><PERSON>an {
        try {
            openSearchClient.indices().create(CreateIndexRequest.Builder().index(name).build())
        } catch (e: OpenSearchException) {
            val errorType = e.response().error().type()
            if (errorType == "resource_already_exists_exception") {
                return false
            }
            throw e
        }
        return true
    }

    fun listIndexes(): List<String> {
        val result = openSearchClient.indices().get(GetIndexRequest.Builder().index(listOf("*")).build())
        return result.result().keys.toList()
    }

    final inline fun <reified T> getSome(
        index: String,
        size: Int = 1000,
    ): List<T> {
        val query =
            QueryBuilders
                .matchAll()
                .build()
                .toQuery()

        val req =
            SearchRequest
                .Builder()
                .index(index)
                .query(query)
                .size(size)
                .build()

        val res = openSearchClient.search(req, T::class.java)
        return res.hits().hits().mapNotNull { it.source() }
    }

    final inline fun <reified T> fetchDocumentById(
        index: String,
        id: String,
    ): T? {
        val query =
            QueryBuilders
                .ids()
                .values(id)
                .build()
                .toQuery()
        val searchRequest =
            SearchRequest
                .Builder()
                .query(query)
                .index(index)
                .build()
        val result = openSearchClient.search(searchRequest, T::class.java)
        return result
            .hits()
            .hits()
            .firstOrNull()
            ?.source()
    }

    fun <T> indexDocument(
        input: T,
        id: String,
        index: String,
    ): Result<String> {
        val indexRequest =
            IndexRequest
                .Builder<T>()
                .index(index)
                .id(id)
                .document(input)
                .build()
        return try {
            val response = openSearchClient.index(indexRequest)
            Result.success(response.id())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    fun <T> bulkIndexDocumentBatch(
        input: List<T>,
        index: String,
        idSelector: (T) -> String,
    ): BulkResponse {
        val request = BulkRequest.Builder()
        input.forEach { inputItem ->
            request.operations { op ->
                op.index { idx ->
                    idx
                        .index(index)
                        .document(inputItem)
                        .id(idSelector(inputItem))
                }
            }
        }
        val bulkOperationResponse = openSearchClient.bulk(request.build())
        if (bulkOperationResponse.errors()) {
            val items = bulkOperationResponse.items()
            val errors = mutableMapOf<String, String?>()
            items.forEach { b ->

                val reason = b.error()?.reason()
                b.id()?.let { id ->
                    errors[id] = reason
                }
            }
            val errorsSpec = errors.values.joinToString(separator = ", ")
            throw OpenSearchIngestException(
                message = "[bulkIndexDocumentBatch] Error Occurred [$errorsSpec]",
                errors = errors,
            )
        }
        return bulkOperationResponse
    }

    fun bulkDeleteDocumentFromIndex(
        index: String,
        refs: List<String>,
    ): BulkResponse {
        val request = BulkRequest.Builder()
        refs.forEach { ref ->
            request.operations { op ->
                op.delete { deleteOp ->
                    deleteOp.index(index).id(ref)
                }
            }
        }
        return openSearchClient.bulk(request.build())
    }

    fun deleteDocumentById(
        index: String,
        id: String,
    ): Result<Unit> =
        try {
            val deleteRequest =
                DeleteRequest
                    .Builder()
                    .id(id)
                    .index(index)
                    .build()
            openSearchClient.delete(deleteRequest)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
}
