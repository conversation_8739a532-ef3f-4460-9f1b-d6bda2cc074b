package no.ruter.tranop.app.common.dataflow.snowflake

import jakarta.annotation.PreDestroy
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestChannel
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClient
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.outbox.OutboxPublishingException

/**
 * Abstract base class for Snowflake streaming ingest clients.
 * has common functionality for client and channel management, ingestion, and cleanup
 *
 * @param T The type of data being ingested
 * @property config Configuration for streaming ingest
 * @property clientFactory Factory for creating Snowflake clients and channels
 * @property clientBuilderName Name for the Snowflake client builder
 * @property channelBuilderName Name for the Snowflake channel builder
 */
abstract class AbstractSnowflakeIngestClient<T>(
    private val config: SnowflakeClientProperties,
    private val clientFactory: SnowflakeClientFactory,
    private val clientBuilderName: String,
    private val channelBuilderName: String,
) {
    protected val logger = LoggerFactory.getLogger(javaClass)

    private val client: SnowflakeStreamingIngestClient? =
        if (config.enabled) clientFactory.createClient(clientBuilderName, config) else null

    private val channel: SnowflakeStreamingIngestChannel? =
        client?.let { clientFactory.createChannel(channelBuilderName, it, config) }

    protected abstract fun T.toIngestMap(): Map<String, Any?>

    fun ingest(
        events: List<Pair<String, T?>>,
        throwExceptionOnError: Boolean = false,
    ): Pair<List<String>, List<String>> {
        if (channel == null || events.isEmpty()) {
            return Pair(emptyList(), emptyList())
        }

        val outboxRefs = events.map { it.first }
        val offsetToken = outboxRefs.last() // Note: we use ref from key in pair instead of fetching from value, since value may be null.
        val result =
            channel.insertRows(
                events.map { it.second?.toIngestMap() },
                offsetToken,
            )

        if (!verifyOffset(channel, offsetToken)) {
            val msg =
                "Could not verify offset when ingesting data to Snowflake. " +
                    "Marking all failed with insertErrors: ${result.insertErrors}"
            val failedRefs = events.map { it.first }
            logger.warn(msg)
            if (throwExceptionOnError) {
                throw OutboxPublishingException(msg, failedRefs = failedRefs)
            }
            return Pair(emptyList(), failedRefs)
        }

        if (result.hasErrors()) {
            val msg = "Error ingesting data to Snowflake: ${result.insertErrors}"
            logger.warn(msg)
            val errorRefs =
                result.insertErrors.map { error ->
                    events[error.rowIndex.toInt()].first
                }
            if (throwExceptionOnError) {
                throw OutboxPublishingException(msg, failedRefs = errorRefs)
            }
            return Pair(outboxRefs - errorRefs.toSet(), errorRefs)
        }

        logger.debug("Successfully ingested ${events.size} events to Snowflake")
        return Pair(
            outboxRefs,
            emptyList(),
        )
    }

    @PreDestroy
    fun shutdown() {
        runCatching {
            channel?.close(true)
            client?.close()
        }.onFailure { e ->
            logger.error("Failed to close Snowflake ingest client on shutdown", e)
        }
    }

    private fun verifyOffset(
        channel: SnowflakeStreamingIngestChannel,
        offsetToken: String,
    ): Boolean {
        repeat(20) {
            if (channel.latestCommittedOffsetToken == offsetToken) return true
            Thread.sleep(200)
        }
        return false
    }
}
