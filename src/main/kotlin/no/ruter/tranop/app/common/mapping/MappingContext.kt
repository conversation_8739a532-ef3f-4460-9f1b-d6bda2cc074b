package no.ruter.tranop.app.common.mapping

import no.ruter.tranop.app.common.insight.InsightContext

/** Mapping details collected during output mapping. **/
class MappingDetails(
    val type: Type,
    val path: String,
    val msg: String,
    val reason: String,
) {
    enum class Type(
        val value: String,
        val insightKey: String,
    ) {
        INFO(value = "info", insightKey = "info"),
        WARN(value = "warning", insightKey = "warn"),
        ERROR(value = "error", insightKey = "error"),
        ;

        override fun toString(): String = value
    }

    val insight = "${type.insightKey}.$reason"

    override fun toString(): String = "$path : $msg : $reason"

    companion object {
        fun error(
            path: String,
            msg: String,
            reason: String = "",
        ) = MappingDetails(type = Type.ERROR, path = path, msg = msg, reason = reason)

        fun summary(details: Collection<MappingDetails>): String = details.joinToString(separator = "\n") { "- $it" }
    }
}

interface MappingContext : InsightContext {
    /** Optional mapping info added during data mapping. **/
    val info: List<MappingDetails>

    /** Optional mapping errors detected during data mapping. **/
    val errors: List<MappingDetails>

    /** Optional mapping warnings detected during data mapping. **/
    val warnings: List<MappingDetails>

    val valid: Boolean
        get() = !hasErrors

    val hasInfo: Boolean
        get() = info.isNotEmpty()

    val hasErrors: Boolean
        get() = errors.isNotEmpty()

    val hasWarnings: Boolean
        get() = warnings.isNotEmpty()

    /** Add info to mapping context. **/
    fun addInfo(
        path: String,
        msg: String,
        reason: String,
    )

    /** Add error to mapping context. **/
    fun addError(
        path: String,
        msg: String,
        reason: String,
    )

    /** Add warning to mapping context. **/
    fun addWarning(
        path: String,
        msg: String,
        reason: String,
    )

    fun add(
        type: MappingDetails.Type,
        path: String,
        msg: String,
        reason: String,
    ) = when (type) {
        MappingDetails.Type.INFO -> addInfo(path, msg, reason)
        MappingDetails.Type.WARN -> addWarning(path, msg, reason)
        MappingDetails.Type.ERROR -> addError(path, msg, reason)
    }
}

/** Map input to output, with null pass-through. **/
inline fun <I, O, C : MappingContext> C.map(
    input: I?,
    path: String,
    mapper: (I, String, C) -> O?,
): O? = if (input != null) mapper(input, path, this) else null

/** Map input collection to a list, using mapper, ignoring null inputs and null outputs. **/
inline fun <I, O, C : MappingContext> C.mapList(
    input: Collection<I?>?,
    path: String,
    mapper: (I, String, C) -> O?,
): List<O> = input?.mapIndexedNotNull { i, e -> if (e != null) mapper(e, "$path[$i]", this) else null } ?: emptyList()
