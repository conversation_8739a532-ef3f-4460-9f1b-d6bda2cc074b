package no.ruter.tranop.app.common.dataflow.snowflake

object SnowflakeIngestClientConstants {
    const val COL_REF = "REF"
    const val COL_ASSIGNMENT_REF = "ASSIGNMENT_REF"
    const val COL_OWNER_ID = "OWNER_ID"
    const val COL_CREATED_AT = "CREATED_AT"
    const val COL_AUTHORITY_ID = "AUTHORITY_ID"
    const val COL_OPERATOR_ID = "OPERATOR_ID"
    const val COL_QUAY_REFS = "QUAY_REFS"
    const val COL_STOP_POINT_REFS = "STOP_POINT_REFS"
    const val COL_DATED_JOURNEY_V2_REF = "DATED_JOURNEY_V2_REF"
    const val COL_DATED_JOURNEY_V2_REFS = "DATED_JOURNEY_V2_REFS"
    const val COL_LINE_REFS = "LINE_REFS"
    const val COL_JSON_DATA = "JSON_DATA"
    const val COL_METADATA = "METADATA"
    const val COL_TYPE = "TYPE"
}
