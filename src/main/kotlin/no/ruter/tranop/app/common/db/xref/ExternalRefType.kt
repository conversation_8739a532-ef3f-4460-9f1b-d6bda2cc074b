package no.ruter.tranop.app.common.db.xref

import java.util.Locale

class ExternalRefType private constructor(
    val name: String,
    val value: Int,
) {
    companion object {
        private const val VALUE_NONE = -1

        private val byName = LinkedHashMap<String, ExternalRefType>()
        private val byValue = LinkedHashMap<Int, ExternalRefType>()

        val LINE_ID = define(name = "line-id", value = 1)
        val NSR_QUAY_ID = define(name = "nsr-quay-id", value = 2)
        val STOP_POINT_REF = define(name = "stop-point-ref", value = 3)
        val DATED_JOURNEY_V2_REF = define(name = "dated-journey-v2-ref", value = 4)
        val PTO_CASE_REF = define(name = "pto-case-ref", value = 5)
        val PTA_CASE_REF = define(name = "pta-case-ref", value = 6)
        val ENTITY_TRAFFIC_CASE_KEY_V2_REF = define(name = "entity-traffic-case-key-v2-ref", value = 7)
        val ENTITY_TRAFFIC_EVENT_KEY_V1_REF = define(name = "entity-traffic-event-key-v1-ref", value = 8)
        val ENTITY_TRAFFIC_SITUATION_KEY_V2_REF = define(name = "entity-traffic-situation-key-v2-ref", value = 9)

        fun of(name: String?): ExternalRefType? {
            if (name.isNullOrEmpty()) return null

            val key = key(name)
            return byName[key] ?: ExternalRefType(name = name, value = VALUE_NONE)
        }

        fun of(value: Int?): ExternalRefType? {
            if (value == null) return null

            return byValue[value] ?: ExternalRefType(name = value.toString(), value = value)
        }

        private fun key(name: String): String = name.lowercase(Locale.ENGLISH)

        private fun define(
            name: String,
            value: Int,
        ): ExternalRefType {
            fun duplicateException() = IllegalStateException("Duplicate external ref type definition: name=$name value=$value")

            val key = key(name)
            if (byName.contains(key)) {
                throw duplicateException()
            }

            if (byValue.contains(value)) {
                throw duplicateException()
            }

            val res = ExternalRefType(name, value)
            byName[key] = res
            byValue[value] = res
            return res
        }
    }

    override fun toString(): String = name

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is ExternalRefType) return false
        return this.name == other.name && this.value == other.value
    }

    override fun hashCode(): Int = name.hashCode()
}
