package no.ruter.tranop.app.common.db.record.json

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.assignment.common.db.record.json.JSONRecord
import org.jooq.Table
import org.jooq.UpdatableRecord

abstract class JSONRecordData<
    T : Table<R>,
    R,
    D,
>(
    val type: RecordType<T, R, *>,
    val data: D,
    val record: R,
) where R : JSONRecord, R : UpdatableRecord<R> {
    val ref: String?
        get() = record.ref ?: dataRef

    abstract val dataRef: String?

    val deleted: Boolean
        get() = record.deletedAt != null

    val operatorId: String?
        get() = record.operatorId

    val authorityId: String?
        get() = record.authorityId
}
