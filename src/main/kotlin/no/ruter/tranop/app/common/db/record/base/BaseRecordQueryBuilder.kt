package no.ruter.tranop.app.common.db.record.base

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import org.jooq.Table
import org.jooq.UpdatableRecord
import org.jooq.impl.DSL
import java.time.OffsetDateTime

open class BaseRecordQueryBuilder<
    T : Table<R>,
    R : UpdatableRecord<R>,
    D : BaseRecordTableMetadata<T, R>,
    Q : BaseRecordQueryBuilder<T, R, D, Q>,
>(
    protected val recordType: RecordType<T, R, D>,
) : AbstractQueryBuilder<
        R,
        T,
        Q,
    >(
        table = recordType.table,
    ) {
    protected val tableMeta = recordType.tableMeta

    fun ref(ref: String?) = append(tableMeta.ref.eq(ref))

    fun refs(refs: Set<String>) = append(recordType.tableMeta.ref.`in`(refs))

    @Suppress(names = ["UNCHECKED_CAST"])
    fun authorized(
        trace: TraceInfo?,
        restricted: Boolean = true,
        includeDeleted: Boolean = false,
    ): Q {
        if (restricted) {
            tableMeta.operatorId?.let { operatorId ->
                tableMeta.authorityId?.let { authorityId ->
                    val cond =
                        trace?.let {
                            val op = operatorId.isNull.or(operatorId.eq(trace.operatorId))
                            val auth = authorityId.isNull.or(authorityId.eq(trace.authorityId))
                            op.and(auth)
                        } ?: DSL.falseCondition()
                    append(cond)
                }
            }
        }
        return includeDeleted(includeDeleted)
    }

    @Suppress(names = ["UNCHECKED_CAST"])
    fun includeDeleted(includeDeleted: Boolean = false): Q =
        (if (includeDeleted) this else tableMeta.deleted?.let { append(it.isNull) } ?: this) as Q

    fun toDateTime(
        dateTime: String?,
        now: OffsetDateTime? = null,
    ): Q = toDateTime(dateTime?.parseOffsetDateTime(name = "to date-time", now = now))

    @Suppress(names = ["UNCHECKED_CAST"])
    fun toDateTime(dateTime: OffsetDateTime?): Q = dateTime?.let { append(tableMeta.created.le(it)) } ?: this as Q

    fun fromDateTime(
        dateTime: String?,
        now: OffsetDateTime? = null,
    ): Q = fromDateTime(dateTime?.parseOffsetDateTime(name = "from date-time", now = now))

    @Suppress(names = ["UNCHECKED_CAST"])
    fun fromDateTime(dateTime: OffsetDateTime?): Q = dateTime?.let { append(tableMeta.created.ge(it)) } ?: this as Q
}
