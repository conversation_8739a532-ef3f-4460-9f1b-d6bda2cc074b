package no.ruter.tranop.app.outbox

import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class OutboxService(
    private val repository: OutboxRepository,
    private val timeService: TimeService,
) {
    fun findUnpublished(
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
        pagination: AbstractQueryBuilder.Pagination = AbstractQueryBuilder.Pagination(),
        retryCount: Int = 5,
    ): List<InternalOutbox> {
        val payloadRefs =
            repository.findUnpublishedPayloadRefs(
                dataType = dataType,
                targetType = targetType,
                retryCount = retryCount,
                pagination = pagination,
                eligibleBefore = timeService.now(),
            )

        return repository.fetchByPayloadRefs(payloadRefs, dataType, targetType) ?: emptyList()
    }

    fun markAsPublished(refList: Collection<String>): Int? =
        if (refList.isEmpty()) {
            0
        } else {
            repository.markPublished(refList, timeService.now())
        }

    fun markOutboxesAsPublished(refList: Collection<InternalOutbox>): Int? =
        if (refList.isEmpty()) {
            0
        } else {
            repository.markPublished(refList.map { it.record.ref }, timeService.now())
        }

    fun markAsFailed(refList: Collection<InternalOutbox>): Int? =
        if (refList.isEmpty()) {
            0
        } else {
            repository.markAsFailed(refList, timeService.now())
        }

    fun deleteOldProcessedEvents(time: OffsetDateTime) = repository.deleteOldProcessedEvents(time)
}
