package no.ruter.tranop.app.outbox.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.OutboxTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord

class OutboxQueryBuilder :
    BaseRecordQueryBuilder<
        OutboxTable,
        OutboxRecord,
        BaseRecordTableMetadata<OutboxTable, OutboxRecord>,
        OutboxQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.OUTBOX
    }
}
