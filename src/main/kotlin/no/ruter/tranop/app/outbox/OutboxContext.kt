package no.ruter.tranop.app.outbox

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.mapping.AbstractMappingContext
import no.ruter.tranop.app.common.mapping.MapperUtils
import org.jooq.Table
import org.jooq.UpdatableRecord

class OutboxContext<
    T : Table<R>,
    R : UpdatableRecord<R>,
    D : Any?,
>(
    val payload: D,
    val recordType: RecordType<T, R, *>,
) : AbstractMappingContext() {
    override val channel: DataChannel
        get() = recordType.channels.output
    override val traceId: String?
        get() = MapperUtils.randomId(prefix = channel.name)
    override val metrics: Map<String, List<String>>
        get() = emptyMap()

    override fun toString(): String =
        try {
            JsonUtils.toJson(payload)
        } catch (e: Exception) {
            "Not able to parse payload: ${e.message}"
        }
}
