package no.ruter.tranop.app.outbox.db

import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.assignmentmanager.db.sql.tables.OutboxTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.exception.DataChangedException
import org.jooq.impl.DSL.max
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import kotlin.math.pow

@Component
class OutboxRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        OutboxTable,
        OutboxRecord,
        InternalOutbox,
        OutboxRecordMapper,
        OutboxQueryBuilder,
        BaseRecordTableMetadata<OutboxTable, OutboxRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = SORT_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = OutboxRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = OutboxQueryBuilder.TYPE
        val TABLE = TYPE.table

        val SORT_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    private fun targetTypeCondition(targetType: DBOutboxTargetType): Condition = TABLE.TARGET_TYPE.eq(targetType.value)

    private fun dataTypeCondition(dataType: DBOutboxDataType): Condition = TABLE.DATA_TYPE.eq(dataType.value)

    private fun payloadRefCondition(strings: Collection<String>): Condition = TABLE.PAYLOAD_REF.`in`(strings)

    private val logger = LoggerFactory.getLogger(javaClass.canonicalName)

    override fun queryBuilder() = OutboxQueryBuilder()

    fun store(outbox: Outbox) {
        val ref = outbox.ref ?: MapperUtils.randomId(prefix = "outbox-")
        fetchByRef(ref)?.let {
            logger.warn("Outbox record with ref $ref already exists, skipping store operation.")
        } ?: storeRecord(wrapSpec(ref, outbox, dslContext.newRecord(table)))
    }

    fun store(outbox: Collection<Outbox>) {
        outbox.forEach {
            store(it)
        }
    }

    fun fetchByPayloadRefs(
        refs: Collection<String>?,
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
    ): List<InternalOutbox>? =
        refs?.let {
            val dataTypeCondition = dataTypeCondition(dataType)
            val targetTypeCondition = targetTypeCondition(targetType)
            val payloadRefCondition = payloadRefCondition(it)
            val condition =
                payloadRefCondition
                    .and(dataTypeCondition)
                    .and(targetTypeCondition)
                    .and(unpublished())
            fetch(cond = condition)
        }

    fun findUnpublishedPayloadRefs(
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
        retryCount: Int = 5,
        pagination: AbstractQueryBuilder.Pagination? = null, // TODO: Remove pagination and use an Int as limit/batchSize
        eligibleBefore: OffsetDateTime = timeService.now(),
    ): Set<String> {
        // Where conditions
        val unpublished = unpublished()
        val dataTypeCondition = dataTypeCondition(dataType)
        val targetTypeCondition = targetTypeCondition(targetType)
        val whereCondition =
            unpublished
                .and(dataTypeCondition)
                .and(targetTypeCondition)

        // Having conditions are applied on data grouped by the groupByField
        val maxRetries = max(TABLE.RETRIES).le(retryCount)
        val maxNextAttemptNull = max(TABLE.NEXT_ATTEMPT_AT).isNull()
        val maxNextAttemptEligible = max(TABLE.NEXT_ATTEMPT_AT).lessOrEqual(eligibleBefore)
        val nextAttempt = maxNextAttemptNull.or(maxNextAttemptEligible)
        val havingCondition = maxRetries.and(nextAttempt)

        val groupByField = TABLE.PAYLOAD_REF

        return dslContext
            .selectDistinct(TABLE.PAYLOAD_REF)
            .from(TABLE)
            .where(whereCondition)
            .groupBy(groupByField)
            .having(havingCondition)
            .limit(pagination?.limit)
            .fetchInto(String::class.java)
            .toSet()
    }

    private fun unpublished(): Condition = TABLE.PUBLISHED_AT.isNull()

    fun findUnpublished(
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
        pagination: AbstractQueryBuilder.Pagination = AbstractQueryBuilder.Pagination(),
        retryCount: Int = 5,
        eligibleBefore: OffsetDateTime = timeService.now(),
    ): List<InternalOutbox> =
        fetch(
            TABLE.PUBLISHED_AT.isNull
                .and(dataTypeCondition(dataType))
                .and(targetTypeCondition(targetType))
                .and(TABLE.RETRIES.le(retryCount))
                .and(
                    TABLE.NEXT_ATTEMPT_AT.isNull.or(
                        TABLE.NEXT_ATTEMPT_AT.lessOrEqual(eligibleBefore),
                    ),
                ),
            pagination,
        )

    fun storeRecord(
        data: InternalOutbox,
        now: OffsetDateTime? = null,
    ): Boolean {
        recordMapper.updateRecord(source = data.data, record = data.record, now = now ?: timeService.now())
        return data.record.store() == 1
    }

    fun markPublished(
        ref: Collection<String>,
        now: OffsetDateTime,
    ): Int? =
        try {
            dslContext
                .update(table)
                .set(table.PUBLISHED_AT, now)
                .where(table.REF.`in`(ref))
                .execute()
        } catch (exception: DataChangedException) {
            log.warn("Could not update published_at timestamp on outbox [ref=$ref]", exception)
            null
        }

    fun markAsFailed(
        list: Collection<InternalOutbox>,
        offsetDateTime: OffsetDateTime = timeService.now(),
    ): Int? =
        try {
            logger.warn(
                "Marking outbox records as failed for refs: ${list.map {
                    val record = it.record
                    "[${record.dataType}/${record.targetType}] ${record.ref}"
                }}",
            )
            var count = 0
            dslContext.batched { batch ->
                list.forEach { internalOutbox ->
                    val nextAttemptAt = offsetDateTime.plusMinutes(2.0.pow(internalOutbox.record.retries + 1).toLong())
                    count +=
                        batch
                            .dsl()
                            .update(table)
                            .set(table.NEXT_ATTEMPT_AT, nextAttemptAt)
                            .set(table.RETRIES, table.RETRIES.plus(1))
                            .where(table.REF.eq(internalOutbox.record.ref))
                            .execute()
                }
            }
            count
        } catch (exception: DataChangedException) {
            log.warn("Failed to update retries for refs: ${list.map { it.record.ref }}", exception)
            null
        }

    fun newRecord(outbox: Outbox): InternalOutbox {
        val ref = outbox.ref ?: MapperUtils.randomId(prefix = "outbox-")
        return wrapSpec(ref, outbox, dslContext.newRecord(table))
    }

    private fun wrapSpec(
        ref: String,
        outbox: Outbox,
        record: OutboxRecord,
    ): InternalOutbox =
        InternalOutbox(
            Outbox(
                ref = ref,
                dataType = outbox.dataType,
                targetType = outbox.targetType,
                payloadRef = outbox.payloadRef,
                payload = outbox.payload,
            ),
            record,
        )

    fun deleteOldProcessedEvents(time: OffsetDateTime): Int? =
        try {
            logger.info("Deleting old processed outbox events older than $time")
            dslContext
                .deleteFrom(table)
                .where(
                    table.PUBLISHED_AT.isNotNull
                        .and(table.PUBLISHED_AT.lt(time)),
                ).execute()
        } catch (exception: DataChangedException) {
            logger.warn("Could not delete old processed outbox events", exception)
            null
        }
}
