package no.ruter.tranop.app.variance.common.api.adt.v4.input

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.TraceInfoContext
import no.ruter.tranop.app.common.api.exception.APIOperationNotAllowedException
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.input.AbstractInputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.api.adt.ADTAuthTrace
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.common.dto.toDTOAuthenticationMessage
import java.time.OffsetDateTime

class ADTv4InputContext<R>(
    val now: OffsetDateTime,
    val request: R?,
    private val requestTrace: ADTAuthTrace,
    override val channel: DataChannel,
) : AbstractInputContext(),
    TraceInfoContext {
    companion object {
        const val MSG_AUTH_MISSING = "Missing or invalid operator authentication"
    }

    override val trace: TraceInfo
        get() = resolvedTrace ?: requestTrace

    override val traceId: String
        get() = trace.traceId ?: MapperUtils.TRACE_ID_NONE

    private var resolvedTrace: TraceInfo? = null

    override val events: Collection<InputEvent> = emptyList()

    override val metadata: Map<String, Any?>
        get() = emptyMap() // TODO: Fix this.

    override val ref: String? = null // TODO: Fix this.
    override val refPath: String = "$" // TODO: Fix this.
    override val header: DTOMessageHeader? = null
    override val received: OffsetDateTime = now

    override val metrics: Map<String, List<String>>
        get() = emptyMap() // TODO: Fix this.

    override fun resolveActiveOperator() {
        val miss = MSG_AUTH_MISSING
        val auth =
            try {
                requestTrace.xADTAuth?.toDTOAuthenticationMessage()
            } catch (e: Exception) {
                throw APIOperationNotAllowedException(miss)
            } ?: throw APIOperationNotAllowedException(miss)

        val allowedOperators = auth.operatorIds?.toSet() ?: emptySet()
        val allowedAuthorities = auth.authorityIds?.toSet() ?: emptySet()
        if (allowedOperators.isEmpty()) throw APIOperationNotAllowedException(msg = "$miss: operator not configured")
        if (allowedAuthorities.isEmpty()) throw APIOperationNotAllowedException(msg = "$miss: authority not configured")

        var activeOperatorId = trace.operatorId?.trim() ?: ""
        var activeAuthorityId = trace.authorityId?.trim() ?: ""
        if (activeOperatorId.isEmpty()) {
            activeOperatorId = allowedOperators.first()
        } else if (!allowedOperators.contains(activeOperatorId)) {
            throw APIOperationNotAllowedException(msg = "Restricted operator (no access)")
        }
        if (activeAuthorityId.isEmpty()) {
            activeAuthorityId = allowedAuthorities.first()
        } else if (!allowedAuthorities.contains(activeAuthorityId)) {
            throw APIOperationNotAllowedException(msg = "Restricted authority (no access)")
        }

        resolvedTrace =
            ADTAuthTrace(
                xADTAuth = requestTrace.xADTAuth,
                xTraceId = requestTrace.traceId,
                xRequestId = requestTrace.xRequestId,
                xOperatorId = activeOperatorId,
                xAuthorityId = activeAuthorityId,
            )
    }

    fun mergeServiceImpactValidationContext(impact: ServiceImpactContext) {
        fun merge(
            errors: List<MappingDetails>,
            reason: APIStatusResponseReasonCode,
        ) = errors.forEach { e -> addError(path = e.path, msg = e.msg, reason = reason.value) }

        merge(impact.errors, APIStatusResponseReasonCode.BAD_REQUEST)
        merge(impact.lineErrors, APIStatusResponseReasonCode.LINE_SPEC_ERROR)
        merge(impact.callErrors, APIStatusResponseReasonCode.CALL_SPEC_ERROR)
        merge(impact.journeyErrors, APIStatusResponseReasonCode.JOURNEY_SPEC_ERROR)
        merge(impact.stopPointErrors, APIStatusResponseReasonCode.STOP_POINT_SPEC_ERROR)
    }
}
