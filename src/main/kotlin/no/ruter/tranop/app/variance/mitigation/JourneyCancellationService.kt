package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.QuayRefTargets
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import org.apache.commons.lang3.NotImplementedException
import org.springframework.stereotype.Service

@Service
class JourneyCancellationService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceMitigationHandler {
    override fun processMitigationRequest(request: ServiceMitigationRequest): ServiceMitigationResponse {
        val type = request.type
        val targets = resolveQuayRefTargets(request)

        val cancelled = type != ServiceVarianceRequest.Type.DELETE
        val mitigation = request.stored ?: return ServiceMitigationResponse.NONE

        val immediate = true
        val targetJourneys = targets.journeys

        // Cancel target journeys.
        targetJourneys.included.forEach { target ->
            cancelJourney(
                trace = request.trace,
                channel = request.channel,
                target = target,
                mitigation = mitigation,
                immediate = immediate,
                targets = targets,
                cancelled = cancelled,
            )
        }

        // Recall cancellation on excluded targets.
        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targetJourneys.excluded.forEach { target ->
                cancelJourney(
                    trace = request.trace,
                    channel = request.channel,
                    target = target,
                    mitigation = mitigation,
                    immediate = immediate,
                    targets = targets,
                    cancelled = false,
                )
            }
        }
        return ServiceMitigationResponse(targets = targetJourneys)
    }

    fun cancelJourney(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        mitigation: InternalServiceMitigation,
        immediate: Boolean,
        targets: QuayRefTargets? = null,
        cancelled: Boolean = true,
    ) {
        val calls = target.calls?.map { call -> mapJourneyEventCall(call, targets) }
        val cancellation =
            DTOJourneyEventCancellation().apply {
                this.calls = calls
                this.partial = target.partial
                this.cancelled = cancelled
            }

        storeJourneyEvent(
            trace = trace,
            channel = channel,
            target = target,
            immediate = immediate,
        ) { event ->
            event.cancellation = cancellation
            event.entityServiceMitigationKeyV1Ref = mitigation.ref
        }
    }
}
