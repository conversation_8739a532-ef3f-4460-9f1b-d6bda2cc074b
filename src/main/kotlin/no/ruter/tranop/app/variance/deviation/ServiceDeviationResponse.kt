package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.variance.common.ServiceVarianceResponse
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetDiff

class ServiceDeviationResponse(
    targets: JourneyTargetDiff,
) : ServiceVarianceResponse(
        targets,
    ) {
    companion object {
        val NONE = ServiceDeviationResponse(targets = JourneyTargetDiff.EMPTY)
    }
}
