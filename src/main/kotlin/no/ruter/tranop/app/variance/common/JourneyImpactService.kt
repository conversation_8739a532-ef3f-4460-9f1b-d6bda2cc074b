package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.DateTimeRange
import no.ruter.tranop.app.common.time.TimeUtils
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.variance.common.spec.JourneyId
import no.ruter.tranop.app.variance.common.spec.JourneySpec
import no.ruter.tranop.app.variance.common.spec.target.JourneyLineSpecWindowTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneySpecWindowOptionTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargets
import no.ruter.tranop.app.variance.common.spec.target.StopPointSpecWindowTarget
import no.ruter.tranop.app.variance.common.spec.window.JourneyLineSpecWindow
import no.ruter.tranop.app.variance.common.spec.window.JourneySpecWindowOption
import no.ruter.tranop.app.variance.common.spec.window.StopPointSpecWindow
import no.ruter.tranop.common.dto.model.DTODateTimeRange
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
import no.ruter.tranop.operation.common.dto.value.DTOJourneyDirectionCode
import org.jooq.Condition
import org.jooq.Field
import org.springframework.stereotype.Service

@Service
class JourneyImpactService(
    val journeyRepo: DatedJourneyRepository,
) {
    val table = journeyRepo.table
    val impactValidator = ServiceImpactValidator()

    fun findJourneyTargets(
        type: RecordType<*, *, *>,
        impact: DTOServiceImpact,
        duration: DTODateTimeRange? = null,
    ): JourneyTargets {
        val context = impactValidator.validateImpact(type, impact = impact, duration = duration)
        return findJourneyTargets(context)
    }

    fun findJourneyTargets(context: ServiceImpactContext): JourneyTargets {
        val res = JourneyTargets(context)
        if (!context.valid) {
            return res
        }

        val path = context.path
        context.journeys.forEachIndexed { i, journey ->
            findJourneys(path = "$path.journeys[i]", option = journey)?.let {
                res.journeys.add(it)
            }
        }
        context.lines.forEachIndexed { i, line ->
            findLineJourneys(path = "$path.lines[i]", line = line).let {
                res.lineJourneys.add(it)
            }
        }
        context.stopPoints.forEachIndexed { i, stop ->
            findStopPointJourneys(path = "$path.lines[i]", stop = stop).let {
                res.stopPointJourneys.add(it)
            }
        }
        return res
    }

    private fun findJourneys(
        path: String,
        option: JourneySpecWindowOption,
    ): JourneySpecWindowOptionTarget? {
        val journey = option.journey ?: throw IllegalArgumentException("missing required journey option journey: $path.journey is null")

        val journeySpec = journey.spec ?: throw IllegalArgumentException("missing required journey spec: $path.spec is null")
        val journeyIdCondition = resolveJourneyCondition(path, journeySpec)

        // TODO: Support filtering on duration.
        val combinedConditions = AbstractQueryBuilder.and(journeyIdCondition, window(journey.window))
        return journeyRepo.findRefs(combinedConditions).firstOrNull()?.let {
            JourneySpecWindowOptionTarget(ref = it, option = option)
        } // TODO: Handle multiple matches.
    }

    private fun resolveJourneyCondition(
        path: String,
        spec: JourneySpec,
    ): Condition {
        val id = spec.journeyId ?: throw IllegalArgumentException("missing required journey id: $path.journeyId is null")
        val type = id.type

        fun check(field: Field<String>): Condition {
            val v1 = id.value
            val v2 = id.rawValue
            return if (v1 == v2) field.eq(v1) else field.eq(v1).or(field.eq(v2))
        }

        // TODO: Fire out what rules to actually apply here...
        var cond: Condition? =
            when (type) {
                JourneyId.Type.DATED_JOURNEY_ID -> {
                    check(table.REF)
                }
                JourneyId.Type.DATED_SERVICE_JOURNEY_ID -> {
                    val c1 = check(table.DATED_JOURNEY_V1_REF)
                    val c2 = check(table.EXTERNAL_JOURNEY_REF)
                    val c3 = check(table.EXTERNAL_JOURNEY_REF_V2)
                    val c4 = check(table.DATED_SERVICE_JOURNEY_ID)
                    c1.or(c2).or(c3).or(c4)
                }
                JourneyId.Type.UNKNOWN, JourneyId.Type.SERVICE_JOURNEY_ID, JourneyId.Type.VEHICLE_JOURNEY_ID -> {
                    val c1 = check(table.VEHICLE_JOURNEY_ID)
                    val c2 = check(table.DATED_JOURNEY_V1_REF)
                    val c3 = check(table.EXTERNAL_JOURNEY_REF)
                    val c4 = check(table.EXTERNAL_JOURNEY_REF_V2)
                    c1.or(c2).or(c3).or(c4)
                }
            }

        if (type.lineRequired) {
            val line =
                spec.lineId?.let { lineId ->
                    table.LINE_ID.eq(lineId)
                } ?: throw IllegalArgumentException("missing required line id: $path.lineId is null")
            cond = AbstractQueryBuilder.and(cond, line)
        }
        if (type.departureRequired) {
            val departure =
                spec.departureTime?.let { dep ->
                    // Accept any departure within the same minute as the specified departure time.
                    val c1 = TimeUtils.removeSeconds(dep)
                    val c2 = TimeUtils.removeSeconds(dep.plusMinutes(1))
                    table.FIRST_DEPARTURE.ge(c1).and(table.FIRST_DEPARTURE.lt(c2))
                } ?: throw IllegalArgumentException("missing required departure time: $path.departureTime is null")
            cond = AbstractQueryBuilder.and(cond, departure)
        }
        return cond ?: throw IllegalStateException("unexpected null condition (internal error)")
    }

    private fun findLineJourneys(
        path: String,
        line: JourneyLineSpecWindow,
    ): JourneyLineSpecWindowTarget {
        val spec = line.spec ?: throw IllegalArgumentException("missing required line spec: $path.spec is null")

        // TODO: Support filtering on duration.
        // Note: We assume DTOJourneyDirectionCode == DTODatedJourneyDirectionCode!
        val lineId =
            spec.lineId?.let {
                table.LINE_ID.eq(it)
            } ?: throw IllegalArgumentException("missing required line id: $path.spec.lineId is null")
        val direction =
            spec.direction?.let { d ->
                if (d != DTOJourneyDirectionCode.ANY) {
                    table.JOURNEY_DIRECTION_CODE.eq(d.value)
                } else {
                    null
                }
            }
        val lineDirectionWindow = AbstractQueryBuilder.and(lineId, direction)
        val lineJourneyConditions = AbstractQueryBuilder.and(lineDirectionWindow, window(line.window))

        val refs = journeyRepo.findRefs(lineJourneyConditions)
        return JourneyLineSpecWindowTarget(refs = refs, specWindow = line)
    }

    private fun findStopPointJourneys(
        path: String,
        stop: StopPointSpecWindow,
    ): StopPointSpecWindowTarget {
        val res = LinkedHashSet<String>()
        val spec = stop.spec ?: throw IllegalArgumentException("missing required stop point spec: $path.spec is null")
        val window = stop.window

        val externalRefs = LinkedHashSet<String>()
        spec.quayId?.let { externalRefs.add(it) } ?: spec.stopPointId?.let { externalRefs.add(it) }
        if (externalRefs.isEmpty()) {
            val msg = "missing required quay id or stop point id: $path.spec.quayId or $path.spec.stopPointId is null"
            throw IllegalArgumentException(msg)
        }

        // TODO: Support filtering on duration.
        val refs = journeyRepo.externalRefRepo.findOwners(externalRefs)
        if (window == null) {
            res.addAll(refs) // No need to filter on departure / arrival, return owner refs directly.
        } else {
            val c = AbstractQueryBuilder.and(table.REF.`in`(refs), window(window))
            res.addAll(journeyRepo.findRefs(c))
        }
        return StopPointSpecWindowTarget(refs = res, specWindow = stop)
    }

    private fun window(window: DateTimeRange?): Condition? {
        if (window == null) return null
        val end = window.end?.let { table.LAST_ARRIVAL.le(it) }
        val start = window.start?.let { table.FIRST_DEPARTURE.ge(it) }
        return AbstractQueryBuilder.and(end, start)
    }
}
