package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.app.variance.common.db.JourneyTargetMapper
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRecordData
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetCollection
import no.ruter.tranop.assignmentmanager.db.sql.tables.MitigationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation

class InternalServiceMitigation(
    data: DTOServiceMitigation,
    record: MitigationRecord,
) : ServiceVarianceRecordData<
        MitigationTable,
        MitigationRecord,
        DTOServiceMitigation,
    >(RecordType.SERVICE_MITIGATION, data, record) {
    val ownerId: String?
        get() = data.header?.ownerId

    override val dataRef: String?
        get() = data.ref

    override val resolvedJourneyTargets: JourneyTargetCollection
        get() = JourneyTargetMapper.mapTargets(record.journeyTargets)
}
