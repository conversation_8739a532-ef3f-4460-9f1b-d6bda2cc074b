package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.variance.common.db.ServiceVarianceTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord

class ServiceDeviationQueryBuilder :
    BaseRecordQueryBuilder<
        DeviationTable,
        DeviationRecord,
        ServiceVarianceTableMetadata<DeviationTable, DeviationRecord>,
        ServiceDeviationQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    var query: String? = null

    fun query(query: String?): ServiceDeviationQueryBuilder {
        this.query = query
        return this
    }

    companion object {
        val TYPE = RecordType.SERVICE_DEVIATION
    }
}
