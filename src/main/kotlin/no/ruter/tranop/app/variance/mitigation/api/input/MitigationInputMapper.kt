package no.ruter.tranop.app.variance.mitigation.api.input

import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.common.api.adt.v4.input.AbstractADTv4APIInputMapper
import no.ruter.tranop.assignment.adt.v4.model.APIMetadataEntry
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceMitigationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationParameters
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
import no.ruter.tranop.operation.common.dto.model.metadata.DTOServiceVarianceMetadataEntry
import no.ruter.tranop.operation.common.dto.value.DTOServiceVarianceMetadataKey

class MitigationInputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractADTv4APIInputMapper(timeService, insightService) {
    companion object {
        private val MITIGATION_CODES =
            mapOf(
                APIServiceMitigationCode.CANCELLATION to DTOServiceMitigationCode.CANCELLATION,
                APIServiceMitigationCode.REPLACEMENT_SERVICE to DTOServiceMitigationCode.REPLACEMENT_SERVICE,
                APIServiceMitigationCode.STANDBY_VEHICLE_PLANNED to DTOServiceMitigationCode.STANDBY_VEHICLE_PLANNED,
            )
    }

    fun mapInput(context: ADTv4InputContext<APIServiceMitigationPostRequest>): DTOServiceMitigationSpec {
        val path = "$.spec"
        val input = context.request
        val outputSpec =
            input?.spec.notNull(
                context = context,
                path = path,
                insight = path,
                errorType = MappingDetails.Type.ERROR,
            )
        val output =
            DTOServiceMitigationSpec().apply {
                this.code = mapMitigationCode(outputSpec?.code, path = "$path.code", context = context)
                this.impact = outputSpec?.impact?.let { mapImpact(it, path = "$path.impact", context = context) }
                this.duration = outputSpec?.duration?.let { mapDuration(it, path = "$path.duration", context = context) }
                this.mitigates = outputSpec?.mitigates.orEmpty()
                this.metadata = context.mapList(outputSpec?.metadata, path = "$path.metadata") { e, _, _ -> mapMetadataEntry(e) }
                this.parameters = outputSpec?.parameters?.let { mapParameters(it) }
            }
        return output
    }

    private fun mapMitigationCode(
        input: String?,
        path: String,
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
    ): DTOServiceMitigationCode? {
        val errorType = MappingDetails.Type.ERROR
        val apiCode =
            input
                .notNull(
                    context = context,
                    path = path,
                    insight = path,
                    errorType = errorType,
                )?.let(APIServiceMitigationCode::of)
        return apiCode?.let {
            val allowed = MITIGATION_CODES
            val dtoCode = allowed[it]
            if (dtoCode == null) {
                val msg = "invalid or unsupported value [$input]: allowed values are [${allowed.keys.joinToString(separator = ", ")}]"
                context.add(errorType, path, msg = msg, reason = APIStatusResponseReasonCode.MITIGATION_CODE_ERROR.value)
            }
            return dtoCode
        }
    }

    private fun mapParameters(input: APIServiceMitigationParameters): DTOServiceMitigationParameters =
        DTOServiceMitigationParameters().apply {
            this.vehicleId = input.vehicleId
            this.transportMode = input.transportMode
        }

    private fun mapMetadataEntry(input: APIMetadataEntry): DTOServiceVarianceMetadataEntry? {
        val key = input.key?.trim()
        return if (key.isNullOrEmpty()) {
            null
        } else {
            DTOServiceVarianceMetadataEntry().apply {
                this.key = DTOServiceVarianceMetadataKey.of(key)
                this.value = input.value
            }
        }
    }

    override val inputDesc: String
        get() = "api service mitigation" // TODO: Implement this.
    override val outputDesc: String
        get() = "dto service mitigation" // TODO: Implement this.

    override fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String?,
        cause: Exception?,
    ) {
        // TODO: Implement this.
    }
}
