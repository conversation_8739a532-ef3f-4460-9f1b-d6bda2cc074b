package no.ruter.tranop.app.variance.common.spec.target

import no.ruter.tranop.app.variance.common.ServiceImpactContext

class JourneyTargets(
    val context: ServiceImpactContext,
) {
    var journeys = ArrayList<JourneySpecWindowOptionTarget>()
    var lineJourneys = ArrayList<JourneyLineSpecWindowTarget>()
    var stopPointJourneys = ArrayList<StopPointSpecWindowTarget>()

    fun getCombinedTargets(): JourneyTargetCollection {
        val res = ArrayList<JourneyTarget>()
        val keys = LinkedHashSet<String>()

        fun add(target: JourneyTarget) {
            res.add(target)
            keys.add(target.ref)
        }

        journeys.forEach { journey ->
            val calls = journey.option.calls.mapNotNull { it.spec }
            add(JourneyTarget(journey.ref, calls))
        }
        lineJourneys.forEach { line ->
            line.refs.forEach { add(JourneyTarget(it)) }
        }
        stopPointJourneys.forEach { stop ->
            stop.refs.forEach { add(JourneyTarget(it)) }
        }
        return JourneyTargetCollection(refs = keys, journeys = res)
    }
}
