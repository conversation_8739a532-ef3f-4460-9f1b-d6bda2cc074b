package no.ruter.tranop.app.variance.deviation.output

import com.fasterxml.jackson.databind.JsonNode
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.bi.model.BIDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

/**
 * Client for streaming ingestion of service deviations into Snowflake
 *
 * @property config Configuration for streaming ingest
 * @property clientFactory Factory for creating Snowflake clients and channels
 */
@ConditionalOnProperty(prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX, name = ["enabled"], havingValue = "true")
@Service
class ServiceDeviationSnowflakeIngestClient(
    connection: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<DTOServiceDeviation>(
        connection,
        clientFactory,
        CLIENT_BUILDER_NAME,
        CHANNEL_BUILDER_NAME,
    ) {
    override fun DTOServiceDeviation.toIngestMap(): Map<String, Any?> {
        val trace = this.trace
        val ownerId = trace.ownerId
        val operatorId = trace.operatorId
        val authorityId = trace.authorityId

        val spec = this.spec
        val impact = spec.impact
        val stopPoints = impact.stopPoints
        val quayRefs = stopPoints?.mapNotNull { it.spec?.quayId }
        val stopPointRefs = stopPoints?.mapNotNull { it.spec?.stopPointId }
        val datedJourneyV2Refs = impact.journeys?.mapNotNull { it.journey?.spec?.journeyId }
        val lineRefs = impact.lines?.mapNotNull { it.spec?.lineId }

        val jsonData: JsonNode? = JsonUtils.toJsonNode(this)
        if (jsonData == null) {
            return emptyMap()
        }

        return BIDeviation(
            ownerId = ownerId,
            operatorId = operatorId,
            authorityId = authorityId,
            quayRefs = quayRefs,
            stopPointRefs = stopPointRefs,
            datedJourneyV2Refs = datedJourneyV2Refs,
            lineRefs = lineRefs,
            jsonData = jsonData,
        ).toMap()
    }

    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATIONS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATION_CHANNEL"
    }
}
