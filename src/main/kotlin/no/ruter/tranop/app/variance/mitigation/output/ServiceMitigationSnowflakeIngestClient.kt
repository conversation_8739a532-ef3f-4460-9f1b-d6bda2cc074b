//package no.ruter.tranop.app.variance.mitigation.output
//
//import no.ruter.rdp.common.json.JsonUtils
//import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeIngestClientConstants
//import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
//import no.ruter.tranop.common.bi.model.BIServiceMitigationCode
//import no.ruter.tranop.operation.mitigation.bi.model.BIDateTimeRange
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceImpact
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigation
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigationParameters
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigationSpec
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceVarianceMetadataEntry
//import no.ruter.tranop.operation.mitigation.bi.model.BIServiceVarianceTraceInfo
//import org.springframework.stereotype.Service
//
///**
// * Client for streaming ingestion of service mitigations into Snowflake.
// *
// * @property config Configuration for streaming ingest.
// * @property clientFactory Factory for creating Snowflake clients and channels.
// */
//@Service
//class ServiceMitigationSnowflakeIngestClient(
//    config: SnowflakeClientProperties,
//    clientFactory: SnowflakeClientFactory,
//) : AbstractSnowflakeIngestClient<BIServiceMitigation>(
//        config,
//        clientFactory,
//        CLIENT_BUILDER_NAME,
//        CHANNEL_BUILDER_NAME,
//    ) {
//    companion object {
//        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATIONS"
//        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATION_CHANNEL"
//    }
//
//    override fun BIServiceMitigation.toIngestMap(): Map<String, Any> =
//        mapOf(
//            SnowflakeIngestClientConstants.COL_REF to ref,
//            SnowflakeIngestClientConstants.COL_OWNER_ID to trace.ownerId,
//            SnowflakeIngestClientConstants.COL_AUTHORITY_ID to trace.authorityId,
//            SnowflakeIngestClientConstants.COL_OPERATOR_ID to trace.operatorId,
//            SnowflakeIngestClientConstants.COL_QUAY_REFS to spec.impact.stopPoints,
//            SnowflakeIngestClientConstants.COL_DATED_JOURNEY_V2_REFS to spec.impact.journeys,
//            SnowflakeIngestClientConstants.COL_LINE_REFS to spec.impact.lines,
//            SnowflakeIngestClientConstants.COL_JSON_DATA to JsonUtils.toJson(this),
//        )
//}
//
//fun InternalServiceMitigation.toBIServiceMitigation(): BIServiceMitigation =
//    with(data) {
//        BIServiceMitigation(
//            ref,
//            spec?.let { spec ->
//                BIServiceMitigationSpec(
//                    spec.code?.value?.let { BIServiceMitigationCode.of(it) }, // TODO: Change type to String in BI model.
//                    BIServiceImpact(),
//                    spec.duration?.let {
//                        BIDateTimeRange(
//                            it.start,
//                            it.end,
//                        )
//                    },
//                    spec.mitigates,
//                    spec.metadata.map {
//                        BIServiceVarianceMetadataEntry(
//                            it.key.toString(),
//                            it.value,
//                        )
//                    },
//                    spec.parameters?.let {
//                        BIServiceMitigationParameters(
//                            it.vehicleId,
//                            it.transportMode,
//                        )
//                    },
//                )
//            },
//            BIServiceVarianceTraceInfo(
//                ownerId,
//                operatorId,
//                authorityId,
//            ),
//        )
//    }
