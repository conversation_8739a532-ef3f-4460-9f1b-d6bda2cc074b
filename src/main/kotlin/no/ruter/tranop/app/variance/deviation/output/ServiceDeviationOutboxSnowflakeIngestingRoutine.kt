package no.ruter.tranop.app.variance.deviation.output

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.event.journey.lifecycle.JourneyEventLifeCycleConfig
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component

@ConditionalOnProperty(prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX, name = ["enabled"], havingValue = "true")
@Component
class ServiceDeviationOutboxSnowflakeIngestingRoutine(
    outboxService: OutboxService,
    val publisher: ServiceDeviationSnowflakeIngestClient,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    lifeCycleConfig: JourneyEventLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
    DeviationTable,
    DeviationRecord,
    DTOServiceDeviation,
    DTOServiceDeviation,
    >(
    name = LC_TYPE_OUTBOX_PUBLISH,
    recordType = RecordType.SERVICE_DEVIATION,
    targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
    payloadClass = DTOServiceDeviation::class,
    outboxService = outboxService,
    insightService = insightService,
    appInfoProperties = appInfoProperties,
    abstractLifeCycleConfig = lifeCycleConfig,
) {
    // TODO: Mapping is performed in the ingest client ServiceDeviationSnowflakeIngestClient. Make mappers and move the logic here
    override fun map(
        data: DTOServiceDeviation?,
        context: MappingContext,
    ): DTOServiceDeviation? = data

    override fun publish(
        messages: List<OutboxOutputMessage<DTOServiceDeviation>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        try {
            val events = messages.map { Pair(it.ref, it.value) }
            publisher.ingest(events, throwExceptionOnError = true)
            postProcessing(null)
        } catch (e: Exception) {
            postProcessing(e)
        }
    }

    override fun metadata(data: DTOServiceDeviation?): Map<String, Any?> = emptyMap()
}
