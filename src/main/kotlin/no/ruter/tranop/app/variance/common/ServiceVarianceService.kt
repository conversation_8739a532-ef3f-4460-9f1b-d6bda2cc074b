package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRepository
import no.ruter.tranop.app.variance.common.db.ServiceVarianceTableMetadata
import no.ruter.tranop.assignment.common.db.record.base.BaseRecord
import no.ruter.tranop.assignment.common.db.record.json.JSONRecord
import org.jooq.RecordMapper
import org.jooq.Table
import org.jooq.UpdatableRecord

class ServiceVarianceService<
    T : Table<R>,
    R,
    D,
    E : JSONRecordData<T, R, D>,
    M : RecordMapper<R, E>,
    Q : BaseRecordQueryBuilder<T, R, V, Q>,
    V : ServiceVarianceTableMetadata<T, R>,
    X : ServiceVarianceRepository<T, R, D, E, M, Q, V>,
>(
    val repo: X,
) where R : JSONRecord, R : UpdatableRecord<R> {
    fun updateResolvedJourneyTargets(
        target: JSONRecordData<*, *, *>,
        response: ServiceVarianceResponse,
    ) {
        response.includedJourneyRefs
            .let { ExternalRefs().add(ExternalRefType.DATED_JOURNEY_V2_REF, it) }
            .let { refs ->
                repo.externalRefRepo.update(
                    record = target.record,
                    externalRefs = mapOf(target.ref to refs),
                    clearOnUpdate = true,
                )
            }

        target.ref?.let { ref ->
            repo.updateJourneyTargets(ref, response.targets.included)
        }
    }
}
