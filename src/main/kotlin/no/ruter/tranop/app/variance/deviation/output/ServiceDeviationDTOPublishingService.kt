package no.ruter.tranop.app.variance.deviation.output

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.variance.deviation.config.ServiceDeviationConfigProperties
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationRepository
import no.ruter.tranop.journey.deviation.dto.kafka.DTOServiceDeviationSerde
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class ServiceDeviationDTOPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    serviceDeviationConfigProperties: ServiceDeviationConfigProperties,
    private val repository: ServiceDeviationRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DTOServiceDeviation,
        DTOServiceDeviationSerde,
        >(
        producerBinding = kafkaConfigService.serviceDeviationDtoOutputProducer,
        config = serviceDeviationConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.Companion.CONFIG_KEY_DTO),
        insightService = insightService,
        keyType = "ENTITY_DEVIATION_KEY_V2", // TODO: Update log keys
    ) {
    fun publish(
        input: DTOServiceDeviation,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()

        publishToKafka(input.ref, input) { exception ->
            when (exception) {
                null -> {
                    // TODO should this function be responsible for this or it should be moved one lvl up
//                    val nPublished = repository.markDTOPublished(input.ref, currentRevision)
//                    future.complete(Result.success(nPublished ?: 0))
                }

                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }
}
