package no.ruter.tranop.app.variance.deviation.api.input

import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.common.api.adt.v4.input.AbstractADTv4APIInputMapper
import no.ruter.tranop.assignment.adt.v4.model.APIMetadataEntry
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReason
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationParameters
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import no.ruter.tranop.journey.deviation.dto.model.common.DTOServiceDeviationReason
import no.ruter.tranop.operation.common.dto.model.metadata.DTOServiceVarianceMetadataEntry
import no.ruter.tranop.operation.common.dto.value.DTOServiceVarianceMetadataKey

class DeviationInputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractADTv4APIInputMapper(timeService, insightService) {
    companion object {
        private val DEVIATION_CODES =
            mapOf(
                APIServiceDeviationCode.DELAY to DTOServiceDeviationCode.DELAY,
                APIServiceDeviationCode.BYPASS to DTOServiceDeviationCode.BYPASS,
                APIServiceDeviationCode.NO_SERVICE to DTOServiceDeviationCode.NO_SERVICE,
                APIServiceDeviationCode.NO_SIGN_ON to DTOServiceDeviationCode.NO_SIGN_ON,
            )
    }

    fun mapInput(context: ADTv4InputContext<APIServiceDeviationPostRequest>): DTOServiceDeviationSpec {
        val path = "$.spec"
        val input = context.request
        val outputSpec =
            input?.spec.notNull(
                context = context,
                path = path,
                insight = path,
                errorType = MappingDetails.Type.ERROR,
            )
        val output =
            DTOServiceDeviationSpec().apply {
                this.code = mapDeviationCode(outputSpec?.code, path = "$path.code", context = context)
                this.reason = outputSpec?.reason?.let { mapReason(it) }
                this.impact = outputSpec?.impact?.let { mapImpact(it, path = "$path.impact", context = context) }
                this.duration = outputSpec?.duration?.let { mapDuration(it, path = "$path.duration", context = context) }
                this.metadata = context.mapList(outputSpec?.metadata, path = "$path.metadata") { e, _, _ -> mapMetadataEntry(e) }
                this.parameters = outputSpec?.parameters?.let { mapParameters(it) }
            }
        return output
    }

    private fun mapDeviationCode(
        input: String?,
        path: String,
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
    ): DTOServiceDeviationCode? {
        val errorType = MappingDetails.Type.ERROR
        val apiCode =
            input
                .notNull(
                    context = context,
                    path = path,
                    insight = path,
                    errorType = errorType,
                )?.let(APIServiceDeviationCode::of)
        return apiCode?.let {
            val allowed = DEVIATION_CODES
            val dtoCode = allowed[it]
            if (dtoCode == null) {
                val msg = "invalid or unsupported value [$input]: allowed values are [${allowed.keys.joinToString(separator = ", ")}]"
                context.add(errorType, path, msg = msg, reason = APIStatusResponseReasonCode.DEVIATION_CODE_ERROR.value)
            }
            return dtoCode
        }
    }

    private fun mapReason(input: APIServiceDeviationReason): DTOServiceDeviationReason =
        DTOServiceDeviationReason().apply {
            this.code = input.code
            this.comment = input.comment
        }

    private fun mapParameters(input: APIServiceDeviationParameters): DTOServiceDeviationParameters =
        DTOServiceDeviationParameters().apply {
            this.vehicleId = input.vehicleId
            this.delayMinutes = input.delayMinutes
            this.operatorExempt = input.operatorExempt
        }

    private fun mapMetadataEntry(input: APIMetadataEntry): DTOServiceVarianceMetadataEntry? {
        val key = input.key?.trim()
        return if (key.isNullOrEmpty()) {
            null
        } else {
            DTOServiceVarianceMetadataEntry().apply {
                this.key = DTOServiceVarianceMetadataKey.of(key)
                this.value = input.value
            }
        }
    }

    override val inputDesc: String
        get() = "api service deviation" // TODO: Implement this.
    override val outputDesc: String
        get() = "dto service deviation" // TODO: Implement this.

    override fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String?,
        cause: Exception?,
    ) {
        // TODO: Implement this.
    }
}
