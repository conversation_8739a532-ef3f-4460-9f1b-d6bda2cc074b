package no.ruter.tranop.app.variance.common.api.adt

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.mapping.MapperUtils

class ADTAuthTrace(
    val xADTAuth: String?,
    val xTraceId: String?,
    val xRequestId: String?,
    val xOperatorId: String?,
    val xAuthorityId: String?,
) : TraceInfo {
    override val traceId: String = xTraceId ?: MapperUtils.randomId()
    override val requestId: String = xRequestId ?: MapperUtils.randomId()
    override val operatorId: String? = xOperatorId
    override val authorityId: String? = xAuthorityId

    companion object {
        val NONE = ADTAuthTrace(xADTAuth = null, xTraceId = null, xRequestId = null, xOperatorId = null, xAuthorityId = null)
    }
}
