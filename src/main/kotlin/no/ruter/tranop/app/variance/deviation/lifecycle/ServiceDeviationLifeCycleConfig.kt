package no.ruter.tranop.app.variance.deviation.lifecycle

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import no.ruter.tranop.app.variance.deviation.config.ServiceDeviationConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = ServiceDeviationLifeCycleConfig.CONF_PREFIX)
class ServiceDeviationLifeCycleConfig : AbstractLifeCycleConfig(CONF_PREFIX) {
    companion object {
        const val CONF_PREFIX = "${ServiceDeviationConfigProperties.CONF_PREFIX}.lifecycle"
    }
}
