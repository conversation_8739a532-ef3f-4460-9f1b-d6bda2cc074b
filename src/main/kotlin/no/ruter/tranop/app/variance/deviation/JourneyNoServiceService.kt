package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.QuayRefTargets
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventOmissionReason
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOmission
import org.springframework.stereotype.Service

@Service
class JourneyNoServiceService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceDeviationHandler {
    companion object {
        val REASONS =
            mapOf(
                ServiceVarianceRequest.Type.CREATE to DTOJourneyEventOmissionReason.NO_INTENTION,
                ServiceVarianceRequest.Type.UPDATE to DTOJourneyEventOmissionReason.NO_INTENTION,
                ServiceVarianceRequest.Type.DELETE to DTOJourneyEventOmissionReason.UN_OMITTED,
            )
    }

    override fun processDeviationRequest(request: ServiceDeviationRequest): ServiceDeviationResponse {
        val type = request.type
        val reason = REASONS[type] ?: throw IllegalStateException("unsupported request type: $type")

        val targets = resolveQuayRefTargets(request)
        val omitted = reason == DTOJourneyEventOmissionReason.NO_INTENTION
        val deviation = request.modified ?: request.stored ?: return ServiceDeviationResponse.NONE

        val immediate = true
        val targetJourneys = targets.journeys

        // Apply no service on journeys no longer included in target.
        targetJourneys.included.forEach { target ->
            omitJourney(
                trace = request.trace,
                channel = request.channel,
                target = target,
                deviation = deviation,
                omitted = omitted,
                targets = targets,
                immediate = immediate,
            )
        }

        // Recall no service on journeys no longer included in target.
        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targetJourneys.excluded.forEach { target ->
                omitJourney(
                    trace = request.trace,
                    channel = request.channel,
                    target = target,
                    deviation = deviation,
                    omitted = false,
                    targets = targets,
                    immediate = immediate,
                )
            }
        }

        return ServiceDeviationResponse(targets = targetJourneys)
    }

    fun omitJourney(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        deviation: InternalServiceDeviation,
        omitted: Boolean,
        targets: QuayRefTargets?,
        immediate: Boolean,
    ) {
        val calls = target.calls?.map { call -> mapJourneyEventCall(call, targets) }
        val omission =
            DTOJourneyEventOmission().apply {
                this.calls = calls
                this.omitted = omitted
                this.lastArrivalDateTime = lastArrivalDateTime
                this.firstDepartureDateTime = firstDepartureDateTime
            }

        storeJourneyEvent(
            trace = trace,
            channel = channel,
            target = target,
            immediate = immediate,
        ) { event ->
            event.omission = omission
            event.entityServiceDeviationKeyV1Ref = deviation.ref
        }
    }
}
