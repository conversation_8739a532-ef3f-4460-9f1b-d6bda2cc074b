package no.ruter.tranop.app.variance.deviation.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.deviation.output.ServiceDeviationBIOutboxPublishingRoutine
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = ServiceDeviationLifeCycleConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class ServiceDeviationLifeCycle(
    val timeService: TimeService,
    serviceDeviationBIOutboxPublishingRoutine: ServiceDeviationBIOutboxPublishingRoutine,
) : AbstractLifeCycle(
        routines =
            listOf(
                serviceDeviationBIOutboxPublishingRoutine,
            ),
    ) {
    companion object {
        const val FREQ_PREFIX = "${ServiceDeviationLifeCycleConfig.CONF_PREFIX}.frequent"
    }

    @Scheduled(
        fixedRateString = "\${$FREQ_PREFIX.fixedRate}",
        initialDelayString = "\${$FREQ_PREFIX.initialDelay}",
    )
    @SchedulerLock(
        name = "\${$FREQ_PREFIX.schedulerLockName}",
        lockAtMostFor = "\${$FREQ_PREFIX.lockAtMostFor}",
        lockAtLeastFor = "\${$FREQ_PREFIX.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }
}
