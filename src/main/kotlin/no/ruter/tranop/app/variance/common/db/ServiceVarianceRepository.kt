package no.ruter.tranop.app.variance.common.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.assignment.common.db.record.json.JSONRecord
import org.jooq.DSLContext
import org.jooq.RecordMapper
import org.jooq.SortField
import org.jooq.Table
import org.jooq.UpdatableRecord

abstract class ServiceVarianceRepository<
    T : Table<R>,
    R,
    D,
    E : JSONRecordData<T, R, D>,
    M : RecordMapper<R, E>,
    Q : BaseRecordQueryBuilder<T, R, V, Q>,
    V : ServiceVarianceTableMetadata<T, R>,
>(
    recordType: RecordType<T, R, V>,
    dslContext: DSLContext,
    sortFields: List<SortField<out Any>>,
    recordMapper: M,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<T, R, E, M, Q, V>(
        recordType = recordType,
        dslContext = dslContext,
        sortFields = sortFields,
        timeService = timeService,
        recordMapper = recordMapper,
        insightService = insightService,
    ) where R : JSONRecord, R : UpdatableRecord<R> {
    fun fetchByRef(
        ref: String?,
        trace: TraceInfo? = null,
        restricted: Boolean = true,
        includeDeleted: Boolean = !restricted,
    ): E? {
        val b = queryBuilder().ref(ref).authorized(trace, restricted, includeDeleted)
        return fetch(b).firstOrNull()
    }

    fun updateJourneyTargets(
        ref: String,
        targets: List<JourneyTarget>,
    ): Boolean {
        val json = JourneyTargetMapper.mapTargets(targets)
        val res =
            dslContext
                .update(table)
                .set(tableMeta.journeyTargets, json)
                .where(tableMeta.ref.eq(ref))
                .execute()
        return res == 1
    }
}
