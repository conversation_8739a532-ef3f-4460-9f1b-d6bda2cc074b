package no.ruter.tranop.app.variance.deviation.api

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.app.variance.common.ServiceImpactValidator
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.ServiceVarianceService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.deviation.JourneyBypassService
import no.ruter.tranop.app.variance.deviation.JourneyDelayService
import no.ruter.tranop.app.variance.deviation.JourneyNoServiceService
import no.ruter.tranop.app.variance.deviation.JourneyNoSignOnService
import no.ruter.tranop.app.variance.deviation.ServiceDeviationHandler
import no.ruter.tranop.app.variance.deviation.ServiceDeviationRequest
import no.ruter.tranop.app.variance.deviation.api.input.DeviationInputMapper
import no.ruter.tranop.app.variance.deviation.api.output.DeviationOutputMapper
import no.ruter.tranop.app.variance.deviation.config.ServiceDeviationOutputConfig
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationQueryBuilder
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationRepository
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service

@Service
class ADTv4DeviationService(
    val repo: ServiceDeviationRepository,
    timeService: TimeService,
    insightService: InsightService,
    delayService: JourneyDelayService,
    bypassService: JourneyBypassService,
    noSignOnService: JourneyNoSignOnService,
    noServiceService: JourneyNoServiceService,
    private val serviceDeviationOutputConfig: ServiceDeviationOutputConfig,
    private val outboxRepository: OutboxRepository,
) {
    private val log = LoggerFactory.getLogger(javaClass)
    private val requestService = ServiceVarianceService(repo)

    private val inputMapper = DeviationInputMapper(timeService, insightService)
    private val impactValidator = ServiceImpactValidator()

    private val outputMapper = DeviationOutputMapper(timeService, insightService)

    private val handlers: Map<DTOServiceDeviationCode, ServiceDeviationHandler> =
        mapOf(
            DTOServiceDeviationCode.DELAY to delayService,
            DTOServiceDeviationCode.BYPASS to bypassService,
            DTOServiceDeviationCode.NO_SIGN_ON to noSignOnService,
            DTOServiceDeviationCode.NO_SERVICE to noServiceService,
        )

    private fun validateInput(
        type: ServiceVarianceRequest.Type,
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        stored: InternalServiceDeviation?,
    ): ServiceDeviationRequest {
        val input = inputMapper.mapInput(context)
        val impactContext = impactValidator.validateImpact(type = repo.recordType, impact = input.impact, duration = input.duration)
        context.mergeServiceImpactValidationContext(impactContext)

        val request =
            ServiceDeviationRequest(
                type = type,
                trace = context.trace,
                input = input,
                impact = impactContext,
                channel = context.channel,
                stored = stored,
                modified = null,
            )
        resolveRequestHandler(request)?.validateDeviationRequest(request, context)
        if (context.hasErrors) {
            throw IllegalArgumentException("Invalid service deviation request")
        }
        return request
    }

    fun createServiceDeviation(context: ADTv4InputContext<APIServiceDeviationPostRequest>): APIServiceDeviation {
        val type = ServiceVarianceRequest.Type.CREATE
        val request = validateInput(type, context, stored = null)

        val record = repo.newRecord(request.input)
        return processServiceDeviation(context, request, stored = record, modified = null)
    }

    fun updateServiceDeviation(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        serviceDeviationId: String,
    ): APIServiceDeviation {
        val type = ServiceVarianceRequest.Type.UPDATE
        val stored =
            repo.fetchByRef(ref = serviceDeviationId, trace = context.trace) ?: throw APIResourceNotFoundException()

        val request = validateInput(type, context, stored = stored)
        val modified = repo.wrapSpec(ref = serviceDeviationId, spec = request.input, record = stored.record)
        return processServiceDeviation(context = context, request = request, stored = stored, modified = modified)
    }

    private fun processServiceDeviation(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        request: ServiceDeviationRequest,
        stored: InternalServiceDeviation,
        modified: InternalServiceDeviation?,
    ): APIServiceDeviation {
        val target = modified ?: stored

        repo.storeRecord(target, trace = context.trace, now = context.received)
        request.stored = stored
        request.modified = modified
        val apiResponse = outputMapper.mapDeviation(target, context)

        resolveRequestHandler(request)
            ?.processDeviationRequest(request)
            ?.let {
                requestService.updateResolvedJourneyTargets(target, it)
            }
        publishDeviation(target, context)
        return apiResponse
    }

    fun deleteServiceDeviation(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        serviceDeviationId: String,
    ) {
        val type = ServiceVarianceRequest.Type.DELETE
        val stored =
            repo.fetchByRef(
                ref = serviceDeviationId,
                trace = context.trace,
                includeDeleted = true,
            ) ?: throw APIResourceNotFoundException()

        if (stored.deleted) {
            return // Record already deleted, no need to do anything.
        }

        val deleted = repo.setDeleted(ref = stored.record.ref, trace = context.trace, now = context.received)
        if (!deleted) {
            throw APIException(
                httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                msg = "Error deleting deviation $serviceDeviationId: record not found?",
            )
        }

        val input = stored.data.spec
        val impact = // Resolve impact of original deviation, so we can give the handler a chance to revert mitigation when deleted.
            impactValidator.validateImpact(
                type = RecordType.SERVICE_DEVIATION,
                impact = input.impact,
                duration = input.duration,
            )
        val request =
            ServiceDeviationRequest(
                type = type,
                trace = context.trace,
                input = input,
                impact = impact,
                channel = context.channel,
                stored = stored,
                modified = null,
            )
        resolveRequestHandler(request)?.processDeviationRequest(request)
    }

    private fun resolveRequestHandler(request: ServiceDeviationRequest): ServiceDeviationHandler? {
        val code =
            request.input.code
                ?: request
                    .stored
                    ?.data
                    ?.spec
                    ?.code
                ?: request
                    .modified
                    ?.data
                    ?.spec
                    ?.code
        return handlers[code]
    }

    fun readServiceDeviation(
        context: ADTv4InputContext<*>,
        serviceDeviationId: String,
    ): APIServiceDeviation? {
        val deviation = repo.fetchByRef(ref = serviceDeviationId, trace = context.trace)
        return deviation?.let { outputMapper.mapDeviation(it, context) }
    }

    fun findServiceDeviations(
        context: ADTv4InputContext<*>,
        query: ServiceDeviationQueryBuilder,
    ): List<APIServiceDeviation> = repo.fetch(resolveExternalRefFromQuery(query)).map { outputMapper.mapDeviation(it, context) }

    private fun resolveExternalRefFromQuery(serviceDeviationQueryBuilder: ServiceDeviationQueryBuilder): ServiceDeviationQueryBuilder {
        if (serviceDeviationQueryBuilder.query.isNullOrEmpty()) {
            return serviceDeviationQueryBuilder
        }
        val serviceDeviationIds = repo.externalRefRepo.findOwners(listOf(serviceDeviationQueryBuilder.query))

        if (serviceDeviationIds.isNotEmpty()) {
            serviceDeviationQueryBuilder.refs(serviceDeviationIds)
        }
        return serviceDeviationQueryBuilder
    }

    private fun publishDeviation(
        record: InternalServiceDeviation,
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
    ): APIServiceDeviation = outputMapper.mapDeviation(record, context)
}
