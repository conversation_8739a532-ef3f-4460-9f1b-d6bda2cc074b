package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRecordData
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRepository
import no.ruter.tranop.app.variance.common.spec.JourneyCallSpec
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetCollection
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetDiff
import no.ruter.tranop.app.variance.common.spec.target.QuayRefTargets
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.common.DTOJourneyEventCall
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec

abstract class AbstractServiceVarianceHandler(
    val timeService: TimeService,
    val stopPointRepo: StopPointRepository,
    val impactService: JourneyImpactService,
    val journeyEventInputService: JourneyEventInputService,
) {
    protected fun storeJourneyEvent(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        immediate: Boolean,
        eventPopulator: (DTOJourneyEvent) -> Unit,
    ) {
        val now = timeService.now()
        val header =
            DTOMessageHeader().apply {
                traceId = trace.traceId
                messageTimestamp = now.toString()
            }

        val event =
            DTOJourneyEvent().apply {
                this.header = header
                this.entityDatedJourneyKeyV2Ref = target.ref
            }
        eventPopulator.invoke(event)
        journeyEventInputService.store(now, event, channel, process = immediate)
    }

    protected fun resolveQuayId(
        stop: DTOStopPointSpec?,
        targets: QuayRefTargets?,
    ): String = stop?.quayId ?: targets?.quayRefs[stop?.stopPointId] ?: throw IllegalStateException("unable to resolve NSR quay id")

    protected fun mapJourneyEventCall(
        call: JourneyCallSpec,
        targets: QuayRefTargets?,
    ): DTOJourneyEventCall =
        DTOJourneyEventCall().apply {
            this.time = (call.departureTime ?: call.arrivalTime)?.toString()
            this.nsrQuayId = resolveQuayId(call.stopPoint, targets)
        }

    protected fun <I, D, E : ServiceVarianceRecordData<*, *, D>> resolveQuayRefTargets(
        request: ServiceVarianceRequest<I, E>,
    ): QuayRefTargets {
        val combinedTargets = resolveCombinedTargets(request)

        // Resolve quay id for any calls referring to stop points using stop point ref instead of quay id.
        val stopPointRefs = LinkedHashSet<String>()
        combinedTargets.all.forEach { target ->
            target.calls?.forEach { call ->
                call.stopPoint?.let { stopPoint ->
                    if (stopPoint.quayId == null) {
                        stopPoint.stopPointId?.let { stopPointRefs.add(it) }
                    }
                }
            }
        }
        val quayIds = stopPointRepo.resolveQuayIds(stopPointRefs)
        return QuayRefTargets(quayRefs = quayIds, journeys = combinedTargets)
    }

    protected fun <I, D, E : ServiceVarianceRecordData<*, *, D>> resolveCombinedTargets(
        request: ServiceVarianceRequest<I, E>,
    ): JourneyTargetDiff {
        val targets = impactService.findJourneyTargets(request.impact)

        val combinedTargets = targets.getCombinedTargets()
        val originalTargets = request.modified?.let { request.stored?.resolvedJourneyTargets } ?: JourneyTargetCollection.EMPTY
        return if (originalTargets.refs.isEmpty()) {
            val journeys = combinedTargets.journeys
            JourneyTargetDiff(
                all = journeys,
                included = journeys,
                excluded = emptyList(),
            )
        } else {
            val all = ArrayList<JourneyTarget>()
            val included = ArrayList<JourneyTarget>(combinedTargets.journeys.size)
            val excluded = ArrayList<JourneyTarget>(originalTargets.journeys.size)
            combinedTargets.journeys.forEach { target ->
                all.add(target)
                included.add(target)
            }
            originalTargets.journeys.forEach { target ->
                if (!combinedTargets.refs.contains(target.ref)) {
                    all.add(target)
                    excluded.add(target)
                }
            }
            JourneyTargetDiff(
                all = all,
                included = included,
                excluded = excluded,
            )
        }
    }
}
