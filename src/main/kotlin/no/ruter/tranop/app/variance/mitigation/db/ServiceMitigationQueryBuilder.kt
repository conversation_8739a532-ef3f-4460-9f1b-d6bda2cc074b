package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.app.variance.common.db.ServiceVarianceTableMetadata
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationQueryBuilder
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.MitigationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord

class ServiceMitigationQueryBuilder :
    BaseRecordQueryBuilder<
        MitigationTable,
        MitigationRecord,
        ServiceVarianceTableMetadata<MitigationTable, MitigationRecord>,
        ServiceMitigationQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.SERVICE_MITIGATION
    }
}
