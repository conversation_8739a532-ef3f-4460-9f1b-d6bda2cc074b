package no.ruter.tranop.app.variance.mitigation.replacement

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line12TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line13TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line15TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line17TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line18TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line19TramReplacement
import no.ruter.tranop.assignment.util.toIsoString
import no.ruter.tranop.common.dto.model.DTOLifeCycleInfo
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyPlan
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReferences
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyRelatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReplaces
import no.ruter.tranop.dated.journey.dto.model.common.DTODestination
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOLine
import no.ruter.tranop.dated.journey.dto.model.common.DTOStopPointBehaviourType
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.apache.commons.lang3.NotImplementedException

class BusForTramReplacementService(
    private val timeService: TimeService,
    private val infoProperties: AppInfoProperties,
    private val replacementStopPointService: ReplacementStopPointService,
) {
    private val lineReplacementImpls =
        mapOf(
            "RUT:Line:12" to Line12TramReplacement(),
            "RUT:Line:13" to Line13TramReplacement(),
            "RUT:Line:15" to Line15TramReplacement(),
            "RUT:Line:17" to Line17TramReplacement(),
            "RUT:Line:18" to Line18TramReplacement(),
            "RUT:Line:19" to Line19TramReplacement(),
        )

    fun createReplacementJourney(
        plannedJourney: DTODatedJourney,
        traceInfo: TraceInfo,
    ): DTODatedJourney {
        if (plannedJourney.line?.transportMode == DTOTransportMode.TRAM) {
            val lineReplacementImpl =
                lineReplacementImpls[plannedJourney.line.lineRef]
                    ?: throw NotImplementedException(
                        "Replacement for line ${plannedJourney.line?.lineRef} not supported",
                    )

            val replacementJourney = createNewDatedJourney(traceInfo, plannedJourney)
            replacementJourney.line = createReplacementLine(plannedJourney.line, lineReplacementImpl.getReplacementLineRef())
            replacementJourney.ref = MapperUtils.toDatedJourneyV2Ref(replacementJourney) // Ref is needed to generate refs in plan
            val replacementStopCalls = lineReplacementImpl.getReplacementStopCalls(plannedJourney.direction)
            replacementJourney.plan = createReplacementPlan(plannedJourney, replacementJourney, replacementStopCalls, traceInfo)
            replacementJourney.journeyReferences.datedServiceJourneyId = MapperUtils.toDatedServiceJourneyId(replacementJourney)
            return replacementJourney
        } else {
            throw NotImplementedException(
                "Replacement for transport mode ${plannedJourney.line?.transportMode} not supported",
            )
        }
    }

    private fun createNewDatedJourney(
        traceInfo: TraceInfo,
        plannedJourney: DTODatedJourney,
    ): DTODatedJourney {
        val legacyRef = MapperUtils.randomId()
        val externalRef = "${MapperUtils.randomId("RUT:ServiceJourney:")}-extra"
        val now = timeService.now().toIsoString()
        val appName = infoProperties.name

        val replacementJourney =
            DTODatedJourney().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.publishedTimestamp = now
                        this.messageTimestamp = now
                        this.receivedTimestamp = now
                        this.ownerId = MapperUtils.OWNER_ID // TODO: Fix hard-coded owner id.
                        this.publisherId = appName
                        this.traceId = traceInfo.traceId
                        this.originId = appName
                    }

                this.order = 1
                this.events =
                    listOf(
                        DTOEvent().apply {
                            this.type = DTOEventType.CREATED
                            this.source = appName
                            this.traceId = traceInfo.traceId
                            this.timestamp = now
                            this.description = "The journey was created by assignment-journey-manager."
                            this.metadata = emptyList()
                        },
                    )
                this.lifeCycleInfo =
                    DTOLifeCycleInfo().apply {
                        this.dataSource = "RUT"
                        this.created = now
                        this.modified = now
                        this.revision = 0
                    }

                this.public = true
                this.deleted = false
                this.omitted = false
                this.cancelled = false

                this.name = plannedJourney.name
                this.type = plannedJourney.type
                this.vehicleTask = plannedJourney.vehicleTask

                this.journeyReferences =
                    DTODatedJourneyReferences().apply {
                        this.vehicleTaskRef = null
                        this.blockRef = null
                        this.datedBlockRef = null
                        this.journeyRef = null
                        this.vehicleJourneyId = plannedJourney.journeyReferences?.vehicleJourneyId // maybe new id?
                        this.legacyJourneyRef = null
                        this.legacyDatedJourneyRef = legacyRef
                        this.journeyPatternRef = null
                        this.runtimePatternRef = null
                        this.legacyJourneyPatternRefs = null
                        this.externalJourneyRef = externalRef
                        this.externalJourneyRefV2 = externalRef
                    }

                this.operators = plannedJourney.operators
                this.operatorContracts = plannedJourney.operatorContracts
                this.lineage = null
                this.vehicles = null
                this.operatingDate = plannedJourney.operatingDate
                this.direction = plannedJourney.direction

                this.journeyState = JourneyUtils.createJourneyState(omitted = this.omitted, cancelled = this.cancelled)
                this.extra = true
                this.replaces =
                    DTODatedJourneyReplaces().apply {
                        this.journeys =
                            listOf(
                                DTODatedJourneyRelatedJourney().apply {
                                    this.entityDatedJourneyKeyV2Ref = plannedJourney.ref
                                    this.journeyReferences =
                                        DTODatedJourneyReferences().apply {
                                            this.externalJourneyRef = plannedJourney.journeyReferences.externalJourneyRef
                                            this.externalJourneyRefV2 = plannedJourney.journeyReferences.externalJourneyRefV2
                                        }
                                },
                            )
                    }
            }
        return replacementJourney
    }

    private fun createReplacementPlan(
        originalJourney: DTODatedJourney,
        replacementJourney: DTODatedJourney,
        replacementStopCalls: Map<QuayRef, ReplacementStopCall?>,
        traceInfo: TraceInfo,
    ): DTODatedJourneyPlan {
        val replacementStopPoints =
            replacementStopCalls
                .mapValues { (_, v) ->
                    v?.let {
                        replacementStopPointService.findOrCreateStopPoint(
                            quayRef = it.quayRef,
                            legacyQuayRef = it.legacyQuayRef,
                            name = it.name,
                            traceInfo = traceInfo,
                        )
                    }
                }

        val stopPointRefToQuayRefMap: Map<String?, QuayRef?> = originalJourney.plan.stops.associateBy(DTOStopPoint::getRef) { it.quayRef }

        // stoppesteder uten direkte erstatning fjernes fra listene med stops og calls
        val plan = DTODatedJourneyPlan()
        plan.apply {
            this.stops =
                originalJourney.plan.stops
                    .filter { replacementStopPoints[it.quayRef] != null }
                    .map { replacementStopPoints[it.quayRef]!!.data }

            this.calls =
                originalJourney.plan.calls
                    .filter {
                        val plannedQuayRef = stopPointRefToQuayRefMap[it.stopPointRef]
                        replacementStopPoints[plannedQuayRef] != null
                    }.mapIndexed { index, call ->
                        val plannedQuayRef = stopPointRefToQuayRefMap[call.stopPointRef]
                        val replacementStopPointRef = replacementStopPoints[plannedQuayRef]!!.data.ref
                        val order = index + 1

                        val datedJourneyCallRef =
                            MapperUtils.toDatedJourneyStopPointCallRef(
                                stopPointRef = replacementStopPointRef,
                                datedJourneyRef = replacementJourney.ref,
                                visitCount = order,
                            )

                        // todo: edit existing call or initialize new call?
                        DTODatedJourneyCall().apply {
                            this.ref = datedJourneyCallRef
                            this.stopPointRef = replacementStopPointRef
                            this.order = order
                            // maybe more fields to copy from existing call?
                            this.plannedArrival = call.plannedArrival
                            this.plannedDeparture = call.plannedDeparture
                            this.stopPointBehaviourType = call.stopPointBehaviourType
                            this.originalStopPointBehaviourType = call.originalStopPointBehaviourType
                        }
                    }

            val lastServicedStopPointRef =
                this.calls
                    .lastOrNull {
                        it.stopPointBehaviourType == DTOStopPointBehaviourType.FOR_ALIGHTING_ONLY ||
                            it.stopPointBehaviourType == DTOStopPointBehaviourType.FULL_SERVICE
                    }?.stopPointRef ?: this.calls.lastOrNull()?.stopPointRef

            if (lastServicedStopPointRef != null) {
                val destination = this.stops.firstOrNull { it.ref == lastServicedStopPointRef }?.name
                if (destination != null) {
                    this.calls.forEach {
                        it.destination = DTODestination(destination, null)
                    }
                }
            }

            this.firstDepartureDateTime = this.calls.firstOrNull()?.plannedDeparture
            this.lastArrivalDateTime = this.calls.lastOrNull()?.plannedArrival

            // no links and linkRefs (yet)
            this.links = emptyList()
            this.linkRefs = emptyList()
        }

        return plan
    }

    private fun createReplacementLine(
        originalLine: DTOLine,
        newLineRef: String,
    ): DTOLine =
        DTOLine().apply {
            this.lineRef = newLineRef
            this.publicCode = "${originalLine.publicCode}B"
            this.transportMode = DTOTransportMode.BUS
            this.name = "Buss for linje ${originalLine.publicCode}"
            this.backgroundColour = "E60000"
            this.textColour = "FFFFFF"
        }
}
