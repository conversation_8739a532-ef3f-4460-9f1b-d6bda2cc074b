package no.ruter.tranop.app.variance.deviation.config

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceDeviationOutputConfig.CONF_PREFIX)
class ServiceDeviationOutputConfig {
    val kafkaAvroEntityV1 = KafkaAvroEntityV1Config()
    val kafkaDtoV1 = KafkaDtoV1Config()
    val snowflakeJsonBiV1 = SnowflakeJsonBiV1Config()

    class KafkaAvroEntityV1Config {
        var enabled: Boolean = true
    }

    class KafkaDtoV1Config {
        var enabled: Boolean = true
    }

    class SnowflakeJsonBiV1Config {
        var enabled: Boolean = true
    }

    companion object {
        const val CONF_PREFIX = "app.config.service-deviation.output"
        const val KAFKA_AVRO_ENTITY_V1_ENABLED = "$CONF_PREFIX.kafka-avro-entity-v1.enabled"
        const val KAFKA_DTO_V1_ENABLED = "$CONF_PREFIX.kafka-dto-v1.enabled"
        const val SNOWFLAKE_JSON_BI_V1_ENABLED = "$CONF_PREFIX.snowflake-json-bi-v1.enabled"
    }
}
