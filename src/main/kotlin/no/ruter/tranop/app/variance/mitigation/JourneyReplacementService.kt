package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetDiff
import no.ruter.tranop.app.variance.common.spec.window.JourneySpecWindow
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.app.variance.mitigation.replacement.BusForTramReplacementService
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopPointService
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperatorContract
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventReplacement
import org.apache.commons.lang3.NotImplementedException
import org.springframework.stereotype.Service

@Service
class JourneyReplacementService(
    infoProperties: AppInfoProperties,
    stopPointRepo: StopPointRepository,
    private val timeService: TimeService,
    private val journeyRepo: DatedJourneyRepository,
    private val impactService: JourneyImpactService,
    private val cancellationService: JourneyCancellationService,
    private val datedJourneyInputService: DatedJourneyInputService,
    private val journeyEventInputService: JourneyEventInputService,
) : ServiceMitigationHandler {
    private val busForTramReplacementService =
        BusForTramReplacementService(
            timeService = timeService,
            infoProperties = infoProperties,
            replacementStopPointService = ReplacementStopPointService(stopPointRepo, timeService, infoProperties),
        )

    private val defaultStandbyOperator = DTOOperator("RUT:Operator:140", "Vy Buss AS")

    // TODO: Does standbyOperator have an operatorContract for replacement journeys? As of now, standbyOperator is applied to tram operator contract
    private val defaultStandbyOperatorContract = DTOOperatorContract("RUT:OperatorContract:x4", "Oslo_Trikken", defaultStandbyOperator)

    override fun processMitigationRequest(request: ServiceMitigationRequest): ServiceMitigationResponse =
        if (request.type == ServiceVarianceRequest.Type.CREATE) {
            request.stored?.let { mitigation ->
                performReplacement(request.trace, request.impact, request.channel, mitigation)
            } ?: ServiceMitigationResponse.NONE
        } else if (request.type == ServiceVarianceRequest.Type.DELETE) {
            request.stored?.let { mitigation ->
                undoReplacement(request.trace, request.impact, request.channel, mitigation)
            } ?: ServiceMitigationResponse.NONE
        } else {
            ServiceMitigationResponse.NONE
        }

    private fun performReplacement(
        trace: TraceInfo,
        impact: ServiceImpactContext,
        channel: DataChannel,
        mitigation: InternalServiceMitigation,
    ): ServiceMitigationResponse {
        val targets = impactService.findJourneyTargets(impact)
        val plannedRecord =
            targets.journeys.lastOrNull()?.let {
                journeyRepo.fetchByRef(it.ref)
            } ?: throw IllegalArgumentException("Impacted journey not found")
        val plannedJourney = plannedRecord.data

        if (plannedJourney.line?.transportMode == DTOTransportMode.TRAM) {
            val replacementJourney = busForTramReplacementService.createReplacementJourney(plannedJourney, trace)
            replacementJourney.operators = listOf(defaultStandbyOperator.deepCopy()) // TODO: should capture from input?
            replacementJourney.operatorContracts = listOf(defaultStandbyOperatorContract.deepCopy()) // TODO: capture from input?

            // TODO: Support draft mode (do not commit changes)
            // TODO: Support asynchronous update (do not commit changes on initial call, but do so in scheduled task later)
            addJourney(replacementJourney)

            // Cancellation-event added before Replaced-event to make sure journeyMitigationState.mitigations holds mitigation with code=REPLACEMENT_SERVICE
            cancellationService.cancelJourney(
                trace = trace,
                channel = channel,
                target = JourneyTarget(ref = plannedJourney.ref, calls = null), // full cancellation of planned journey.
                mitigation = mitigation,
                immediate = true,
            )

            addReplacedEvent(
                trace = trace,
                channel = channel,
                journeyRef = plannedJourney.ref,
                replacementJourneyRefs = listOf(replacementJourney.ref),
                mitigation = mitigation,
                immediate = true,
            )

            // TODO: Figure out proper handling of updates with calculation of included / excluded, etc.
            val replacements =
                JourneyReplacements(
                    replaced = listOf(plannedJourney),
                    replacements = listOf(replacementJourney),
                )
            val combinedJourneys = targets.getCombinedTargets().journeys
            val journeyTargetDiff =
                JourneyTargetDiff(
                    all = combinedJourneys,
                    included = combinedJourneys,
                    excluded = emptyList(),
                )
            return ServiceMitigationResponse(
                targets = journeyTargetDiff,
                replacements = listOf(replacements),
            )
        } else {
            val msg = "Replacement for transport mode ${plannedJourney.line?.transportMode} not supported"
            throw NotImplementedException(msg)
        }
    }

    private fun undoReplacement(
        trace: TraceInfo,
        impact: ServiceImpactContext,
        channel: DataChannel,
        mitigation: InternalServiceMitigation,
    ): ServiceMitigationResponse {
        val impactedJourney: JourneySpecWindow =
            impact.journeys
                .lastOrNull()
                ?.journey
                ?: throw IllegalArgumentException("\$spec.impact.journeys is required")

        val plannedJourney =
            journeyRepo
                .fetchByRef(impactedJourney.spec?.journeyId?.value)
                ?.data
                ?: throw IllegalArgumentException("Impacted journey not found")

        // undo cancellation of planned journey
        cancellationService.cancelJourney(
            trace = trace,
            channel = channel,
            target = JourneyTarget(ref = plannedJourney.ref, calls = null),
            mitigation = mitigation,
            immediate = true,
            cancelled = false,
        )

        // full cancellation of replacement journey(s)
        plannedJourney.replacedBy?.journeys?.forEach { replacedByJourney ->
            journeyRepo
                .fetchByRef(replacedByJourney.entityDatedJourneyKeyV2Ref)
                ?.data
                ?.let {
                    cancellationService.cancelJourney(
                        trace = trace,
                        channel = channel,
                        target = JourneyTarget(ref = it.ref, calls = null),
                        mitigation = mitigation,
                        immediate = true,
                    )
                }
        }

        // Unreplaced-event
        addReplacedEvent(
            trace = trace,
            channel = channel,
            journeyRef = plannedJourney.ref,
            replacementJourneyRefs = plannedJourney.replacedBy.journeys.map { it.entityDatedJourneyKeyV2Ref },
            mitigation = mitigation,
            immediate = true,
            replaced = false,
        )
        return ServiceMitigationResponse(
            targets = JourneyTargetDiff.EMPTY, // TODO: Return proper targets?
        )
    }

    private fun addJourney(journey: DTODatedJourney) {
        datedJourneyInputService.processInternal(journey.ref, journey) // TODO validate
    }

    private fun addReplacedEvent(
        trace: TraceInfo,
        channel: DataChannel,
        journeyRef: String,
        replacementJourneyRefs: List<String>,
        mitigation: InternalServiceMitigation,
        immediate: Boolean,
        replaced: Boolean = true,
    ) {
        val now = timeService.now()
        val header =
            DTOMessageHeader().apply {
                traceId = trace.traceId
                messageTimestamp = now.toString()
            }

        val replacement =
            DTOJourneyEventReplacement().apply {
                this.replaced = replaced
                this.replacementJourneyRefs = replacementJourneyRefs
            }

        val event =
            DTOJourneyEvent().apply {
                this.header = header
                this.replacement = replacement
                this.entityDatedJourneyKeyV2Ref = journeyRef
                this.entityServiceMitigationKeyV1Ref = mitigation.ref
            }
        journeyEventInputService.store(now, event, channel, process = immediate)
    }
}
