package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOperatingWithoutSignon
import org.springframework.stereotype.Service

@Service
class JourneyNoSignOnService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceDeviationHandler {
    override fun processDeviationRequest(request: ServiceDeviationRequest): ServiceDeviationResponse {
        val type = request.type
        val active = type != ServiceVarianceRequest.Type.DELETE
        val targets = resolveCombinedTargets(request)

        val immediate = true
        val deviation = request.modified ?: request.stored ?: return ServiceDeviationResponse.NONE

        // Apply no sign-on on journeys no longer included in target.
        targets.included.forEach { target ->
            noSignOnJourney(target, request, deviation, immediate, active = active)
        }

        // Recall no sign-on on journeys no longer included in target.
        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targets.excluded.forEach { target ->
                noSignOnJourney(target, request, deviation, immediate, active = false)
            }
        }

        return ServiceDeviationResponse(targets = targets)
    }

    private fun noSignOnJourney(
        target: JourneyTarget,
        request: ServiceDeviationRequest,
        deviation: InternalServiceDeviation,
        immediate: Boolean,
        active: Boolean,
    ) {
        val withoutSignOn =
            DTOJourneyEventOperatingWithoutSignon().apply {
                this.active = active
            }

        storeJourneyEvent(
            trace = request.trace,
            channel = request.channel,
            target = target,
            immediate = immediate,
        ) { event ->
            event.operatingWithoutSignon = withoutSignOn
            event.entityServiceDeviationKeyV1Ref = deviation.ref
        }
    }
}
