package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.variance.common.ServiceVarianceResponse
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetDiff

class ServiceMitigationResponse(
    targets: JourneyTargetDiff,
    val replacements: List<JourneyReplacements> = emptyList(),
) : ServiceVarianceResponse(
        targets,
    ) {
    companion object {
        val NONE = ServiceMitigationResponse(targets = JourneyTargetDiff.EMPTY)
    }
}
