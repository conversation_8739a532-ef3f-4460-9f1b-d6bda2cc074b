package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRepository
import no.ruter.tranop.app.variance.common.db.ServiceVarianceTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class ServiceDeviationRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : ServiceVarianceRepository<
        DeviationTable,
        DeviationRecord,
        DTOServiceDeviation,
        InternalServiceDeviation,
        ServiceDeviationRecordMapper,
        ServiceDeviationQueryBuilder,
        ServiceVarianceTableMetadata<DeviationTable, DeviationRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = ServiceDeviationRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = ServiceDeviationQueryBuilder.TYPE
        val TABLE = TYPE.table
        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.MODIFIED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    override fun queryBuilder() = ServiceDeviationQueryBuilder()

    fun newRecord(spec: DTOServiceDeviationSpec): InternalServiceDeviation {
        val ref = MapperUtils.randomId(prefix = "sd-")
        val record = dslContext.newRecord(table)
        return wrapSpec(ref, spec, record)
    }

    fun wrapSpec(
        ref: String,
        spec: DTOServiceDeviationSpec,
        record: DeviationRecord,
    ): InternalServiceDeviation {
        val wrapper =
            DTOServiceDeviation().apply {
                this.ref = ref
                this.spec = spec
            }
        return InternalServiceDeviation(wrapper, record)
    }

    fun storeRecord(
        data: InternalServiceDeviation,
        trace: TraceInfo,
        now: OffsetDateTime? = null,
    ): Boolean {
        val t = now ?: timeService.now()
        val record = data.record
        val externalRefs = ExternalRefs()
        recordMapper.updateRecord(
            source = data.data,
            record = record,
            trace = trace,
            now = t,
            externalRefs = externalRefs,
        )
        val res = record.store() > 0

        if (res) {
            externalRefRepo.update(
                createdAt = record.createdAt,
                createdBy = record.createdBy,
                externalRefs = mapOf(data.ref to externalRefs),
            )

            addToOutbox(
                ref = data.data.ref,
                payload = record.jsonData,
                tombstone = record.tombstonedAt != null,
            )
        }
        return res
    }
}
