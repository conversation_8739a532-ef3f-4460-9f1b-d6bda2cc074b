package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.QuayRefTargets
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventDelayReason
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventDelay
import org.springframework.stereotype.Service

@Service
class JourneyDelayService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceDeviationHandler {
    companion object {
        val REASONS =
            mapOf(
                ServiceVarianceRequest.Type.CREATE to DTOJourneyEventDelayReason.DELAYED,
                ServiceVarianceRequest.Type.UPDATE to DTOJourneyEventDelayReason.DELAYED,
                ServiceVarianceRequest.Type.DELETE to DTOJourneyEventDelayReason.UNDELAYED,
            )
    }

    override fun processDeviationRequest(request: ServiceDeviationRequest): ServiceDeviationResponse {
        val type = request.type
        val reason = REASONS[type] ?: throw IllegalStateException("unsupported request type: $type")
        val targets = resolveQuayRefTargets(request)
        val deviation = request.modified ?: request.stored ?: return ServiceDeviationResponse.NONE

        val immediate = true
        val targetJourneys = targets.journeys

        // Apply delay deviation on included target journeys.
        targetJourneys.included.forEach { target ->
            delayJourney(
                trace = request.trace,
                channel = request.channel,
                target = target,
                deviation = deviation,
                immediate = immediate,
                reason = reason,
                targets = targets,
            )
        }

        // Recall daly for excluded journeys on update.
        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targetJourneys.excluded.forEach { target ->
                delayJourney(
                    trace = request.trace,
                    channel = request.channel,
                    target = target,
                    deviation = deviation,
                    immediate = immediate,
                    reason = DTOJourneyEventDelayReason.UNDELAYED,
                    targets = targets,
                )
            }
        }

        return ServiceDeviationResponse(targets = targetJourneys)
    }

    fun delayJourney(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        deviation: InternalServiceDeviation,
        immediate: Boolean,
        reason: DTOJourneyEventDelayReason,
        targets: QuayRefTargets?,
    ) {
        val minutes =
            if (reason == DTOJourneyEventDelayReason.UNDELAYED) {
                0
            } else {
                deviation
                    .data
                    .spec
                    ?.parameters
                    ?.delayMinutes
            } ?: 0
        val calls = target.calls?.map { call -> mapJourneyEventCall(call, targets) }

        val eventDelay =
            DTOJourneyEventDelay().apply {
                this.calls = calls
                this.reason = reason
                this.delayMinutes = minutes
                this.entityTrafficCaseKeyV2Ref = null // TODO: Map this from deviation metadata?
                this.entityTrafficEventKeyV1Ref = null // TODO: Map this from deviation metadata?
                this.entityTrafficSituationKeyV2Ref = null // TODO: Map this from deviation metadata?
            }

        storeJourneyEvent(
            trace = trace,
            channel = channel,
            target = target,
            immediate = immediate,
        ) { event ->
            // TODO: Avoid adding a new event if only deviation metadata has changed (i.e., delay value is the same).
            // event.id = "${deviation.ref}-r${deviation.record.revision}"
            event.delay = eventDelay
            event.entityServiceDeviationKeyV1Ref = deviation.ref
        }
    }
}
