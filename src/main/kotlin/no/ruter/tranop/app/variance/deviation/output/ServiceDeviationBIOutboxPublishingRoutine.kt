package no.ruter.tranop.app.variance.deviation.output

import com.fasterxml.jackson.databind.JsonNode
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractOutboxPublishingRoutine
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.outbox.OutboxOutputMessage
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.variance.deviation.lifecycle.ServiceDeviationLifeCycleConfig
import no.ruter.tranop.app.variance.deviation.output.entity.ServiceDeviationEntityOutputMapper
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.bi.model.BIDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component

@ConditionalOnProperty(prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX, name = ["enabled"], havingValue = "true")
@Component
class ServiceDeviationBIOutboxPublishingRoutine(
    outboxService: OutboxService,
    val mapper: ServiceDeviationEntityOutputMapper,
    val publisher: ServiceDeviationSnowflakeIngestClient,
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    serviceDeviationLifeCycleConfig: ServiceDeviationLifeCycleConfig,
) : AbstractOutboxPublishingRoutine<
        DeviationTable,
        DeviationRecord,
        DTOServiceDeviation,
        DTOServiceDeviation,
    >(
        name = LC_TYPE_OUTBOX_PUBLISH,
        recordType = RecordType.SERVICE_DEVIATION,
        targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
        payloadClass = DTOServiceDeviation::class,
        outboxService = outboxService,
        insightService = insightService,
        appInfoProperties = appInfoProperties,
        abstractLifeCycleConfig = serviceDeviationLifeCycleConfig,
    ) {
    override fun map(
        data: DTOServiceDeviation?,
        context: MappingContext,
    ): DTOServiceDeviation? {
        return data
    }

    override fun publish(
        messages: List<OutboxOutputMessage<DTOServiceDeviation>>,
        postProcessing: (Exception?) -> Unit,
    ) {
        try {
            val events = messages.map { Pair(it.ref, it.value) }
            publisher.ingest(events, throwExceptionOnError = true)
            postProcessing(null)
        } catch (e: Exception) {
            postProcessing(e)
        }
    }

    override fun metadata(data: DTOServiceDeviation?): Map<String, Any?> = emptyMap()
}
