package no.ruter.tranop.app.variance.common.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.json.JSONRecordData
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetCollection
import no.ruter.tranop.assignment.common.db.record.json.JSONRecord
import org.jooq.Table
import org.jooq.UpdatableRecord

abstract class ServiceVarianceRecordData<
    T : Table<R>,
    R,
    D,
>(
    type: RecordType<T, R, *>,
    data: D,
    record: R,
) : JSONRecordData<T, R, D>(
        type = type,
        data = data,
        record = record,
    ) where R : JSONRecord, R : UpdatableRecord<R> {
    abstract val resolvedJourneyTargets: JourneyTargetCollection
}
