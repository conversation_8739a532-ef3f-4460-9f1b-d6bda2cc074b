package no.ruter.tranop.app.variance.deviation.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class ServiceDeviationRecordMapper(
    val insightService: InsightService,
) : RecordMapper<DeviationRecord, InternalServiceDeviation> {
    override fun map(record: DeviationRecord): InternalServiceDeviation {
        val data = JsonUtils.toObject(record.jsonData?.data(), DTOServiceDeviation::class.java)
        return InternalServiceDeviation(
            data = data,
            record = record,
        )
    }

    fun updateRecord(
        source: DTOServiceDeviation,
        record: DeviationRecord,
        trace: TraceInfo,
        now: OffsetDateTime,
        externalRefs: ExternalRefs,
    ) {
        record.ref = source.ref
        record.jsonHash = source.hash()
        record.jsonData = JSON.valueOf(JsonUtils.toJson(source))

        val creator = trace.operatorId
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
            record.operatorId = trace.operatorId
            record.authorityId = trace.authorityId
        }
        record.deletedAt = null
        record.deletedRevision = null

        record.modifiedAt = now
        record.modifiedBy = creator

        source.spec.metadata.forEach { entry ->
            ExternalRefType.of(entry.key.value).takeIf { it != null && it.value > 0 }?.let {
                externalRefs.add(
                    type = it,
                    value = entry.value,
                )
            }
        }
    }

    companion object {
        fun DTOServiceDeviation?.hash(): String =
            this?.let {
                val stripped =
                    it
                        .deepCopy()
                        .apply {
                            this?.header = null
                            this?.lifecycle = null
                        }
                MapperUtils.hash(JsonUtils.toJson(stripped))
            } ?: "null"
    }
}
