package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec

class ServiceDeviationRequest(
    type: Type,
    trace: TraceInfo,
    input: DTOServiceDeviationSpec,
    impact: ServiceImpactContext,
    channel: DataChannel,
    stored: InternalServiceDeviation? = null,
    modified: InternalServiceDeviation? = null,
) : ServiceVarianceRequest<DTOServiceDeviationSpec, InternalServiceDeviation>(
        type = type,
        trace = trace,
        input = input,
        impact = impact,
        channel = channel,
        stored = stored,
        modified = modified,
    )
