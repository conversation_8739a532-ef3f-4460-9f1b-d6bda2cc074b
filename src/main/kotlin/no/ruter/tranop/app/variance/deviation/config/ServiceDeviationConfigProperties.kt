package no.ruter.tranop.app.variance.deviation.config

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceDeviationConfigProperties.CONF_PREFIX)
class ServiceDeviationConfigProperties : AbstractSectionConfigProperties() {
    lateinit var toggles: ServiceDeviationToggles

    companion object {
        const val CONF_PREFIX = "app.config.service-deviation"
    }
}
