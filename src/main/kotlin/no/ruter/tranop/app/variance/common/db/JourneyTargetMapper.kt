package no.ruter.tranop.app.variance.common.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.variance.common.spec.JourneyCallSpec
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetCollection
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpactJourneyTarget
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpactJourneyTargets
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyCallSpec
import org.jooq.JSON

class JourneyTargetMapper private constructor() {
    companion object {
        fun mapTarget(input: JourneyTarget): DTOServiceImpactJourneyTarget =
            DTOServiceImpactJourneyTarget().apply {
                this.ref = input.ref
                this.calls = input.calls?.map(::mapTargetCall)
            }

        fun mapTarget(input: DTOServiceImpactJourneyTarget): JourneyTarget =
            JourneyTarget(
                ref = input.ref,
                calls = input.calls?.map(::mapTargetCall),
            )

        fun mapTargetCall(input: JourneyCallSpec): DTOJourneyCallSpec =
            DTOJourneyCallSpec().apply {
                this.stopPoint = input.stopPoint
                this.arrivalDateTime = input.arrivalTime?.toString()
                this.departureDateTime = input.departureTime?.toString()
            }

        fun mapTargetCall(input: DTOJourneyCallSpec): JourneyCallSpec =
            JourneyCallSpec(
                stopPoint = input.stopPoint,
                arrivalTime = input.arrivalDateTime?.toOffsetDateTime(),
                departureTime = input.departureDateTime?.toOffsetDateTime(),
            )

        fun mapTargets(input: List<JourneyTarget>): JSON {
            val dbTargets =
                DTOServiceImpactJourneyTargets().apply {
                    this.resolvedJourneyTargets = input.map(JourneyTargetMapper::mapTarget)
                }
            return JSON.valueOf(JsonUtils.toJson(dbTargets))
        }

        fun mapTargets(input: JSON?): JourneyTargetCollection {
            val targets =
                input
                    ?.data()
                    ?.let {
                        JsonUtils.toObject(it, DTOServiceImpactJourneyTargets::class.java)
                    }?.resolvedJourneyTargets
                    ?: return JourneyTargetCollection.EMPTY

            val n = targets.size
            val refs = LinkedHashSet<String>(n)
            val journeys = ArrayList<JourneyTarget>(n)
            targets.forEach { t ->
                refs.add(t.ref)
                journeys.add(mapTarget(t))
            }
            return JourneyTargetCollection(refs = refs, journeys = journeys)
        }
    }
}
