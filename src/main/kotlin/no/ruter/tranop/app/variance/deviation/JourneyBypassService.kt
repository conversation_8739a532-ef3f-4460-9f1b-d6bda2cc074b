package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.common.spec.target.QuayRefTargets
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventBypass
import org.springframework.stereotype.Service

@Service
class JourneyBypassService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceDeviationHandler {
    override fun validateDeviationRequest(
        request: ServiceDeviationRequest,
        context: MappingContext,
    ) {
        val type = request.type
        if (type != ServiceVarianceRequest.Type.DELETE) {
            val impact = request.impact
            val impactPath = impact.path
            if (impact.lines.isNotEmpty()) {
                val msg = "Bypass deviation request may not contains line specs"
                context.addError(path = "$impactPath.lines", msg = msg, reason = "")
            }
            if (impact.stopPoints.isNotEmpty()) {
                val msg = "Bypass deviation request may not contains stop point specs"
                context.addError(path = "$impactPath.stopPoints", msg = msg, reason = "")
            }
            if (impact.partialJourneys.isEmpty()) {
                val msg = "Bypass deviation request must specify at least one call in one journey"
                context.addError(path = "$impactPath.journeys", msg = msg, reason = "")
            }
        }
    }

    override fun processDeviationRequest(request: ServiceDeviationRequest): ServiceDeviationResponse {
        val type = request.type
        val targets = resolveQuayRefTargets(request)
        val bypassed = type != ServiceVarianceRequest.Type.DELETE
        val deviation = request.stored ?: return ServiceDeviationResponse.NONE

        val immediate = true
        val targetJourneys = targets.journeys

        // Recall bypass on excluded target journeys.
        targetJourneys.included.forEach { target ->
            bypassJourneyCall(target, targets, request, deviation, bypassed = bypassed, immediate = immediate)
        }

        // Recall bypass on excluded target journeys.
        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targetJourneys.excluded.forEach { target ->
                bypassJourneyCall(target, targets, request, deviation, bypassed = false, immediate = immediate)
            }
        }

        return ServiceDeviationResponse(targets = targetJourneys)
    }

    private fun bypassJourneyCall(
        target: JourneyTarget,
        targets: QuayRefTargets,
        request: ServiceDeviationRequest,
        deviation: InternalServiceDeviation,
        bypassed: Boolean,
        immediate: Boolean,
    ) {
        val calls = target.calls?.map { call -> mapJourneyEventCall(call, targets) }
        val details =
            DTOJourneyEventBypass().apply {
                this.calls = calls
                this.bypass = bypassed
            }

        storeJourneyEvent(
            trace = request.trace,
            channel = request.channel,
            target = target,
            immediate = immediate,
        ) { event ->
            event.bypass = details
            event.entityServiceDeviationKeyV1Ref = deviation.ref
        }
    }
}
