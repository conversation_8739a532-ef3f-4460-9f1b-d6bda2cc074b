package no.ruter.tranop.app.variance.deviation.output

import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTarget
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX)
class ServiceDeviationOutboxStreamingConfig: SnowflakeTarget {
    var batchSize = 1000
    var enabled = false
    var retryCount = 5

    override lateinit var dbName: String
    override lateinit var schemaName: String
    override lateinit var tableName: String

    companion object {
        const val CONF_PREFIX = "app.config.outbox.snowflake-streaming-service-deviation"
    }
}
