package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRepository
import no.ruter.tranop.app.variance.common.db.ServiceVarianceTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.tables.MitigationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class ServiceMitigationRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : ServiceVarianceRepository<
        MitigationTable,
        MitigationRecord,
        DTOServiceMitigation,
        InternalServiceMitigation,
        ServiceMitigationRecordMapper,
        ServiceMitigationQueryBuilder,
        ServiceVarianceTableMetadata<MitigationTable, MitigationRecord>,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = ServiceMitigationRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = ServiceMitigationQueryBuilder.TYPE
        val TABLE = TYPE.table

        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.MODIFIED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    override fun queryBuilder() = ServiceMitigationQueryBuilder()

    fun newRecord(spec: DTOServiceMitigationSpec): InternalServiceMitigation {
        val ref = MapperUtils.randomId(prefix = "sm-")
        val record = dslContext.newRecord(table)
        return wrapSpec(ref, spec, record)
    }

    fun wrapSpec(
        ref: String,
        spec: DTOServiceMitigationSpec,
        record: MitigationRecord,
//        draft: Boolean
    ): InternalServiceMitigation {
        val wrapper =
            DTOServiceMitigation().apply {
                this.ref = ref
                this.spec = spec
//                this.draft = draft
            }
        return InternalServiceMitigation(wrapper, record)
    }

    fun storeRecord(
        mitigation: InternalServiceMitigation,
        trace: TraceInfo,
        now: OffsetDateTime? = null,
    ): Boolean {
        val t = now ?: timeService.now()
        val record = mitigation.record
        val data = mitigation.data
        recordMapper.updateRecord(source = data, record = record, trace = trace, now = t)
        val res = record.store() > 0
        if (res) {
            val payload = record.jsonData
            addToOutbox(
                ref = data.ref,
                payload = payload,
                tombstone = record.tombstonedAt != null,
            )
        }
        return res
    }
}
