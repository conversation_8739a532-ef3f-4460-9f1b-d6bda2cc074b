package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.AbstractServiceVarianceHandler
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventContingencyVehiclePlanned
import org.springframework.stereotype.Service

@Service
class JourneyStandbyVehicleService(
    timeService: TimeService,
    stopPointRepo: StopPointRepository,
    impactService: JourneyImpactService,
    journeyEventInputService: JourneyEventInputService,
) : AbstractServiceVarianceHandler(
        timeService = timeService,
        stopPointRepo = stopPointRepo,
        impactService = impactService,
        journeyEventInputService = journeyEventInputService,
    ),
    ServiceMitigationHandler {
    override fun processMitigationRequest(request: ServiceMitigationRequest): ServiceMitigationResponse {
        val type = request.type
        val targets = resolveQuayRefTargets(request)
        val planned = type != ServiceVarianceRequest.Type.DELETE
        val mitigation = request.stored ?: return ServiceMitigationResponse.NONE

        val immediate = true
        val targetJourneys = targets.journeys
        targetJourneys.included.forEach { target ->
            createContingencyVehiclePlannedEvent(
                trace = request.trace,
                channel = request.channel,
                target = target,
                mitigation = mitigation,
                planned = planned,
                immediate = immediate,
            )
        }

        if (type == ServiceVarianceRequest.Type.UPDATE) {
            targetJourneys.excluded.forEach { target ->
                createContingencyVehiclePlannedEvent(
                    trace = request.trace,
                    channel = request.channel,
                    target = target,
                    mitigation = mitigation,
                    planned = false, // recall stand-by vehicle on excluded journeys
                    immediate = immediate,
                )
            }
        }
        return ServiceMitigationResponse(targets = targetJourneys)
    }

    fun createContingencyVehiclePlannedEvent(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        mitigation: InternalServiceMitigation,
        planned: Boolean = true,
        immediate: Boolean = true,
    ) {
        val contingency =
            DTOJourneyEventContingencyVehiclePlanned().apply {
                this.planned = planned
                this.operatorRefs = listOf(trace.operatorId)
            }

        storeJourneyEvent(
            trace = trace,
            channel = channel,
            target = target,
            immediate = immediate,
        ) { event ->
            event.contingencyVehiclePlanned = contingency
            event.entityServiceMitigationKeyV1Ref = mitigation.ref
        }
    }
}
