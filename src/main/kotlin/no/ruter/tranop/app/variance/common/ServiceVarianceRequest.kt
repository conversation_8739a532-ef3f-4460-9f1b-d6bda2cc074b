package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRecordData

abstract class ServiceVarianceRequest<
    I,
    T,
>(
    val type: Type,
    val trace: TraceInfo,
    val input: I,
    val impact: ServiceImpactContext,
    override val channel: DataChannel,
    var stored: T?,
    var modified: T?,
) : InsightContext where T : ServiceVarianceRecordData<*, *, *> {
    enum class Type {
        CREATE,
        UPDATE,
        DELETE,
    }

    override val traceId: String?
        get() = trace.traceId

    override val metrics: Map<String, List<String>>
        get() = emptyMap() // TODO: Implement this properly.
}
