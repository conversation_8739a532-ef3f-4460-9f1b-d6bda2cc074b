package no.ruter.tranop.app.variance.mitigation.api

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.ServiceImpactValidator
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.ServiceVarianceService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.mitigation.JourneyCancellationService
import no.ruter.tranop.app.variance.mitigation.JourneyReplacementService
import no.ruter.tranop.app.variance.mitigation.JourneyStandbyVehicleService
import no.ruter.tranop.app.variance.mitigation.ServiceMitigationHandler
import no.ruter.tranop.app.variance.mitigation.ServiceMitigationRequest
import no.ruter.tranop.app.variance.mitigation.api.input.MitigationInputMapper
import no.ruter.tranop.app.variance.mitigation.api.output.MitigationOutputMapper
import no.ruter.tranop.app.variance.mitigation.api.output.ReplacementServiceOutputMapper
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationQueryBuilder
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationRepository
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Isolation
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
class ADTv4MitigationService(
    val repo: ServiceMitigationRepository,
    private val standbyService: JourneyStandbyVehicleService,
    private val replacementService: JourneyReplacementService,
    private val cancellationService: JourneyCancellationService,
    insightService: InsightService,
    timeService: TimeService,
) {
    private val log = LoggerFactory.getLogger(javaClass)
    private val requestService = ServiceVarianceService(repo)

    private val inputMapper = MitigationInputMapper(timeService, insightService)
    private val impactValidator = ServiceImpactValidator()

    // TODO: Merge output mappers?
    private val outputMapper = MitigationOutputMapper(timeService, insightService)
    private val replacementOutputMapper = ReplacementServiceOutputMapper()

    private val handlers: Map<DTOServiceMitigationCode, ServiceMitigationHandler> =
        mapOf(
            DTOServiceMitigationCode.CANCELLATION to cancellationService,
            DTOServiceMitigationCode.REPLACEMENT_SERVICE to replacementService,
            DTOServiceMitigationCode.STANDBY_VEHICLE_PLANNED to standbyService,
        )

    private fun validateInput(
        type: ServiceVarianceRequest.Type,
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        stored: InternalServiceMitigation?,
    ): ServiceMitigationRequest {
        val input = inputMapper.mapInput(context)
        val impactContext = impactValidator.validateImpact(repo.recordType, impact = input.impact, duration = input.duration)
        context.mergeServiceImpactValidationContext(impactContext)

        val request =
            ServiceMitigationRequest(
                type = type,
                trace = context.trace,
                input = input,
                impact = impactContext,
                channel = context.channel,
                stored = stored,
                modified = null,
            )
        resolveRequestHandler(request)?.validateMitigationRequest(request, context)
        if (context.hasErrors) {
            throw IllegalArgumentException("Invalid service mitigation request")
        }
        return request
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    fun createServiceMitigation(context: ADTv4InputContext<APIServiceMitigationPostRequest>): APIServiceMitigation {
        val type = ServiceVarianceRequest.Type.CREATE
        val request = validateInput(type, context, stored = null)

        val record = repo.newRecord(request.input)
        return processMitigationRequest(request, context, stored = record, modified = null)
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    fun updateServiceMitigation(
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        serviceMitigationId: String,
    ): APIServiceMitigation {
        val type = ServiceVarianceRequest.Type.UPDATE
        val stored =
            repo.fetchByRef(ref = serviceMitigationId, trace = context.trace)
                ?: throw APIResourceNotFoundException()

        val request = validateInput(type, context, stored)
        val modified = repo.wrapSpec(ref = serviceMitigationId, spec = request.input, record = stored.record)
        return processMitigationRequest(request, context, stored = stored, modified = modified)
    }

    fun deleteServiceMitigation(
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        serviceMitigationId: String,
    ) {
        val type = ServiceVarianceRequest.Type.DELETE
        val stored =
            repo.fetchByRef(
                ref = serviceMitigationId,
                trace = context.trace,
                includeDeleted = true,
            ) ?: throw APIResourceNotFoundException()

        if (stored.deleted) {
            return // Record already deleted, no need to do anything.
        }

        val deleted = repo.setDeleted(ref = stored.record.ref, trace = context.trace, now = context.received)
        if (!deleted) {
            throw APIException(
                httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                msg = "Error deleting mitigation $serviceMitigationId: record not found?",
            )
        }

        val input = stored.data.spec
        val impact = // Resolve impact of original mitigation, so we can give the handler a chance to revert mitigation hen deleted.
            impactValidator.validateImpact(
                type = RecordType.SERVICE_MITIGATION,
                impact = input.impact,
                duration = input.duration,
            )
        val request =
            ServiceMitigationRequest(
                type = type,
                trace = context.trace,
                input = input,
                impact = impact,
                channel = context.channel,
                stored = stored,
                modified = null,
            )
        processMitigationRequest(request, context, stored = stored, modified = null, storeRecord = false)
    }

    private fun processMitigationRequest(
        request: ServiceMitigationRequest,
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        stored: InternalServiceMitigation,
        modified: InternalServiceMitigation?,
        storeRecord: Boolean = true,
    ): APIServiceMitigation {
        val target = modified ?: stored
        if (storeRecord) {
            repo.storeRecord(target, trace = context.trace, now = context.received)
            request.stored = stored
            request.modified = modified
        }
        val response = outputMapper.mapMitigation(target, context)

        val apiResponse =
            resolveRequestHandler(request)?.let { handler ->
                val processResponse = handler.processMitigationRequest(request)
                processResponse.let {
                    requestService.updateResolvedJourneyTargets(target, it)
                }
                val replacements = processResponse.replacements
                if (replacements.isEmpty()) {
                    response
                } else {
                    response.copy(
                        replacements = replacements.map(replacementOutputMapper::mapReplacement),
                    )
                }
            } ?: response

        // TODO: Update external refs.

        publishMitigation(target, context)
        return apiResponse
    }

    private fun resolveRequestHandler(request: ServiceMitigationRequest): ServiceMitigationHandler? {
        val code =
            request.input.code
                ?: request
                    .stored
                    ?.data
                    ?.spec
                    ?.code
                ?: request
                    .modified
                    ?.data
                    ?.spec
                    ?.code
        return handlers[code]
    }

    fun readServiceMitigation(
        context: ADTv4InputContext<*>,
        serviceMitigationId: String,
    ): APIServiceMitigation? {
        val mitigation = repo.fetchByRef(ref = serviceMitigationId, trace = context.trace)
        return mitigation?.let { outputMapper.mapMitigation(it, context) }
    }

    fun findServiceMitigations(
        context: ADTv4InputContext<*>,
        query: ServiceMitigationQueryBuilder,
    ): List<APIServiceMitigation> =
        repo
            .fetch(query)
            .map { outputMapper.mapMitigation(it, context) }

    private fun publishMitigation(
        record: InternalServiceMitigation,
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
    ): APIServiceMitigation = outputMapper.mapMitigation(record, context)
}
