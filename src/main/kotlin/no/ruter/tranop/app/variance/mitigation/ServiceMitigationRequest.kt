package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec

class ServiceMitigationRequest(
    type: Type,
    trace: TraceInfo,
    input: DTOServiceMitigationSpec,
    impact: ServiceImpactContext,
    channel: DataChannel,
    stored: InternalServiceMitigation? = null,
    modified: InternalServiceMitigation? = null,
) : ServiceVarianceRequest<DTOServiceMitigationSpec, InternalServiceMitigation>(
        type = type,
        trace = trace,
        input = input,
        impact = impact,
        channel = channel,
        stored = stored,
        modified = modified,
    )
