package no.ruter.tranop.app.variance.deviation.api

import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.AbstractADTv4APIController
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeService
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationQueryBuilder
import no.ruter.tranop.assignment.adt.v4.api.DeviationApi
import no.ruter.tranop.assignment.adt.v4.model.APIPageInfo
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReasonCodeListResponse
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class ADTv4DeviationAPIController(
    timeService: TimeService,
    insightService: InsightService,
    private val reasonCodeService: ReasonCodeService,
    private val deviationService: ADTv4DeviationService,
) : AbstractADTv4APIController(
        channel = deviationService.repo.recordType.channels.input,
        timeService = timeService,
        insightService = insightService,
    ),
    DeviationApi {
    override fun findServiceDeviations(
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
        query: String?,
        fromDateTime: String?,
        toDateTime: String?,
        limit: Int?,
        offset: Int?,
    ): ResponseEntity<APIServiceDeviationListResponse> {
        val context =
            createInputContext<Unit?>(
                request = null,
                ref = null,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )

        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceDeviationListResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceDeviationListResponse(result = r) } }
        return execute(
            name = "deviation.find",
            context = context,
            action =
                {
                    val q =
                        ServiceDeviationQueryBuilder()
                            .page(limit = limit, offset = offset)
                            .authorized(trace = context.trace, restricted = true, includeDeleted = false)
                            .toDateTime(toDateTime)
                            .fromDateTime(fromDateTime)
                            .query(query)
                    val deviations = deviationService.findServiceDeviations(context, q)
                    apiResponse(HttpStatus.OK, context) { r ->
                        val page = APIPageInfo(limit = limit, offset = offset, itemCount = deviations.size)
                        APIServiceDeviationListResponse(result = r, items = deviations, page = page)
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun getServiceDeviation(
        serviceDeviationId: String,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceDeviationGetResponse> {
        val context =
            createInputContext(
                request = null,
                ref = serviceDeviationId,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceDeviationGetResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceDeviationGetResponse(result = r) } }
        return execute(
            name = "deviation.read",
            context = context,
            action =
                {
                    val dev =
                        deviationService.readServiceDeviation(context, serviceDeviationId)
                            ?: throw APIResourceNotFoundException()
                    // TODO: Check that client has access to read this deviation.
                    apiResponse(HttpStatus.OK, context) { r ->
                        APIServiceDeviationGetResponse(result = r, deviation = dev)
                    }
                },
            notFoundHandler = errorHandler,
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun getServiceDeviationReasonCodes(
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
        serviceDeviationCode: String?,
    ): ResponseEntity<APIServiceDeviationReasonCodeListResponse> {
        val context =
            createInputContext(
                request = null,
                ref = null,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceDeviationReasonCodeListResponse> =
            { s, m, _ ->
                apiResponseEntity(
                    s,
                    context,
                    m,
                ) { r -> APIServiceDeviationReasonCodeListResponse(result = r) }
            }
        return execute(
            name = "deviation.reason.codes",
            context = context,
            action =
                {
                    val apiServiceDeviationCode = APIServiceDeviationCode.of(serviceDeviationCode)
                    val result = reasonCodeService.getReasonCodes(context, apiServiceDeviationCode)
                    val headers = HttpHeaders.EMPTY // Add cache control header.
                    apiResponse(HttpStatus.OK, context, headers = headers) { result }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun postServiceDeviation(
        apIServiceDeviationPostRequest: APIServiceDeviationPostRequest,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceDeviationPostResponse> {
        val context =
            createInputContext(
                request = apIServiceDeviationPostRequest,
                ref = null,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceDeviationPostResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceDeviationPostResponse(result = r) } }
        return execute(
            name = "deviation.create",
            context = context,
            action =
                { ctx ->
                    val deviation = deviationService.createServiceDeviation(ctx)
                    apiResponse(HttpStatus.CREATED, context) { r ->
                        APIServiceDeviationPostResponse(result = r, deviation = deviation)
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun updateServiceDeviation(
        serviceDeviationId: String,
        apIServiceDeviationPostRequest: APIServiceDeviationPostRequest,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceDeviationPostResponse> {
        val action = apIServiceDeviationPostRequest.action?.let(APIPostRequestType::of) ?: APIPostRequestType.UPDATE
        val context =
            createInputContext(
                request = apIServiceDeviationPostRequest,
                ref = serviceDeviationId,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceDeviationPostResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceDeviationPostResponse(result = r) } }
        return execute(
            name = "deviation.update",
            context = context,
            action =
                {
                    if (action == APIPostRequestType.UPDATE) {
                        val modified = deviationService.updateServiceDeviation(context, serviceDeviationId)
                        apiResponse(HttpStatus.OK, context) { r ->
                            APIServiceDeviationPostResponse(result = r, deviation = modified)
                        }
                    } else if (action == APIPostRequestType.DELETE) {
                        deviationService.deleteServiceDeviation(context, serviceDeviationId)
                        apiResponse(HttpStatus.OK, context) { r -> APIServiceDeviationPostResponse(result = r) }
                    } else {
                        throw IllegalArgumentException("Invalid action: $action")
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }
}
