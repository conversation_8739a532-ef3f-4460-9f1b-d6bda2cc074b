package no.ruter.tranop.app.variance.common.db

import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import org.jooq.JSON
import org.jooq.Table
import org.jooq.TableField
import org.jooq.UpdatableRecord

class ServiceVarianceTableMetadata<
    A : Table<R>,
    R : UpdatableRecord<R>,
>(
    table: A,
) : BaseRecordTableMetadata<A, R>(table) {
    val journeyTargets: TableField<R, JSON> = required(table, name = "journey_targets")
}
