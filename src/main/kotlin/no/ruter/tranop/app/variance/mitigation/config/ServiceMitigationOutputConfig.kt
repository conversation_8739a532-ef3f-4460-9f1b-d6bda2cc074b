package no.ruter.tranop.app.variance.mitigation.config

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceMitigationOutputConfig.CONF_PREFIX)
class ServiceMitigationOutputConfig {
    val kafkaAvroEntityV1 = KafkaAvroEntityV1Config()
    val snowflakeJsonBiV1 = SnowflakeJsonBiV1Config()

    class KafkaAvroEntityV1Config {
        var enabled: Boolean = true
    }

    class SnowflakeJsonBiV1Config {
        var enabled: Boolean = true
    }

    companion object {
        const val CONF_PREFIX = "app.config.service-mitigation.output"
        const val KAFKA_AVRO_ENTITY_V1_ENABLED = "$CONF_PREFIX.kafka-avro-entity-v1.enabled"
        const val SNOWFLAKE_JSON_BI_V1_ENABLED = "$CONF_PREFIX.snowflake-json-bi-v1.enabled"
    }
}
