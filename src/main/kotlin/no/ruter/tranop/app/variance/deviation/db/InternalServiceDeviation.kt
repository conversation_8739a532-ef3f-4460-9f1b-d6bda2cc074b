package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.variance.common.db.JourneyTargetMapper
import no.ruter.tranop.app.variance.common.db.ServiceVarianceRecordData
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargetCollection
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation

class InternalServiceDeviation(
    data: DTOServiceDeviation,
    record: DeviationRecord,
) : ServiceVarianceRecordData<
        DeviationTable,
        DeviationRecord,
        DTOServiceDeviation,
    >(RecordType.SERVICE_DEVIATION, data, record) {
    val ownerId: String?
        get() = data.header?.ownerId

    override val dataRef: String?
        get() = data.ref

    override val resolvedJourneyTargets: JourneyTargetCollection
        get() = JourneyTargetMapper.mapTargets(record.journeyTargets)
}
