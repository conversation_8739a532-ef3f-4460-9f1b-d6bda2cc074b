kafka:
  consumers:
    enabled: false

  configuration:
    "[schema.registry.url]": "mock://mocked.url"

# logging:
#  level:
#    "org.opensearch": TRACE

spring:
  scheduling:
    enabled: false
  async:
    enabled: false
  main:
    allow-bean-definition-overriding: true

opensearch:
    endpoint: localhost:9200
    region: eu-west-1
    journey-index-name: journey

app:
  config:
    streaming-ingest:
      enabled: true
    assignment:
      output:
        http:
          status:
            enabled: false
    datedjourney:
      lifecycle:
        routines:
          publish:
            olderThan: "PT0S"
    service-deviation:
      lifecycle:
        enabled: false
    outbox:
      snowflake-streaming-journey-event:
        enabled: true
      snowflake-streaming-service-deviation:
        enabled: true

