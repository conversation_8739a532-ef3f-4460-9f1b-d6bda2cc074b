{"properties": {"cancelled": {"type": "boolean"}, "deleted": {"type": "boolean"}, "direction": {"type": "keyword"}, "events": {"properties": {"description": {"type": "text"}, "id": {"copy_to": ["references_kw"], "type": "keyword"}, "messageTimestamp": {"type": "date"}, "metadata": {"properties": {"key": {"type": "keyword"}, "value": {"type": "text"}}}, "source": {"type": "keyword"}, "timestamp": {"type": "date"}, "traceId": {"type": "keyword"}, "type": {"type": "keyword"}}}, "extra": {"type": "boolean"}, "header": {"properties": {"expiresTimestamp": {"type": "date"}, "messageTimestamp": {"type": "date"}, "originId": {"type": "keyword"}, "ownerId": {"type": "keyword"}, "publishedTimestamp": {"type": "date"}, "publisherId": {"type": "keyword"}, "receivedTimestamp": {"type": "date"}, "traceId": {"type": "keyword"}}}, "journeyReferences": {"properties": {"blockRef": {"copy_to": ["references_kw"], "type": "keyword"}, "datedBlockRef": {"copy_to": ["references_kw"], "type": "keyword"}, "datedServiceJourneyId": {"copy_to": ["references_kw"], "type": "keyword"}, "externalJourneyRef": {"copy_to": ["references_kw"], "type": "keyword"}, "externalJourneyRefV2": {"copy_to": ["references_kw"], "type": "keyword"}, "journeyPatternRef": {"copy_to": ["references_kw"], "type": "keyword"}, "journeyRef": {"copy_to": ["references_kw"], "type": "keyword"}, "legacyDatedJourneyRef": {"copy_to": ["references_kw"], "type": "keyword"}, "legacyJourneyPatternRefs": {"copy_to": ["references_kw"], "type": "keyword"}, "legacyJourneyRef": {"copy_to": ["references_kw"], "type": "keyword"}, "runtimePatternRef": {"copy_to": ["references_kw"], "type": "keyword"}, "vehicleJourneyId": {"copy_to": ["references_kw"], "type": "keyword"}, "vehicleTaskRef": {"copy_to": ["references_kw"], "type": "keyword"}}}, "journeyState": {"properties": {"journeyDeviationState": {"properties": {"deviations": {"properties": {"code": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "ref": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}}}, "omitted": {"type": "boolean"}, "operatingWithoutSignon": {"type": "boolean"}, "partiallyOmitted": {"type": "boolean"}}}, "journeyMitigationState": {"properties": {"cancelled": {"type": "boolean"}, "contingencyVehiclePlanned": {"type": "boolean"}, "partiallyCancelled": {"type": "boolean"}}}}}, "lifeCycleInfo": {"properties": {"created": {"type": "date"}, "dataSource": {"type": "keyword"}, "modified": {"type": "date"}, "revision": {"type": "long"}}}, "line": {"properties": {"backgroundColour": {"type": "text"}, "legacyTransportMode": {"type": "keyword"}, "legacyTransportSubMode": {"type": "keyword"}, "lineRef": {"copy_to": ["references_kw"], "type": "keyword"}, "name": {"type": "text"}, "privateCode": {"type": "keyword"}, "publicCode": {"type": "keyword"}, "textColour": {"type": "text"}, "transportMode": {"type": "keyword"}, "transportModeProperties": {"type": "text"}}}, "name": {"fields": {"keyword": {"type": "keyword"}}, "type": "text"}, "omitted": {"type": "boolean"}, "operatingDate": {"type": "date"}, "operatorContracts": {"properties": {"name": {"type": "text"}, "operator": {"properties": {"name": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "operatorRef": {"type": "keyword"}}}, "operatorContractRef": {"copy_to": ["references_kw"], "type": "keyword"}}}, "operators": {"properties": {"name": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "operatorRef": {"copy_to": ["references_kw"], "type": "keyword"}}}, "order": {"type": "long"}, "plan": {"properties": {"calls": {"properties": {"cancelled": {"type": "boolean"}, "destination": {"properties": {"subText": {"type": "text"}, "text": {"type": "text"}}}, "expectedArrival": {"type": "date"}, "expectedDeparture": {"type": "date"}, "interchange": {"properties": {"from": {"properties": {"arrivalDateTime": {"type": "date"}, "attributes": {"properties": {"direction": {"type": "keyword"}, "operatingDate": {"type": "date"}, "vehicleJourneyId": {"type": "keyword"}}}, "guaranteed": {"type": "boolean"}, "id": {"type": "keyword"}, "maximumWaitTimeSec": {"type": "long"}, "references": {"properties": {"entityDatedJourneyV1Ref": {"type": "keyword"}, "entityDatedJourneyV2CallRef": {"type": "keyword"}, "entityDatedJourneyV2Ref": {"type": "keyword"}, "externalInterchangeRef": {"type": "keyword"}, "externalJourneyId": {"type": "keyword"}, "legacyQuayRef": {"type": "keyword"}, "lineRef": {"type": "keyword"}, "quayRef": {"type": "keyword"}}}}}, "to": {"properties": {"attributes": {"properties": {"direction": {"ignore_above": 256, "type": "keyword"}, "operatingDate": {"type": "date"}, "vehicleJourneyId": {"ignore_above": 256, "type": "keyword"}}}, "departureDateTime": {"type": "date"}, "guaranteed": {"type": "boolean"}, "id": {"ignore_above": 256, "type": "keyword"}, "references": {"properties": {"entityDatedJourneyV1Ref": {"ignore_above": 256, "type": "keyword"}, "entityDatedJourneyV2CallRef": {"ignore_above": 256, "type": "keyword"}, "entityDatedJourneyV2Ref": {"ignore_above": 256, "type": "keyword"}, "externalInterchangeRef": {"ignore_above": 256, "type": "keyword"}, "externalJourneyId": {"ignore_above": 256, "type": "keyword"}, "legacyQuayRef": {"ignore_above": 256, "type": "keyword"}, "lineRef": {"ignore_above": 256, "type": "keyword"}, "quayRef": {"ignore_above": 256, "type": "keyword"}}}, "staySeated": {"type": "boolean"}}}}}, "journeyPatternPointRef": {"ignore_above": 256, "type": "keyword"}, "nextCallStopPointLinkRef": {"ignore_above": 256, "type": "keyword"}, "omitted": {"type": "boolean"}, "order": {"type": "long"}, "originalStopPointBehaviourType": {"ignore_above": 256, "type": "keyword"}, "plannedArrival": {"type": "date"}, "plannedDeparture": {"type": "date"}, "previousCallStopPointLinkRef": {"ignore_above": 256, "type": "keyword"}, "ref": {"ignore_above": 256, "type": "keyword"}, "stopPointBehaviourType": {"ignore_above": 256, "type": "keyword"}, "stopPointRef": {"ignore_above": 256, "type": "keyword"}}, "type": "nested"}, "firstDepartureDateTime": {"type": "date"}, "lastArrivalDateTime": {"type": "date"}, "links": {"properties": {"calculatedLength": {"type": "text"}, "fromQuayRef": {"ignore_above": 256, "type": "keyword"}, "fromStopPointRef": {"ignore_above": 256, "type": "keyword"}, "lifeCycleInfo": {"properties": {"created": {"type": "date"}, "dataSource": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "modified": {"type": "date"}, "revision": {"type": "long"}}}, "measuredLength": {"type": "text"}, "ref": {"ignore_above": 256, "type": "keyword"}, "serviceLinkRef": {"ignore_above": 256, "type": "keyword"}, "stopPointLinkType": {"ignore_above": 256, "type": "keyword"}, "toQuayRef": {"ignore_above": 256, "type": "keyword"}, "toStopPointRef": {"ignore_above": 256, "type": "keyword"}, "trackLine": {"properties": {"geometry": {"properties": {"coordinates": {"type": "float"}, "type": {"ignore_above": 256, "type": "keyword"}}}, "properties": {"type": "object"}, "type": {"ignore_above": 256, "type": "keyword"}}}, "trackLineType": {"ignore_above": 256, "type": "keyword"}}}, "stops": {"properties": {"description": {"type": "text"}, "events": {"properties": {"description": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "source": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "timestamp": {"type": "date"}, "traceId": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "type": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}}}, "geoPoint": {"properties": {"circleRadius": {"type": "long"}, "detectionPointRef": {"ignore_above": 256, "type": "keyword"}, "detectionType": {"ignore_above": 256, "type": "keyword"}, "entryDirection": {"type": "long"}, "entryDirectionWindow": {"type": "long"}, "location": {"properties": {"geometry": {"type": "geo_shape"}, "properties": {"type": "object"}, "type": {"ignore_above": 256, "type": "keyword"}}}, "polygon": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}}}, "header": {"properties": {"messageTimestamp": {"type": "date"}, "receivedTimestamp": {"type": "date"}}}, "legacyQuayRef": {"ignore_above": 256, "type": "keyword"}, "lifeCycleInfo": {"properties": {"created": {"type": "date"}, "dataSource": {"type": "text"}, "modified": {"type": "date"}, "revision": {"type": "long"}}}, "name": {"type": "text"}, "publicCode": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "quayRef": {"ignore_above": 256, "type": "keyword"}, "ref": {"ignore_above": 256, "type": "keyword"}, "stopAreaRef": {"ignore_above": 256, "type": "keyword"}, "stopPlaceDescription": {"type": "text"}, "stopPlaceRef": {"ignore_above": 256, "type": "keyword"}, "tariffZones": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}}, "type": "nested"}}}, "public": {"type": "boolean"}, "ref": {"copy_to": ["references_kw"], "type": "keyword"}, "references_kw": {"type": "keyword"}, "replaces": {"properties": {"journeys": {"properties": {"entityDatedJourneyKeyV2Ref": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "journeyReferences": {"properties": {"externalJourneyRef": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "externalJourneyRefV2": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}}}}}}}, "type": {"type": "keyword"}, "vehicleTask": {"copy_to": ["references_kw"], "type": "keyword"}, "vehicles": {"properties": {"name": {"fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}, "type": "text"}, "standbyVehicleId": {"copy_to": ["references_kw"], "type": "keyword"}, "vehicleRef": {"copy_to": ["references_kw"], "type": "keyword"}}}}}