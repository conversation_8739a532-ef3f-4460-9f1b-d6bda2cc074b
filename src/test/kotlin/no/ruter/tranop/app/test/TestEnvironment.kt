package no.ruter.tranop.app.test

import no.ruter.tranop.app.common.dataflow.kafka.KafkaTestEnvironment
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
import no.ruter.tranop.assignmentmanager.db.sql.DefaultSchema
import org.jooq.DSLContext
import org.springframework.stereotype.Service

@Service
class TestEnvironment(
    val jooq: DSLContext,
    val kafka: KafkaTestEnvironment,
    val snowflake: SnowflakeTestClientFactory,
) {
    fun reset() {
        clearKafka()
        clearDatabase()
        clearSnowflake()
    }

    fun clearKafka() {
        kafka.cleanOutputTopics()
    }

    fun clearDatabase() {
        DefaultSchema.DEFAULT_SCHEMA.tables.forEach { table ->
            jooq.deleteFrom(table).execute()
        }
    }

    fun clearSnowflake() {
        snowflake.reset()
    }
}
