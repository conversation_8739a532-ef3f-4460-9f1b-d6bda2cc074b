package no.ruter.tranop.app.test

import io.micrometer.core.instrument.Measurement
import io.micrometer.core.instrument.Meter
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import jakarta.annotation.PostConstruct
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.Application
import no.ruter.tranop.app.common.dataflow.kafka.KafkaTestConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TestTimeService
import no.ruter.tranop.app.event.journey.db.JourneyEventRepository
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.event.journey.output.JourneyEventOutboxSnowflakeIngestingRoutine
import no.ruter.tranop.app.event.traffic.config.TrafficEventConfig
import no.ruter.tranop.app.event.traffic.input.TrafficEventInputService
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputService
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputData
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputRepository
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.app.plan.journey.output.opensearch.DatedJourneyOpenSearchPublishRoutine
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import no.ruter.tranop.app.plan.link.input.StopPointLinkInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.app.plan.stop.input.StopPointInputService
import no.ruter.tranop.app.variance.deviation.output.ServiceDeviationOutboxSnowflakeIngestingRoutine
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.flyway.FlywayDataSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.time.Duration
import kotlin.test.assertNotNull
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@SpringBootTest(
    classes = [
        Application::class,
        TestConfig::class,
        KafkaTestConfig::class,
    ],
    properties = [
        "spring.main.allow-bean-definition-overriding=true",
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration",
    ],
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@FlywayDataSource
@ActiveProfiles(value = ["test", "local"])
@AutoConfigureEmbeddedDatabase(
    type = AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES,
    provider = AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY,
    refresh = AutoConfigureEmbeddedDatabase.RefreshMode.AFTER_EACH_TEST_METHOD,
)
abstract class AbstractBaseTest {
    protected val log: Logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var datedJourneyConfigProperties: DatedJourneyConfigProperties

    @Autowired
    lateinit var datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig

    @Autowired
    lateinit var testEnvironment: TestEnvironment

    @Autowired
    lateinit var timeService: TestTimeService

    @Autowired
    lateinit var insightService: InsightService

    @Autowired
    lateinit var journeyRepo: DatedJourneyRepository

    @Autowired
    lateinit var journeyInputRepo: DatedJourneyInputRepository

    @Autowired
    lateinit var datedJourneyService: DatedJourneyService

    @Autowired
    lateinit var journeyEventRepository: JourneyEventRepository

    @Autowired
    lateinit var journeyEventService: JourneyEventInputService

    @Autowired
    lateinit var trafficEventService: TrafficEventInputService

    @Autowired
    lateinit var trafficEventConfig: TrafficEventConfig

    @Autowired
    lateinit var datedJourneyInputService: DatedJourneyInputService

    @Autowired
    lateinit var stopPointRepository: StopPointRepository

    @Autowired
    lateinit var stopPointLinkRepository: StopPointLinkRepository

    @Autowired
    lateinit var outboxRepository: OutboxRepository

    @Autowired
    lateinit var outboxService: OutboxService

    @Autowired
    lateinit var publishDatedJourneyToOpenSearchRoutine: DatedJourneyOpenSearchPublishRoutine

    @Autowired
    lateinit var journeyEventOutboxSnowflakeIngestingRoutine: JourneyEventOutboxSnowflakeIngestingRoutine

    @Autowired
    lateinit var serviceDeviationSnowflakeIngestingRoutine: ServiceDeviationOutboxSnowflakeIngestingRoutine

    @Autowired
    lateinit var stopPointLinkInputService: StopPointLinkInputService

    @Autowired
    lateinit var stopPointInputService: StopPointInputService

    val meterRegistry by lazy { insightService.metricsService.meterRegistry }

    @PostConstruct
    fun setUpConfig() {
        // Do something?
    }

    val metricsMap: List<Pair<Meter, MutableIterable<Measurement>>>
        get() = meterRegistry.meters.map { it to it.measure() }

    @BeforeEach
    fun setup() {
        timeService.reset()
        meterRegistry.clear()
    }

    protected fun adjustTime(
        journey: PDJDatedJourney,
        offset: Duration? = null,
    ) = timeService.adjustJourneyTime(journey, offset)

    protected fun adjustTime(
        journey: DTODatedJourney,
        offset: Duration? = null,
    ) = timeService.adjustJourneyTime(journey, offset)

    fun printMetrics(): String {
        val msg =
            mutableListOf<String>()
                .apply {
                    add("<--- all metrics ---")
                    addAll(metricsMap.prettyList())
                    add("--- all metrics --->")
                }.joinToString(separator = "\n", postfix = "\n\n", prefix = "\n\n")
        log.info("Metrics: $msg")
        return msg
    }

    protected fun storeDatedJourneys(vararg datedJourneys: PDJDatedJourney): Int {
        var res = 0
        datedJourneys.forEach { res += storeDatedJourney(it.ref, it) }
        return res
    }

    protected fun storeDatedJourneys(datedJourneys: Collection<PDJDatedJourney>): Int {
        var res = 0
        datedJourneys.forEach { res += storeDatedJourney(it.ref, it) }
        return res
    }

    protected fun storeDatedJourneys(journeys: Map<String, List<PDJDatedJourney>>): Int {
        var res = 0
        journeys.values.forEach { taskJourneys ->
            taskJourneys.forEach {
                res += storeDatedJourney(it.ref, it)
            }
        }
        return res
    }

    protected fun storeDatedJourney(
        key: String,
        datedJourney: PDJDatedJourney,
    ): Int {
        adjustTime(datedJourney)
        val contex = datedJourneyInputService.process(key, datedJourney)
        insightService.insight(contex)
        return contex.stored
    }

    /** Store stop points with quay ref keyed by stop point ref. **/
    fun storeStopPoints(stopPoints: Map<String, String>) {
        stopPoints.entries.forEach { e ->
            val input = DTOStopPoint().withRef(e.key).withQuayRef(e.value)
            stopPointRepository.store(StopPointInputContext(stopPoint = input))
        }
    }

    protected fun assertJourneyExists(ref: String?): DTODatedJourney = assertJourneyRecordExists(ref).data

    protected fun assertJourneyInputExists(ref: String?): DTODatedJourney = assertJourneyInputRecordExists(ref).data

    protected fun assertJourneyRecordExists(ref: String?): InternalDatedJourney = assertNotNull(journeyRepo.fetchByRef(ref))

    protected fun assertJourneyInputRecordExists(ref: String?): DatedJourneyInputData = assertNotNull(journeyInputRepo.fetchByRef(ref))

    protected fun awaitUntilAsserted(
        desc: String = "Awaiting stuff to be asserted",
        pollInterval: kotlin.time.Duration = 100.milliseconds,
        pollDelay: kotlin.time.Duration = kotlin.time.Duration.ZERO,
        atMost: kotlin.time.Duration = 30.seconds,
        block: () -> Unit,
    ) {
        await(desc)
            .pollDelay(pollDelay.toJavaDuration()) // start checking immediately
            .pollInterval(pollInterval.toJavaDuration()) // every 1s
            .atMost((atMost).toJavaDuration()) // up to 32s
            .untilAsserted {
                block()
            }
    }
}
