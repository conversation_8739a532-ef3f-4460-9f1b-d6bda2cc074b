package no.ruter.tranop.app.common.dataflow.opensearch

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest

class OpenSearchTest : AbstractOpenSearchTest() {
    @AfterEach
    fun afterEach() {
        openSearchSingleNode
            .client()
            .admin()
            .indices()
            .delete(DeleteIndexRequest("*"))
            .get()
    }

    @Test
    fun `test index creation`() {
        val index = "some-index"
        openSearchService.createIndexIfNotExists(index)
        val result = openSearchService.listIndexes()
        Assertions.assertTrue(result.contains(index))
    }

    @Test
    fun `create existing index shouldn't fail and should be no op`() {
        val index = "some-index"
        val result = openSearchService.createIndexIfNotExists(index)
        Assertions.assertTrue(result)

        val result2 = openSearchService.createIndexIfNotExists(index)
        Assertions.assertFalse(result2)
    }
}
