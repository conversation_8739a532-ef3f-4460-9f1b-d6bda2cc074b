package no.ruter.tranop.app.common.db

import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import org.junit.jupiter.api.Assertions
import kotlin.test.Test

class ExternalRefsTest {
    @Test
    fun testExternalRefs() {
        val refs = ExternalRefs()
        Assertions.assertTrue(refs.isEmpty())
        Assertions.assertEquals(0, refs.size)
        Assertions.assertEquals(0, refs.all().size)
        Assertions.assertFalse(refs.isNotEmpty())

        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-1")
        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-1")
        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-2")
        Assertions.assertEquals(2, refs.size) // duplicate "quay-1" should not count
        Assertions.assertFalse(refs.isEmpty())
        Assertions.assertTrue(refs.isNotEmpty())

        refs.add(ExternalRefType.LINE_ID, "line-1")
        refs.add(ExternalRefType.LINE_ID, "line-2")
        refs.add(ExternalRefType.LINE_ID, "line-1")
        Assertions.assertEquals(4, refs.size) // duplicate "line-1" should not count

        val all = refs.all()
        Assertions.assertEquals(2, all.keys.size)
        Assertions.assertEquals(2, all[ExternalRefType.LINE_ID]?.size)
        Assertions.assertEquals(2, all[ExternalRefType.NSR_QUAY_ID]?.size)
    }

    @Test
    fun testNullValues() {
        val refs = ExternalRefs()

        refs.add(null, "value")
        Assertions.assertEquals(0, refs.size)
        Assertions.assertTrue(refs.isEmpty())

        refs.add(ExternalRefType.LINE_ID, null as String?)
        Assertions.assertEquals(0, refs.size)
        Assertions.assertTrue(refs.isEmpty())

        refs.add(ExternalRefType.LINE_ID, "")
        Assertions.assertEquals(0, refs.size)
        Assertions.assertTrue(refs.isEmpty())

        refs.add(ExternalRefType.LINE_ID, "line-1")
        Assertions.assertEquals(1, refs.size)
        Assertions.assertFalse(refs.isEmpty())
    }

    @Test
    fun testMultipleExternalRefTypes() {
        val refs = ExternalRefs()

        refs.add(ExternalRefType.LINE_ID, "line-1")
        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-1")
        refs.add(ExternalRefType.STOP_POINT_REF, "stop-1")
        refs.add(ExternalRefType.DATED_JOURNEY_V2_REF, "dj-1")
        refs.add(ExternalRefType.PTO_CASE_REF, "pto-1")
        refs.add(ExternalRefType.PTA_CASE_REF, "pta-1")
        refs.add(ExternalRefType.ENTITY_TRAFFIC_CASE_KEY_V2_REF, "traffic-case-1")
        refs.add(ExternalRefType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF, "traffic-event-1")
        refs.add(ExternalRefType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF, "traffic-situation-1")

        Assertions.assertEquals(9, refs.size)
        Assertions.assertEquals(9, refs.all().keys.size)

        Assertions.assertNotNull(refs.first(ExternalRefType.LINE_ID))
        Assertions.assertNotNull(refs.first(ExternalRefType.NSR_QUAY_ID))
        Assertions.assertNotNull(refs.first(ExternalRefType.STOP_POINT_REF))
        Assertions.assertNotNull(refs.first(ExternalRefType.PTO_CASE_REF))
        Assertions.assertNotNull(refs.first(ExternalRefType.PTA_CASE_REF))
        Assertions.assertNotNull(refs.first(ExternalRefType.ENTITY_TRAFFIC_CASE_KEY_V2_REF))
        Assertions.assertNotNull(refs.first(ExternalRefType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF))
        Assertions.assertNotNull(refs.first(ExternalRefType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF))
    }
}
