package no.ruter.tranop.app.common.dataflow.snowflake

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.event.journey.output.JourneyEventOutboxSnowflakeIngestingService

abstract class AbstractSnowflakeTest : AbstractKafkaTest() {
    val journeyEventSnowflakeChannel
        get() =
            getChannel(
                client = getClient(name = JourneyEventOutboxSnowflakeIngestingService.CLIENT_NAME),
                name = JourneyEventOutboxSnowflakeIngestingService.CHANNEL_NAME,
            )

    private fun getClient(name: String): SnowflakeTestClient =
        testEnvironment.snowflake.clients.get(name) ?: throw AssertionError("Missing required client $name")

    private fun getChannel(
        client: SnowflakeTestClient,
        name: String,
    ): SnowflakeTestChannel {
        val channels =
            client
                .channels
                .filter { it.key.startsWith(name) }
                .values
        if (channels.size > 1) {
            throw AssertionError("Too many channels $channels")
        }

        return channels
            .firstOrNull() ?: throw AssertionError("Missing required channel $name")
    }
}
