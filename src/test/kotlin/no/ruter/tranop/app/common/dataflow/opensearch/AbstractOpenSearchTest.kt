package no.ruter.tranop.app.common.dataflow.opensearch

import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeTest
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.opensearch.action.admin.cluster.health.ClusterHealthRequest
import org.opensearch.action.admin.indices.create.CreateIndexRequest
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest
import org.opensearch.action.admin.indices.exists.indices.IndicesExistsRequest
import org.opensearch.action.admin.indices.refresh.RefreshRequest
import org.opensearch.client.Client
import org.opensearch.cluster.health.ClusterHealthStatus
import org.opensearch.common.settings.Settings
import org.opensearch.common.unit.TimeValue
import org.opensearch.common.xcontent.XContentType
import org.opensearch.geo.GeoModulePlugin
import org.opensearch.node.InternalSettingsPreparer
import org.opensearch.node.Node
import org.opensearch.transport.Netty4Plugin
import org.springframework.beans.factory.annotation.Autowired
import java.nio.file.Paths
import kotlin.io.path.ExperimentalPathApi
import kotlin.io.path.deleteRecursively

abstract class AbstractOpenSearchTest : AbstractSnowflakeTest() {
    @Autowired
    lateinit var openSearchService: OpenSearchService

    @Autowired
    lateinit var openSearchProperties: OpenSearchProperties

    val openSearchIndexName get() = openSearchProperties.journeyIndexName

    companion object {
        private const val BASE_DATA_PATH = "build/opensearch-test-data"
        private const val NODE_NAME = "test-node"
        private val CONF_PATH = Paths.get("common")

        lateinit var openSearchSingleNode: Node
        private lateinit var client: Client

        @JvmStatic
        @BeforeAll
        fun setupOs() {
            try {
                println("Starting OpenSearch test node...")

                val settings = getOsSettings()
                val env = InternalSettingsPreparer.prepareEnvironment(settings, emptyMap(), CONF_PATH) { NODE_NAME }
                openSearchSingleNode = OpenSearchNode(env, listOf(Netty4Plugin::class.java, GeoModulePlugin::class.java))
                openSearchSingleNode.start()

                client = openSearchSingleNode.client()

                // Wait for cluster to be ready
                waitForClusterReady()

                // Create test indices with mappings
                createTestIndices()

                println("OpenSearch test setup completed successfully")
            } catch (e: Exception) {
                println("Failed to setup OpenSearch: ${e.message}")
                e.printStackTrace()
                throw e
            }
        }

        @OptIn(ExperimentalPathApi::class)
        @JvmStatic
        @AfterAll
        fun tearDown() {
            try {
                println("Tearing down OpenSearch test node...")

                // Clean up test indices
                cleanupTestIndices()

                // Close the node
                if (::openSearchSingleNode.isInitialized) {
                    openSearchSingleNode.close()
                }

                // Clean up data directory
                val dataPath = Paths.get(BASE_DATA_PATH, NODE_NAME)
                if (dataPath.toFile().exists()) {
                    dataPath.deleteRecursively()
                    println("Cleaned up data directory: $dataPath")
                }

                println("OpenSearch teardown completed")
            } catch (e: Exception) {
                println("Error during teardown: ${e.message}")
                e.printStackTrace()
            }
        }

        private fun getOsSettings(): Settings {
            val homePath = Paths.get(BASE_DATA_PATH, NODE_NAME)
            return Settings
                .builder()
                .put("path.home", homePath.toString())
                .putList("node.roles", "cluster_manager", "data")
                .put("http.port", "9200")
                .put("transport.port", "9300")
                .put("discovery.type", "single-node")
                .put("cluster.name", "test-cluster")
                .put("node.name", NODE_NAME)
                .build()
        }

        private fun waitForClusterReady() {
            try {
                println("Waiting for cluster to be ready...")

                val request =
                    ClusterHealthRequest()
                        .waitForStatus(ClusterHealthStatus.YELLOW)
                        .timeout(TimeValue.timeValueSeconds(30))

                val response =
                    client
                        .admin()
                        .cluster()
                        .health(request)
                        .actionGet()

                if (response.isTimedOut) {
                    throw RuntimeException("Cluster health check timed out")
                }

                println("Cluster is ready with status: ${response.status}")
            } catch (e: Exception) {
                println("Failed to wait for cluster ready: ${e.message}")
                throw e
            }
        }

        private fun createTestIndices() {
            val indicesToCreate =
                mapOf(
                    "journey" to "common/journey-index-mapping.json",
                )

            indicesToCreate.forEach { (indexName, mappingFile) ->
                createIndexWithMapping(indexName, mappingFile)
            }
        }

        private fun createIndexWithMapping(
            indexName: String,
            mappingFilePath: String,
        ) {
            try {
                // Check if index already exists
                val existsRequest = IndicesExistsRequest(indexName)
                val exists =
                    client
                        .admin()
                        .indices()
                        .exists(existsRequest)
                        .actionGet()
                        .isExists

                if (exists) {
                    println("Index '$indexName' already exists, skipping creation")
                    return
                }

                // Load mapping from file
                val mapping = loadMappingFromFile(mappingFilePath)

                // Create index with mapping and settings
                val createRequest =
                    CreateIndexRequest(indexName)
                        .mapping(mapping, XContentType.JSON)
                        .settings(getIndexSettings())

                val response =
                    client
                        .admin()
                        .indices()
                        .create(createRequest)
                        .actionGet()

                if (response.isAcknowledged) {
                    println("Successfully created index '$indexName' with mapping from '$mappingFilePath'")
                } else {
                    println("Warning: Index '$indexName' creation was not acknowledged")
                }
            } catch (e: Exception) {
                println("Failed to create index '$indexName': ${e.message}")
                e.printStackTrace()
                throw RuntimeException("Failed to create index '$indexName'", e)
            }
        }

        private fun loadMappingFromFile(mappingFilePath: String): String =
            try {
                val inputStream =
                    AbstractOpenSearchTest::class.java.classLoader
                        .getResourceAsStream(mappingFilePath)
                        ?: throw IllegalArgumentException("Mapping file not found: $mappingFilePath")

                inputStream.bufferedReader().use { reader ->
                    reader.readText().also { content ->
                        if (content.isBlank()) {
                            throw IllegalArgumentException("Mapping file is empty: $mappingFilePath")
                        }
                        println("Loaded mapping from '$mappingFilePath' (${content.length} characters)")
                    }
                }
            } catch (e: Exception) {
                println("Error loading mapping from file '$mappingFilePath': ${e.message}")
                throw RuntimeException("Failed to load mapping file: $mappingFilePath", e)
            }

        private fun getIndexSettings(): Settings =
            Settings
                .builder()
                .put("number_of_shards", 1)
                .put("number_of_replicas", 0)
                .put("refresh_interval", "1s") // Fast refresh for testing
                .build()

        private fun cleanupTestIndices() {
            val indicesToDelete = listOf("users", "products")

            indicesToDelete.forEach { indexName ->
                try {
                    val existsRequest = IndicesExistsRequest(indexName)
                    val exists =
                        client
                            .admin()
                            .indices()
                            .exists(existsRequest)
                            .actionGet()
                            .isExists

                    if (exists) {
                        val deleteRequest = DeleteIndexRequest(indexName)
                        val response =
                            client
                                .admin()
                                .indices()
                                .delete(deleteRequest)
                                .actionGet()

                        if (response.isAcknowledged) {
                            println("Successfully deleted index '$indexName'")
                        } else {
                            println("Warning: Index '$indexName' deletion was not acknowledged")
                        }
                    }
                } catch (e: Exception) {
                    println("Error deleting index '$indexName': ${e.message}")
                    // Don't throw here, continue with cleanup
                }
            }
        }

        // Utility method to get client for tests
        fun getClient(): Client {
            if (!::client.isInitialized) {
                throw IllegalStateException("OpenSearch client is not initialized. Make sure setupOs() was called.")
            }
            return client
        }

        // Utility method to check if an index exists
        fun indexExists(indexName: String): Boolean =
            try {
                val request = IndicesExistsRequest(indexName)
                client
                    .admin()
                    .indices()
                    .exists(request)
                    .actionGet()
                    .isExists
            } catch (e: Exception) {
                println("Error checking if index '$indexName' exists: ${e.message}")
                false
            }
    }

    fun refreshOpenSearchIndices() {
        try {
            openSearchSingleNode
                .client()
                .admin()
                .indices()
                .refresh(RefreshRequest(openSearchIndexName))
                .get()
        } catch (e: Exception) {
            log.info("Index error: $openSearchIndexName -> ${e.message}", e)
        }
    }
}
