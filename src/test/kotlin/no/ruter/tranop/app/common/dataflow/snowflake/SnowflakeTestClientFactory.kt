package no.ruter.tranop.app.common.dataflow.snowflake

class SnowflakeTestClientFactory : SnowflakeClientFactory() {
    val clients = LinkedHashMap<String, SnowflakeTestClient>()

    // Defaults applied to any newly opened channel
    private var defaultRowErrors: List<Int> = emptyList()
    private var defaultRowExceptions: Map<Int, RuntimeException> = emptyMap()

    /** Reset all clients by clearing all channels. **/
    fun reset() {
        clients.values.forEach(SnowflakeTestClient::reset)
        defaultRowErrors = emptyList()
        defaultRowExceptions = emptyMap()
    }

    fun setDefaultRowErrors(indexes: List<Int>) {
        defaultRowErrors = indexes
        // propagate to any existing clients as well
        clients.values.forEach { it.setDefaults(defaultRowErrors, defaultRowExceptions) }
    }

    fun setDefaultRowExceptions(ex: Map<Int, RuntimeException>) {
        defaultRowExceptions = ex
        // propagate to any existing clients as well
        clients.values.forEach { it.setDefaults(defaultRowErrors, defaultRowExceptions) }
    }

    override fun createClient(
        name: String,
        config: SnowflakeClientProperties,
    ): SnowflakeTestClient {
        val client = SnowflakeTestClient(name)
        clients[name] = client
        return client
    }

    fun channel(
        clientName: String,
        channelName: String,
    ): SnowflakeTestChannel {
        val client = clients[clientName] ?: throw IllegalArgumentException("client not found: $clientName")
        val prefix = "$channelName-"
        for ((key, value) in client.channels.entries) {
            if (key.startsWith(prefix)) {
                return value
            }
        }
        throw IllegalArgumentException("channel not found: $channelName")
    }
}
