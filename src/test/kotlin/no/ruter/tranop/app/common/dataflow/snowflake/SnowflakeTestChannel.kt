package no.ruter.tranop.app.common.dataflow.snowflake

import net.snowflake.ingest.streaming.InsertValidationResponse
import net.snowflake.ingest.streaming.OpenChannelRequest
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestChannel
import net.snowflake.ingest.streaming.internal.ColumnProperties
import java.util.concurrent.CompletableFuture

class SnowflakeTestChannel(
    val request: OpenChannelRequest,
) : SnowflakeStreamingIngestChannel {
    companion object {
        const val OFFSET_TOKEN_NONE = "-"
        const val CHANNEL_NAME_NONE = "-"
    }

    val rows = ArrayList<Map<String?, Any?>?>()
    val rowErrors = LinkedHashSet<Int>()
    val rowExceptions = LinkedHashMap<Int, RuntimeException>()

    var offsetToken: String = OFFSET_TOKEN_NONE

    /** Reset channel by clearing all rows. **/
    fun reset() {
        rows.clear()
        rowErrors.clear()
        rowExceptions.clear()

        offsetToken = OFFSET_TOKEN_NONE
    }

    /**
     * Provide list of indexes for rows to fail ingestion. Note that indexes must be absolute, i.e. _not_ relative to how many times
     * ingest() is called on channel.
     **/
    fun setRowErrors(rowIndexes: List<Int>) {
        rowErrors.addAll(rowIndexes)
    }

    /**
     * Provide a map of indexes for rows to throw exception on ingestion. Note that indexes must be absolute, i.e. _not_ relative to how
     * many times ingest() is called on channel.
     **/
    fun setRowExceptions(exceptions: Map<Int, RuntimeException>) {
        rowExceptions.putAll(exceptions)
    }

    override fun getName(): String? = request.channelName

    override fun getDBName(): String? = request.dbName

    override fun getSchemaName(): String? = request.schemaName

    override fun getTableName(): String? = request.tableName

    override fun getFullyQualifiedName(): String? = "$fullyQualifiedTableName.$name"

    override fun getFullyQualifiedTableName(): String? = request.fullyQualifiedTableName ?: "$dbName.$schemaName.$tableName"

    override fun isValid(): Boolean = throw NotImplementedError("mock not implemented: isValid()")

    override fun isClosed(): Boolean = throw NotImplementedError("mock not implemented: isClosed()")

    override fun close(): CompletableFuture<Void?>? = CompletableFuture.completedFuture(null)

    override fun close(drop: Boolean): CompletableFuture<Void?>? = CompletableFuture.completedFuture(null)

    override fun insertRow(
        row: Map<String?, Any?>?,
        offsetToken: String?,
    ): InsertValidationResponse? = insertRows(listOf(row), offsetToken)

    override fun insertRows(
        rows: Iterable<Map<String?, Any?>?>?,
        startOffsetToken: String?,
        endOffsetToken: String?,
    ): InsertValidationResponse? {
        this.offsetToken = startOffsetToken ?: OFFSET_TOKEN_NONE
        return insertRows(rows, endOffsetToken)
    }

    override fun insertRows(
        rows: Iterable<Map<String?, Any?>?>?,
        offsetToken: String?,
    ): InsertValidationResponse? {
        val n = this.rows.size
        val errors = ArrayList<InsertValidationResponse.InsertError>()
        rows?.forEachIndexed { i, row ->
            val nextIndex = n + i
            if (rowErrors.contains(nextIndex)) {
                val error = InsertValidationResponse.InsertError(row, i.toLong())
                errors.add(error)
            } else if (rowExceptions.containsKey(nextIndex)) {
                throw rowExceptions.get(nextIndex) ?: IllegalStateException("internal error in mock")
            } else {
                this.rows.add(row)
            }
        }
        this.offsetToken = offsetToken ?: OFFSET_TOKEN_NONE
        return object : InsertValidationResponse() {
            private val errs = errors.toMutableList()
            override fun hasErrors(): Boolean = errs.isNotEmpty()
            override fun getInsertErrors(): MutableList<InsertValidationResponse.InsertError> = errs
        }
    }

    override fun getLatestCommittedOffsetToken(): String? = offsetToken

    override fun getTableSchema(): Map<String?, ColumnProperties?>? = throw NotImplementedError("table schema not implemented by mock")
}
