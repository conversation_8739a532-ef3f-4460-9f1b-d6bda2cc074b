package no.ruter.tranop.app.common.dataflow.snowflake

import net.snowflake.ingest.streaming.DropChannelRequest
import net.snowflake.ingest.streaming.OpenChannelRequest
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestChannel
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClient
import no.ruter.rdp.logging.LoggerFactory
import java.util.concurrent.CompletableFuture

class SnowflakeTestClient(
    val clientName: String,
) : SnowflakeStreamingIngestClient {
    val log = LoggerFactory.getLogger(javaClass)

    val channels = LinkedHashMap<String, SnowflakeTestChannel>()

    // Defaults to apply to new channels
    private var defaultRowErrors: List<Int> = emptyList()
    private var defaultRowExceptions: Map<Int, RuntimeException> = emptyMap()

    fun setDefaults(rowErrors: List<Int>, rowExceptions: Map<Int, RuntimeException>) {
        defaultRowErrors = rowErrors
        defaultRowExceptions = rowExceptions
    }

    /** Reset client by clearing all channels. **/
    fun reset() {
        channels.values.forEach(SnowflakeTestChannel::reset)
    }

    override fun openChannel(request: OpenChannelRequest?): SnowflakeStreamingIngestChannel? {
        val channelName = request?.channelName ?: SnowflakeTestChannel.CHANNEL_NAME_NONE
        log.info("open channel: $channelName")
        val channel =
            request?.let {
                SnowflakeTestChannel(request).apply {
                    if (defaultRowErrors.isNotEmpty()) setRowErrors(defaultRowErrors)
                    if (defaultRowExceptions.isNotEmpty()) setRowExceptions(defaultRowExceptions)
                }
            } ?: throw IllegalArgumentException("Unable open channel: null request")
        channels[channelName] = channel
        return channel
    }

    override fun dropChannel(request: DropChannelRequest?) {
        val channelName = request?.channelName ?: SnowflakeTestChannel.CHANNEL_NAME_NONE
        channels.remove(channelName)
        log.info("drop channel: $channelName")
    }

    override fun getName(): String? = clientName

    override fun setRefreshToken(refreshToken: String?) = throw NotImplementedError("mock not implemented: setRefreshToken()")

    override fun flush(): CompletableFuture<Void?>? = throw NotImplementedError("mock not implemented: flush()")

    override fun isClosed(): Boolean = throw NotImplementedError("mock not implemented: isClosed()")

    override fun getLatestCommittedOffsetTokens(channels: List<SnowflakeStreamingIngestChannel?>?): Map<String?, String?>? =
        throw NotImplementedError("mock not implemented: getLatestCommittedOffsetTokens()")

    override fun close() {
        // No action required.
    }
}
