package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyOperatingWithoutSignon
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class TrafficEventOperatingWithoutSignonTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - Operating without Sign-On - Reported true`() {
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.OPERATING_WITHOUT_SIGN_ON]).hasSize(1)
        Assertions.assertThat(storedJourney.journeyState.journeyDeviationState.operatingWithoutSignon).isTrue
    }

    @Test
    fun `TrafficEvent - Operating without Sign-On - Reported false`() {
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, false)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(storedJourney.journeyState.journeyDeviationState.operatingWithoutSignon).isFalse
    }

    private fun createEvent(
        journeyRef: String,
        reported: Boolean?,
    ): DTOTrafficEvent =
        DTOTrafficEvent().apply {
            header =
                DTOMessageHeader().apply {
                    messageTimestamp = timeService.now().toUtcIsoString()
                    traceId = "Test - TrafficEvent - Signon"
                }
            journeyRefs =
                DTOTrafficEventJourneyReferences().apply {
                    entityDatedJourneyKeyV2Ref = journeyRef
                }
            operatingWithoutSignon =
                reported?.let {
                    DTOTrafficEventJourneyOperatingWithoutSignon().apply { this.active = it }
                }
        }

    @Test
    fun `TrafficEvent on djv2 - Operating without Sign-On - Reported true then recalled`() {
        trafficEventConfig.operatingWithoutSignon.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-b028f3-dated-journey.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent = TestUtils.readTrafficEvent("traffic-event/djj-b028f3-traffic-event-signon.json")
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events[DTOEventType.OPERATING_WITHOUT_SIGN_ON]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(storedJourney.journeyState.journeyDeviationState.operatingWithoutSignon).isTrue

        val trafficEventRecalled = TestUtils.readTrafficEvent("traffic-event/djj-b028f3-traffic-event-signon-recalled.json")
        val contextRecalled = trafficEventService.processInput(journey.ref, trafficEventRecalled)
        Assertions
            .assertThat(
                contextRecalled.valid(),
            ).`as`(contextRecalled.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourneyRecalled = assertJourneyExists(journey.ref)
        val eventsRecalled = storedJourneyRecalled.events.groupBy { it.type }
        Assertions.assertThat(eventsRecalled[DTOEventType.OPERATING_WITHOUT_SIGN_ON]).hasSize(1)
        Assertions.assertThat(eventsRecalled[DTOEventType.OPERATING_WITHOUT_SIGN_ON_RECALLED]).hasSize(1)
        Assertions.assertThat(eventsRecalled[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(storedJourneyRecalled.journeyState.journeyDeviationState.operatingWithoutSignon).isFalse
    }
}
