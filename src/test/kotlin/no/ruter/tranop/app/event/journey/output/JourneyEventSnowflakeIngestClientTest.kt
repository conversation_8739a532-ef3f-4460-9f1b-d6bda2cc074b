package no.ruter.tranop.app.event.journey.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
import no.ruter.tranop.app.event.journey.db.InternalJourneyEvent
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventDelay
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOmission
import no.ruter.tranop.journey.event.dto.model.common.DTOJourneyEventCall
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class JourneyEventSnowflakeIngestClientTest {
    val clientFactory = SnowflakeTestClientFactory()
    val defaultChannel = RecordType.DATED_JOURNEY.channels.internal

    val streamingIngestClient =
        JourneyEventSnowflakeIngestClient(
            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
            clientFactory = clientFactory,
        )

    @BeforeEach
    fun setUp() {
        clientFactory.reset()
    }

    @Test
    fun `stream sends data to Snowflake successfully with cancellation`() {
        val journeyEventContext =
            JourneyEventInputContext(
                channel = defaultChannel,
                received = OffsetDateTime.now(),
                internalEvent =
                    InternalJourneyEvent(
                        event =
                            DTOJourneyEvent().apply {
                                this.entityDatedJourneyKeyV2Ref = "sampleRef"
                                this.cancellation =
                                    DTOJourneyEventCancellation().apply {
                                        this.cancelled = true
                                        this.calls =
                                            listOf(
                                                DTOJourneyEventCall().apply {
                                                    this.nsrQuayId = "quay-123"
                                                    this.time = OffsetDateTime.now().toString()
                                                },
                                            )
                                    }
                            },
                    ),
            )
        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", journeyEventContext.toBIJourneyEvent())))

        val capturedMap = firstIngestedRow()
        assertEquals(null, capturedMap["ASSIGNMENT_REF"])
        assertNotNull(capturedMap["CREATED_AT"])
        assertEquals("sampleRef", capturedMap["DATED_JOURNEY_V2_REF"])
        assertEquals(journeyEventContext.ref, capturedMap["REF"])
        assertEquals("CANCELLATION", capturedMap["TYPE"])
        val metadataString = capturedMap["METADATA"].toString()
        val metadata = JsonUtils.toJsonNode(metadataString)
        assertTrue(metadata["cancelled"].booleanValue())
        val calls = metadata["callsCancelled"]
        assertEquals(1, calls.size())
        assertEquals("quay-123", calls[0]["nsrQuayId"].textValue())
        assertNotNull(calls[0]["time"])
    }

    @Test
    fun `stream sends data to Snowflake successfully with omission`() {
        val journeyEventContext =
            JourneyEventInputContext(
                channel = defaultChannel,
                received = OffsetDateTime.now(),
                internalEvent =
                    InternalJourneyEvent(
                        event =
                            DTOJourneyEvent().apply {
                                this.entityDatedJourneyKeyV2Ref = "sampleRef"
                                this.omission =
                                    DTOJourneyEventOmission().apply {
                                        this.vehicleTaskId = "task-123"
                                        this.mqttOperatorId = "operator-789"
                                        this.omitted = true
                                        this.calls =
                                            listOf(
                                                DTOJourneyEventCall().apply {
                                                    this.nsrQuayId = "quay-123"
                                                    this.time = OffsetDateTime.now().toString()
                                                },
                                            )
                                    }
                            },
                    ),
            )
        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", journeyEventContext.toBIJourneyEvent())))

        val capturedMap = firstIngestedRow()
        assertEquals(null, capturedMap["ASSIGNMENT_REF"])
        assertNotNull(capturedMap["CREATED_AT"])
        assertEquals("sampleRef", capturedMap["DATED_JOURNEY_V2_REF"])
        assertEquals(journeyEventContext.ref, capturedMap["REF"])
        assertEquals("OMITTED", capturedMap["TYPE"])
        val metadataString = capturedMap["METADATA"].toString()
        val metadata = JsonUtils.toJsonNode(metadataString)
        assertEquals("task-123", metadata["omitted"]["vehicleTaskId"].textValue())
        assertEquals("operator-789", metadata["omitted"]["mqttOperatorId"].textValue())
        assertTrue(metadata["omitted"]["omitted"].booleanValue())
        val omittedCalls = metadata["omitted"]["calls"]
        assertEquals(1, omittedCalls.size())
    }

    @Test
    fun `stream sends data to Snowflake successfully with delay`() {
        val journeyEventContext =
            JourneyEventInputContext(
                channel = defaultChannel,
                received = OffsetDateTime.now(),
                internalEvent =
                    InternalJourneyEvent(
                        event =
                            DTOJourneyEvent().apply {
                                this.id = "sample-id-Ref"
                                this.entityDatedJourneyKeyV2Ref = "sampleRef"
                                this.delay =
                                    DTOJourneyEventDelay().apply {
                                        this.delayMinutes = 300
                                    }
                            },
                    ),
            )
        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", journeyEventContext.toBIJourneyEvent())))

        val capturedMap = firstIngestedRow()
        assertEquals(null, capturedMap["ASSIGNMENT_REF"])
        assertNotNull(capturedMap["CREATED_AT"])
        assertEquals("sampleRef", capturedMap["DATED_JOURNEY_V2_REF"])
        assertEquals(journeyEventContext.ref, capturedMap["REF"])
        assertEquals("DELAY", capturedMap["TYPE"])
        val metadataString = capturedMap["METADATA"].toString()
        val metadata = JsonUtils.toJsonNode(metadataString)
        assertEquals(300, metadata["delay"]["delayMinutes"].intValue())
    }

    private fun firstIngestedRow(): Map<String?, Any?> =
        clientFactory
            .channel(
                clientName = JourneyEventSnowflakeIngestClient.CLIENT_BUILDER_NAME,
                channelName = JourneyEventSnowflakeIngestClient.CHANNEL_BUILDER_NAME,
            ).rows
            .first() ?: throw AssertionError("ingested row is null")
}
