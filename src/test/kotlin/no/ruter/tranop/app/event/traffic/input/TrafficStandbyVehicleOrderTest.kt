package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class TrafficStandbyVehicleOrderTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 vehicles `() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions
            .assertThat(
                storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
        Assertions
            .assertThat(
                storedJourney.vehicles.firstOrNull()?.standbyVehicleId,
            ).isEqualTo(trafficEvent.contingencyVehiclePlanned.standbyVehicleId)
        Assertions
            .assertThat(storedJourney.vehicles.firstOrNull()?.vehicleRef)
            .isEqualTo(trafficEvent.contingencyVehiclePlanned.vehicleRef)
    }

    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 - with no djv2 reference `() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-unknown-traffic-standby-vehicle-order.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isFalse

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
    }

    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 - with no standby vehicle id`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order-without-standby-vehicle-id.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions
            .assertThat(
                storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
        Assertions.assertThat(storedJourney.vehicles.firstOrNull()?.standbyVehicleId).isNull()
        Assertions.assertThat(storedJourney.vehicles.firstOrNull()?.vehicleRef).isNull()
    }

    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 - with no vehicle ref`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order-without-vehicle-ref.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions
            .assertThat(
                storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
        Assertions.assertThat(storedJourney.vehicles.firstOrNull()?.vehicleRef).isEqualTo("STBVID000000000B1")
        Assertions
            .assertThat(storedJourney.vehicles.firstOrNull()?.standbyVehicleId)
            .isEqualTo(trafficEvent.contingencyVehiclePlanned.standbyVehicleId)
    }

    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 - with invalid vehicle ref`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order-with-invalid-vehicle-ref.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions
            .assertThat(
                storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
        Assertions.assertThat(storedJourney.vehicles.firstOrNull()?.vehicleRef).isEqualTo("STBVID000000000B1")
        Assertions
            .assertThat(storedJourney.vehicles.firstOrNull()?.standbyVehicleId)
            .isEqualTo(trafficEvent.contingencyVehiclePlanned.standbyVehicleId)
    }

    @Test
    fun `TrafficEvent - StandbyVehicleOrder - Update dated journey v2 - first without standby vehicle id then with all data`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)

        val trafficEvent =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order-without-standby-vehicle-id.json",
            )
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions
            .assertThat(
                storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
        Assertions.assertThat(storedJourney.vehicles.firstOrNull()?.standbyVehicleId).isNull()

        val trafficEvent2 =
            TestUtils.readTrafficEvent(
                "traffic-event/djj-3321b-traffic-standby-vehicle-order.json",
            )
        trafficEventService.processInput(journey.ref, trafficEvent2)

        val updatedJourney = assertJourneyExists(journey.ref)
        val updatedEvents = updatedJourney.events.groupBy { it.type }
        Assertions.assertThat(updatedEvents).hasSize(2)
        Assertions.assertThat(updatedEvents[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(updatedEvents[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(2)
        Assertions
            .assertThat(
                updatedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned,
            ).isTrue
        Assertions
            .assertThat(updatedJourney.vehicles.firstOrNull()?.standbyVehicleId)
            .isEqualTo(trafficEvent2.contingencyVehiclePlanned.standbyVehicleId)
        Assertions
            .assertThat(updatedJourney.vehicles.firstOrNull()?.vehicleRef)
            .isEqualTo(trafficEvent2.contingencyVehiclePlanned.vehicleRef)
    }
}
