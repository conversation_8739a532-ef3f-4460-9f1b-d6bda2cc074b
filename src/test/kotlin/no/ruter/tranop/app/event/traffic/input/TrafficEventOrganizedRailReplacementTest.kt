package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.app.test.assertSize
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficCaseReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyOrganizedRailReplacementVehicles
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficReferences
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TrafficEventOrganizedRailReplacementTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - OrganizedRailReplacement`() {
        trafficEventConfig.mitigation.enabled = true
        trafficEventConfig.organizedRailReplacementVehicles.enabled = true

        val journey = TestUtils.readDatedJourney("mitigation/service-replacement-line-12/dated-journey-01.json")

        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        assertThat(
            context.valid(),
        ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        assertThat(storedJourney.cancelled).isTrue()
        assertThat(storedJourney.journeyState.journeyMitigationState.cancelled).isTrue()
        val mitigation =
            storedJourney.journeyState.journeyMitigationState.mitigations
                .assertSize(size = 1)
                .first()
        assertThat(mitigation.code).isEqualTo(DTOServiceMitigationCode.REPLACEMENT_SERVICE.value)
        assertThat(storedJourney.events.last().type).isEqualTo(DTOEventType.REPLACED)

        val replacedByRef =
            storedJourney.replacedBy.journeys
                .first()
                .entityDatedJourneyKeyV2Ref
        assertJourneyExists(replacedByRef)
    }

    @Test
    fun `TrafficEvent - Recall OrganizedRailReplacement`() {
        trafficEventConfig.mitigation.enabled = true
        trafficEventConfig.organizedRailReplacementVehicles.enabled = true

        val journey = TestUtils.readDatedJourney("mitigation/service-replacement-line-12/dated-journey-01.json")

        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        assertThat(
            context.valid(),
        ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        var storedJourney = assertJourneyExists(journeyRef)
        assertThat(storedJourney.cancelled).isTrue()
        assertThat(storedJourney.journeyState.journeyMitigationState.cancelled).isTrue()
        val mitigation =
            storedJourney.journeyState.journeyMitigationState.mitigations
                .assertSize(size = 1)
                .first()
        assertThat(mitigation.code).isEqualTo(DTOServiceMitigationCode.REPLACEMENT_SERVICE.value)
        assertThat(storedJourney.events.last().type).isEqualTo(DTOEventType.REPLACED)

        val replacedByRef =
            storedJourney.replacedBy.journeys
                .first()
                .entityDatedJourneyKeyV2Ref
        assertJourneyExists(replacedByRef)

        val recallEvent = createEvent(journeyRef, false)
        val recallContext = trafficEventService.processInput(journeyRef, recallEvent)
        assertThat(
            recallContext.valid(),
        ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        storedJourney = assertJourneyExists(journeyRef)
        assertThat(storedJourney.cancelled).isFalse()
        assertThat(storedJourney.journeyState.journeyMitigationState.cancelled).isFalse()
        assertThat(storedJourney.events.last().type).isEqualTo(DTOEventType.UNREPLACED)
        assertThat(storedJourney.journeyState.journeyMitigationState.mitigations).isEmpty()

        val replacement = assertJourneyExists(replacedByRef)
        assertThat(replacement.cancelled).isTrue()
    }

    private fun createEvent(
        journeyRef: String,
        planned: Boolean?,
    ): DTOTrafficEvent =
        DTOTrafficEvent().apply {
            ref = "te-0000"
            header =
                DTOMessageHeader().apply {
                    messageTimestamp = timeService.now().toUtcIsoString()
                    traceId = "Test - TrafficEvent - OrganizedRailReplacement"
                }
            journeyRefs =
                DTOTrafficEventJourneyReferences().apply {
                    entityDatedJourneyKeyV2Ref = journeyRef
                }

            trafficRefs =
                DTOTrafficReferences().apply {
                    caseRefs =
                        DTOTrafficCaseReferences().apply {
                            entityTrafficCaseKeyV2Ref = "CaseKeyV2Ref"
                        }
                }

            organizedRailReplacementVehicles =
                planned?.let {
                    DTOTrafficEventJourneyOrganizedRailReplacementVehicles().apply { this.planned = it }
                }
        }
}
