package no.ruter.tranop.app.event.journey.input

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.event.journey.input.process.mitigation.StandbyVehicleJourneyEventHandler
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import no.ruter.tranop.app.test.assertStopPoint
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventContingencyVehiclePlanned
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOmission
import no.ruter.tranop.journey.event.dto.model.common.DTOJourneyEventCall
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class JourneyEventInputTest : AbstractKafkaTest() {
    @Test
    fun `two input partial cancellation journey events should be processed and correct calls should be cancelled`() {
        val journey = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey)
        pipe(journey)

        val cancellationCall1 =
            DTOJourneyEventCall()
                .withNsrQuayId(JourneyInputUtils.QUAY_REF_1)
                .withTime("2022-01-01T12:00+02:00")
        val firstJourneyEvent =
            DTOJourneyEvent()
                .withId("first-event-id")
                .withEntityDatedJourneyKeyV2Ref(journey.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true)
                        .withPartial(true)
                        .withCalls(listOf(cancellationCall1)),
                )

        val firstEventResult = journeyEventService.processInput(firstJourneyEvent.id, firstJourneyEvent)

        Assertions.assertFalse(firstEventResult.skip)

        val firstEventStoredJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            firstEventStoredJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.cancelled == false,
        )
        Assertions.assertTrue(
            firstEventStoredJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.partiallyCancelled == true,
        )
        Assertions.assertEquals(
            1,
            firstEventStoredJourney
                ?.data
                ?.plan
                ?.calls
                ?.filter { it.cancelled == true }
                ?.size,
        )

        val cancellationCall2 =
            DTOJourneyEventCall()
                .withNsrQuayId(JourneyInputUtils.QUAY_REF_2)
                .withTime("2022-01-01T14:00+02:00")
        val secondJourneyEvent =
            DTOJourneyEvent()
                .withId("some-event-id-2")
                .withEntityDatedJourneyKeyV2Ref(journey.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true)
                        .withPartial(true)
                        .withCalls(listOf(cancellationCall2)),
                )
        val secondEventResult = journeyEventService.processInput(secondJourneyEvent.id, secondJourneyEvent)

        Assertions.assertFalse(secondEventResult.skip)

        val updatedStoredJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            updatedStoredJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.partiallyCancelled == true,
        )
        Assertions.assertTrue(
            updatedStoredJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.cancelled == false,
        )
        Assertions.assertEquals(
            1,
            updatedStoredJourney
                ?.data
                ?.plan
                ?.calls
                ?.filter { it.cancelled == true }
                ?.size,
        )
    }

    @Test
    fun `input partial cancellation journey event should be processed and applied to journey`() {
        val journey1 = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey1)
        pipe(journey1)

        val cancellationCall =
            DTOJourneyEventCall()
                .withNsrQuayId(JourneyInputUtils.QUAY_REF_1)
                .withTime("2022-01-01T12:00+02:00")
        val journeyEvent =
            DTOJourneyEvent()
                .withId("some-event-id")
                .withEntityDatedJourneyKeyV2Ref(journey1.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true)
                        .withPartial(true)
                        .withCalls(listOf(cancellationCall)),
                )

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)

        Assertions.assertFalse(eventResult.skip)

        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey1.ref)
        Assertions.assertTrue(storedJourney?.data?.cancelled == false)
        Assertions.assertTrue(storedJourney?.partiallyCancelled == true)
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.partiallyCancelled == true,
        )
    }

    @Test
    fun `input cancellation journey event should be processed and applied to journey`() {
        val journey1 = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey1)
        pipe(journey1)

        val journeyEvent =
            DTOJourneyEvent()
                .withId("some-event-id")
                .withEntityDatedJourneyKeyV2Ref(journey1.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true),
                )

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)

        Assertions.assertFalse(eventResult.skip)

        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey1.ref)
        Assertions.assertTrue(storedJourney?.data?.cancelled == true)
    }

    @Test
    fun `tombstone event should be returning noop right now, but we need to think about it`() {
        val journey1 = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey1)
        pipe(journey1)

        val journeyEvent = null

        val eventResult = journeyEventService.processInput("some-event-id", journeyEvent)
        Assertions.assertNotNull(eventResult)
        Assertions.assertInstanceOf(ProcessingContext.NOOPProcessingContext::class.java, eventResult)
    }

    @Test
    fun `applying same event twice shouldn't be possible`() {
        val journey1 = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey1)
        pipe(journey1)

        val journeyEvent =
            DTOJourneyEvent()
                .withId("some-event-id")
                .withHeader(DTOMessageHeader().withTraceId("some-trace-id"))
                .withEntityDatedJourneyKeyV2Ref(journey1.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true),
                )

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)
        Assertions.assertTrue(eventResult.valid())
        val eventResult2 = journeyEventService.processInput(journeyEvent.id, journeyEvent.withId("some-event-id"))
        Assertions.assertTrue(!eventResult2.valid())
    }

    @Test
    fun `omit journey event should result in journey state with a journey deviation state with omitted`() {
        val journey = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey)
        pipe(journey)

        val journeyEvent =
            DTOJourneyEvent().apply {
                id = "some-event-id"
                header = DTOMessageHeader().apply { traceId = "some-trace-id" }
                entityDatedJourneyKeyV2Ref = journey.ref
                omission = DTOJourneyEventOmission().apply { omitted = true }
            }

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)
        Assertions.assertTrue(eventResult.valid())
        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyDeviationState
                ?.omitted == true,
        )
    }

    @Test
    fun `omit on call on journey event on should result in journey state with a journey deviation state with partially omitted`() {
        val journey = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey)
        pipe(journey)

        val lastCall = journey.plan.calls.last()
        val lastCallStopPoint = journey.assertStopPoint(lastCall.stopPointRef)
        val journeyEvent =
            DTOJourneyEvent().apply {
                id = "some-event-id"
                header = DTOMessageHeader().apply { traceId = "some-trace-id" }
                entityDatedJourneyKeyV2Ref = journey.ref
                omission =
                    DTOJourneyEventOmission().apply {
                        omitted = true
                        calls =
                            listOf(
                                DTOJourneyEventCall().apply {
                                    time = lastCall.plannedArrival
                                    nsrQuayId = lastCallStopPoint.quayRef
                                },
                            )
                    }
            }

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)
        Assertions.assertTrue(eventResult.valid())
        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyDeviationState
                ?.omitted == false,
        )
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyDeviationState
                ?.partiallyOmitted == true,
        )
    }

    @Test
    fun `Contingency vehicle planned on journey event on should result in journey state with a contingencyVehiclePlanned true`() {
        val contingencyOperatorRef = "RUT:Operator:SomeRef"
        val journey = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey)
        pipe(journey)

        val journeyEvent =
            DTOJourneyEvent().apply {
                id = "some-event-id"
                header = DTOMessageHeader().apply { traceId = "some-trace-id" }
                entityDatedJourneyKeyV2Ref = journey.ref
                contingencyVehiclePlanned =
                    DTOJourneyEventContingencyVehiclePlanned().apply {
                        planned = true
                        operatorRefs = listOf(contingencyOperatorRef)
                    }
            }

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)
        Assertions.assertTrue(eventResult.valid())
        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.contingencyVehiclePlanned == true,
        )
        Assertions.assertTrue(
            storedJourney?.data?.operators?.contains(
                DTOOperator()
                    .withOperatorRef(contingencyOperatorRef)
                    .withName(StandbyVehicleJourneyEventHandler.CONTINGENCY_OPERATOR_NAME),
            ) == true,
        )
    }

    @Test
    fun `ContingencyVehiclePlanned then PublishedTrafficSituation event should result in state with contingencyVehiclePlanned false`() {
        val journey = JourneyInputUtils.createDatedJourney().withOperatingDate(OffsetDateTime.now().toLocalDate().toString())
        adjustTime(journey)
        pipe(journey)

        val journeyEventContingencyTrue =
            DTOJourneyEvent().apply {
                id = "some-event-id"
                header = DTOMessageHeader().apply { traceId = "some-trace-id" }
                entityDatedJourneyKeyV2Ref = journey.ref
                contingencyVehiclePlanned =
                    DTOJourneyEventContingencyVehiclePlanned().apply {
                        planned = true
                    }
            }

        journeyEventService.processInput(journeyEventContingencyTrue.id, journeyEventContingencyTrue)
        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            storedJourney
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.contingencyVehiclePlanned == true,
        )

        val journeyEventContingencyFalse =
            DTOJourneyEvent().apply {
                id = "some-event-id-2"
                header = DTOMessageHeader().apply { traceId = "some-trace-id-2" }
                entityDatedJourneyKeyV2Ref = journey.ref
                contingencyVehiclePlanned =
                    DTOJourneyEventContingencyVehiclePlanned().apply {
                        planned = false
                    }
            }

        journeyEventService.processInput(journeyEventContingencyFalse.id, journeyEventContingencyFalse)

        val storedJourneyAfterTwoEvents = datedJourneyService.fetchDatedJourneyByRef(journey.ref)
        Assertions.assertTrue(
            storedJourneyAfterTwoEvents
                ?.data
                ?.journeyState
                ?.journeyMitigationState
                ?.contingencyVehiclePlanned == false,
        )
    }
}
