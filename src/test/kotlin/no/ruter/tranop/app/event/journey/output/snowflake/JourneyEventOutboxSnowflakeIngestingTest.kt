package no.ruter.tranop.app.event.journey.output.snowflake

import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeTest
import no.ruter.tranop.bi.model.BIDatedJourneyV2JourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import org.springframework.test.context.TestPropertySource

@TestPropertySource(
    properties = [
        "${SnowflakeClientProperties.KEY_PREFIX}.enabled=true",
        "app.config.outbox.snowflake-streaming-journey-event.enabled=true",
    ],
)

@Suppress("UNCHECKED_CAST")
class JourneyEventOutboxSnowflakeIngestingTest : AbstractSnowflakeTest() {
    @Test
    fun `ingest and read`() {
        val journey1 = vehicleTask1datedJourney2
        adjustTime(journey1)
        pipe(journey1)

        val journeyEvent =
            DTOJourneyEvent()
                .withId("some-event-id")
                .withEntityDatedJourneyKeyV2Ref(journey1.ref)
                .withCancellation(
                    DTOJourneyEventCancellation()
                        .withCancelled(true),
                )

        val eventResult = journeyEventService.processInput(journeyEvent.id, journeyEvent)

        Assertions.assertFalse(eventResult.skip)

        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journey1.ref)
        Assertions.assertTrue(storedJourney?.data?.cancelled == true)

        journeyEventOutboxSnowflakeIngestingRoutine.execute(timeService.now())

        val channel = journeyEventSnowflakeChannel

        Assertions.assertNotNull(channel)
        val rows = channel.rows
        Assertions.assertNotNull(channel.rows)
        Assertions.assertTrue(rows.size == 1)
        val row =
            rows.first()?.filterKeys { it != null }?.let {
                BIDatedJourneyV2JourneyEvent.fromMap(it as Map<String, Any?>)
            }!!

        Assertions.assertEquals("CANCELLATION", row.type)
        Assertions.assertEquals(journey1.ref, row.datedJourneyV2Ref)
    }
}
