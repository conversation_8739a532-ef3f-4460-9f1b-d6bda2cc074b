package no.ruter.tranop.app.plan.journey

import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJEvent
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.plandata.journey.dated.v2.dto.value.PDJEventType
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.opensearch.AbstractOpenSearchTest
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventAssignedReason
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventUnassignedReason
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventAssigned
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventServiceWindow
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventUnassigned
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import org.springframework.test.context.TestPropertySource

import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

@TestPropertySource(
    properties = [
        "${SnowflakeClientProperties.KEY_PREFIX}.enabled=true",
        "app.config.outbox.snowflake-streaming-journey-event.enabled=false",
    ],
)

class DatedJourneyOutboxLifeCycleTest : AbstractOpenSearchTest() {
    val recordType = RecordType.DATED_JOURNEY
    val outputs = recordType.outputs
    val dataType: DBOutboxDataType? = outputs?.type
    val targetTypes = outputs?.targetTypes ?: emptyList()
    val entityProducer get() = kafka.datedJourneyProducer
    val internalProducer get() = kafka.assignmentJourneyProducer

    val outboxRoutines
        get() =
            listOf(
                datedJourneyOutboxEntityV2PublishingRoutine,
                datedJourneyOutboxInternalPublishingRoutine,
                datedJourneyOutboxOpenSearchPublishingRoutine,
                journeyEventOutboxSnowflakeIngestingRoutine,
                serviceDeviationSnowflakeIngestingRoutine,
            )

    /**
     * LifeCycle to be tested:
     *   1. INSERT
     *   2. UPDATE
     *   3. SIGN_ON
     *   4. SIGN_OFF
     *   5. DELETE
     *   6. TOMBSTONE
     **/
    @Test
    fun `DatedJourney Outbox Life Cycle`() {
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        insert(journeyRef, journey)
        assert("insert", assertSnowflakeJourneyEvents = false)

        update(journeyRef, journey)
        assert("update", assertSnowflakeJourneyEvents = false)

        assign(journeyRef, journey)
        assert("assign")

        unassign(journeyRef, journey)
        assert("unassign")

        delete(journeyRef, journey)
        // TODO: Can not assert on kafka here.
        //  The entity is pushed sync when the input has delete=true.
        //  Enable this test when delete processing is removed
        assert("delete", assertKafka = false, assertSnowflakeJourneyEvents = false)

        tombstone(journeyRef, journey)
        assert("tombstone", assertSnowflakeJourneyEvents = false)
    }

    private fun assert(
        message: String,
        assertKafka: Boolean = true,
        assertOutbox: Boolean = true,
        assertSnowflakeJourneyEvents: Boolean = true,
    ) {
        kafka.cleanOutputTopics()

        if (assertOutbox) assertOutbox(message = message)

        if (assertKafka) assertKafka(records = 1, msg = message)
        if (assertSnowflakeJourneyEvents) assertSnowflakeJourneyEvents(records = 1, msg = message)

        if (assertOutbox) assertOutbox(outboxItems = 0, message = message)

        kafka.cleanOutputTopics()
    }

    private fun assertSnowflakeJourneyEvents(
        records: Int,
        msg: String,
    ) {
        val rows = journeyEventSnowflakeChannel.rows
        val message = "$msg snowflake: ${rows.map { it?.get("TYPE") }}"
        Assertions.assertEquals(records, rows.size, message)
        journeyEventSnowflakeChannel.rows.clear()
    }

    private fun assertKafka(
        records: Int,
        msg: String,
    ) {
        val entities = entityProducer.getRecords()
        val internal = internalProducer.getRecords()
        val messageEntities = "Entity $msg: ${
            entities.map {
                it
                    .value()
                    ?.entityData
                    ?.events
                    ?.map { it.type }
            }
        }"
        val messageInternal = "Internal $msg: ${
            internal.map {
                it
                    .value()
                    ?.events
                    ?.map { it.type }
            }
        }"
        Assertions.assertEquals(records, internal.size, messageInternal)
        Assertions.assertEquals(records, entities.size, messageEntities)
        kafka.cleanOutputTopics()
        Assertions.assertEquals(0, entityProducer.getRecords().size, msg)
        Assertions.assertEquals(0, internalProducer.getRecords().size, msg)
    }

    private fun tombstone(
        journeyRef: String,
        journey: PDJDatedJourney,
    ) {
        datedJourneyService.markTombstoned(journeyRef)
    }

    private fun delete(
        journeyRef: String,
        journey: PDJDatedJourney,
    ) {
        journey.apply {
            this.deleted = true
        }
        insert(journeyRef, journey)
    }

    private fun assign(
        journeyRef: String?,
        journey: PDJDatedJourney,
    ) {
        journeyEventService.processInput(value = assignedEvent(journeyRef, journey))
    }

    private fun unassign(
        journeyRef: String?,
        journey: PDJDatedJourney,
    ) {
        journeyEventService.processInput(value = unassignedEvent(journeyRef))
    }

    private fun update(
        journeyRef: String?,
        journey: PDJDatedJourney,
    ) {
        insert(
            journeyRef,
            journey.apply {
                this.events.apply {
                    add(updateEvent())
                }
                this.plan.apply {
                    this.calls = this.calls.subList(0, 4)
                    this.links = this.links.subList(0, 3)
                }
            },
        )
    }

    private fun insert(
        journeyRef: String?,
        journey: PDJDatedJourney,
    ) {
        val context = datedJourneyInputService.process(journeyRef, journey)
        Assertions.assertTrue(context.valid(), "${context.statusDetails}")
        val storedJourney = datedJourneyService.fetchDatedJourneyByRef(journeyRef)
        Assertions.assertNotNull(storedJourney)
    }

    private fun assertOutbox(
        outboxItems: Int = 1,
        message: String,
    ) {
        targetTypes.forEach { targetType ->
            val desc = "$message $dataType - $targetType".trim()
            val res =
                outboxService.findUnpublished(
                    dataType = dataType!!,
                    targetType = targetType,
                )
            Assertions.assertNotNull(res, desc)
            Assertions.assertEquals(outboxItems, res.size, desc)
        }

        outboxRoutines.forEach { r ->
            r.execute(timeService.now())
        }

        targetTypes.forEach { targetType ->
            val desc = "$dataType - $targetType $message".trim()
            val res =
                outboxService.findUnpublished(
                    dataType = dataType!!,
                    targetType = targetType,
                )
            Assertions.assertNotNull(res, desc)
            Assertions.assertTrue(res.isEmpty(), desc)
        }
    }

    private fun updateEvent(): PDJEvent =
        PDJEvent().apply {
            this.id = "update-id-001"
            this.type = PDJEventType.UPDATED
            this.traceId = MapperUtils.randomId("test")
            this.timestamp = timeService.now().toUtcIsoString()
            this.description = "some desc"
            this.source = source ?: "plandata/testing"
        }

    private fun assignedEvent(
        journeyRef: String?,
        journey: PDJDatedJourney,
    ): DTOJourneyEvent =
        DTOJourneyEvent()
            .apply {
                this.id = "assigned-id-001"
                this.header = messageHeader()
                this.entityDatedJourneyKeyV2Ref = journeyRef
                this.assigned =
                    DTOJourneyEventAssigned().apply {
                        this.reason = DTOJourneyEventAssignedReason.PLANNED
                        this.assignmentRef = "ass-$journeyRef"
                        this.vehicleRef = vehicleRef
                        this.serviceWindow =
                            DTOJourneyEventServiceWindow().apply {
                                val calls = journey.plan.calls
                                this.firstDepartureDateTime = calls.firstOrNull()?.plannedDeparture
                                this.lastArrivalDateTime = calls.lastOrNull()?.plannedArrival
                            }
                    }
            }

    private fun unassignedEvent(journeyRef: String?): DTOJourneyEvent =
        DTOJourneyEvent()
            .apply {
                this.id = "unassigned-id-001"
                this.header = messageHeader()
                this.entityDatedJourneyKeyV2Ref = journeyRef
                this.unassigned =
                    DTOJourneyEventUnassigned().apply {
                        this.reason = DTOJourneyEventUnassignedReason.FINISHED
                        this.assignmentRef = "ass-$journeyRef"
                        this.vehicleRef = vehicleRef
                    }
            }

    private fun messageHeader(): DTOMessageHeader =
        DTOMessageHeader().apply {
            this.messageTimestamp = timeService.now().toUtcIsoString()
            this.receivedTimestamp = timeService.now().toUtcIsoString()
            this.traceId = "Test - TrafficEvent - Full Cancellation"
        }
}
