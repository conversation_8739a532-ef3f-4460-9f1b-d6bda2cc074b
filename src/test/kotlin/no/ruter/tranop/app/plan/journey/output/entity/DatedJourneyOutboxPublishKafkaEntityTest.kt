package no.ruter.tranop.app.plan.journey.output.entity

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

// TODO: Simulate exception in routine to check error handling
class DatedJourneyOutboxPublishKafkaEntityTest : AbstractKafkaTest() {
    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
    }

    @Test
    fun `Outbox Publish - Dated Journey Entity V2`() {
        val journey = vehicleTask1datedJourney1

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.DATED_JOURNEY,
                targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(journey),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        datedJourneyOutboxEntityV2PublishingRoutine.execute(timeService.now())

        val producedRecords = kafka.datedJourneyProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)
    }
}
