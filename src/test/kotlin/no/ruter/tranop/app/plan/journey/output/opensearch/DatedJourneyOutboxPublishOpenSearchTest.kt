package no.ruter.tranop.app.plan.journey.output.opensearch

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.opensearch.AbstractOpenSearchTest
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

// TODO: Simulate exception in routine to check error handling
class DatedJourneyOutboxPublishOpenSearchTest : AbstractOpenSearchTest() {
    @Test
    fun `Outbox Publish - Dated Journey Open Search`() {
        val journey = vehicleTask1datedJourney1

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.DATED_JOURNEY,
                targetType = DBOutboxTargetType.OPENSEARCH_JSON_INTERNAL,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(journey),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        datedJourneyOutboxOpenSearchPublishingRoutine.execute(timeService.now())

        awaitUntilAsserted("OpenSearchIndex: $openSearchIndexName") {
            val records = openSearchService.getSome<DTODatedJourney>(openSearchIndexName)
            Assertions.assertEquals(1, records.size)
            val record = records.first()
            Assertions.assertEquals(journey.ref, record.ref)
        }
    }
}
