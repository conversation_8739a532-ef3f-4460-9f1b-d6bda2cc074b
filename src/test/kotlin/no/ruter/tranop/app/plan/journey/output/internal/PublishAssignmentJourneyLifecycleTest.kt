package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.common.dataflow.opensearch.AbstractOpenSearchTest
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.minutes

class PublishAssignmentJourneyLifecycleTest : AbstractOpenSearchTest() {
    private val inputMapper = DatedJourneyInputMapper()
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
        journeyValidator = DatedJourneyInputValidator(timeService)
        openSearchService.createIndexIfNotExists(openSearchProperties.journeyIndexName)
    }

    @Test
    fun `test publishing av unpublished assignment journeys to Kafka`() {
        val ref = "some-ref"
        val journey1 =
            JourneyInputUtils.createDatedJourney(
                ref = ref,
            )
        timeService.offset(journey1)

        val internalJourney1 = inputMapper.mapDatedJourney(journey1)
        val context1 = journeyValidator.validate(journey1.ref, internalJourney1)
        journeyRepo.store(context1)

        timeService.offset(1.minutes)

        publishDatedJourneyRoutine.execute(timeService.now())

        val producedAssignmentJourneys = kafka.assignmentJourneyProducer.getRecords()

        Assertions.assertThat(producedAssignmentJourneys).hasSize(1)
        Assertions.assertThat(producedAssignmentJourneys.first().value().ref).isEqualTo(ref)
    }
}
