package no.ruter.tranop.app.plan.stop.db

import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class StopPointRepositoryTest : AbstractBaseTest() {
    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `store and retrieve stop point`() {
        val stopPoint = DTOStopPoint().withRef("some-stop-point-ref")
        stopPointRepository.store(StopPointInputContext(stopPoint = stopPoint))

        val storedStopPoint = stopPointRepository.fetchByRef(stopPoint.ref)
        Assertions.assertNotNull(storedStopPoint)
        val diff =
            JsonDiff.of(
                stopPoint,
                storedStopPoint?.data?.deepCopy().apply {
                    this?.header = null
                },
            )
        Assertions.assertFalse(diff.diff, diff.toString())

        val record = storedStopPoint?.record
        Assertions.assertNotNull(record)
        Assertions.assertNotNull(record?.createdAt)
        Assertions.assertNotNull(record?.createdBy)
        Assertions.assertNotNull(record?.jsonHash)
    }

    @Test
    fun `should increase revision number when updated`() {
        val stopPoint = DTOStopPoint().withRef("some-stop-point-ref")
        stopPointRepository.store(StopPointInputContext(stopPoint = stopPoint))
        val storedStopPoint = stopPointRepository.fetchByRef(stopPoint.ref)
        Assertions.assertNotNull(storedStopPoint)
        Assertions.assertEquals(1, storedStopPoint?.record?.revision)

        val updatedStopPoint = stopPoint.withDescription("add-some-description")
        stopPointRepository.store(StopPointInputContext(stopPoint = updatedStopPoint))
        val storedUpdatedStopPoint = stopPointRepository.fetchByRef(stopPoint.ref)
        Assertions.assertNotNull(storedUpdatedStopPoint)
        Assertions.assertEquals(2, storedUpdatedStopPoint?.record?.revision)
    }
}
