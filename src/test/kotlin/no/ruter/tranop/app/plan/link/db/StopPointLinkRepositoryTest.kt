package no.ruter.tranop.app.plan.link.db

import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.tranop.app.plan.link.input.StopPointLinkInputContext
import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLinkType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class StopPointLinkRepositoryTest : AbstractBaseTest() {
    @BeforeEach
    fun setUp() {
        testEnvironment.reset()
        timeService.reset()
    }

    @Test
    fun `store and retrieve stop point link`() {
        val stopPointLink = DTOStopPointLink().withRef("some-stop-point-link-ref")
        stopPointLinkRepository.store(StopPointLinkInputContext(link = stopPointLink))

        val storedStopPointLink = stopPointLinkRepository.fetchByRef(stopPointLink.ref)
        Assertions.assertNotNull(storedStopPointLink)

        val diff = JsonDiff.of(stopPointLink, storedStopPointLink?.data)
        Assertions.assertFalse(diff.diff, diff.toString())
        val record = storedStopPointLink?.record
        Assertions.assertNotNull(record)
        Assertions.assertNotNull(record?.createdAt)
        Assertions.assertNotNull(record?.createdBy)
        Assertions.assertNotNull(record?.jsonHash)
    }

    @Test
    fun `should increase revision number when updated`() {
        val stopPointLink = DTOStopPointLink().withRef("some-stop-point-ref")
        stopPointLinkRepository.store(StopPointLinkInputContext(link = stopPointLink))
        val storedStopPointLink = stopPointLinkRepository.fetchByRef(stopPointLink.ref)
        Assertions.assertNotNull(storedStopPointLink)
        Assertions.assertEquals(1, storedStopPointLink?.record?.revision)

        val updatedStopPointLink = stopPointLink.withStopPointLinkType(DTOStopPointLinkType.WATER)
        stopPointLinkRepository.store(StopPointLinkInputContext(link = updatedStopPointLink))
        val storedUpdatedStopPointLink = stopPointLinkRepository.fetchByRef(stopPointLink.ref)
        Assertions.assertNotNull(storedUpdatedStopPointLink)
        Assertions.assertEquals(2, storedUpdatedStopPointLink?.record?.revision)
    }
}
