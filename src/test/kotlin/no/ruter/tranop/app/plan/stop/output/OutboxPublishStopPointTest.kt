package no.ruter.tranop.app.plan.stop.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

// TODO: Simulate exception in routine to check error handling
class OutboxPublishStopPointTest : AbstractKafkaTest() {
    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
    }

    @Test
    fun `Outbox Publish - Stop Point Entity V2`() {
        val ref = "some-stop-point-ref"
        val stopPoint =
            DTOStopPoint()
                .withRef(
                    ref,
                ).withStopPlaceDescription("som description")

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.STOP_POINT,
                targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(stopPoint),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        stopPointOutboxEntityV2PublishingRoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)
    }

    @Test
    fun `Outbox Publish - Stop Point DTO`() {
        val ref = "some-stop-point-ref"
        val stopPoint =
            DTOStopPoint()
                .withRef(
                    ref,
                ).withStopPlaceDescription("som description")

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.STOP_POINT,
                targetType = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(stopPoint),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        stopPointOutboxInternalPublishingRoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)
    }
}
