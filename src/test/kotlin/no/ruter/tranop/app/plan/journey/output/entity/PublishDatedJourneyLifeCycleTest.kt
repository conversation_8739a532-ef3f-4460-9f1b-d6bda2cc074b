package no.ruter.tranop.app.plan.journey.output.entity

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonSetter
import com.fasterxml.jackson.annotation.Nulls
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.rdp.common.json.node.removePaths
import no.ruter.tranop.app.common.AvroMixIn
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.common.dataflow.opensearch.AbstractOpenSearchTest
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.time.Duration.Companion.minutes

class PublishDatedJourneyLifeCycleTest : AbstractOpenSearchTest() {
    private val inputMapper = DatedJourneyInputMapper()
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
        journeyValidator = DatedJourneyInputValidator(timeService)
        refreshOpenSearchIndices()
        openSearchService.createIndexIfNotExists(openSearchProperties.journeyIndexName)
    }

    companion object {
        private val IGNORED_PATHS =
            listOf(
                "\$.entityHeader.publishedTimestamp",
                "\$.entityHeader.publisherId",
                "\$.entityHeader.receivedTimestamp",
                "\$.entityData.journey.stopPoints[*].externalStopAreaId",
                // TODO: Resolve these (we should control their value, which may not be same as they were in input).
                "\$.entityData.journey.journeyState",
                "\$.entityData.journey.callSequence[*].omitted",
                "\$.entityData.journey.callSequence[*].cancelled",
            )
        private val MAPPER =
            JsonUtils
                .createDefaultObjectMapper()
                .setDefaultSetterInfo(JsonSetter.Value.forValueNulls(Nulls.AS_EMPTY))
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .registerModule(JavaTimeModule())
                .addMixIn(Object::class.java, AvroMixIn::class.java)
    }

    @Test
    fun `test publishing av unpublished journeys to Kafka`() {
        val ref = "some-ref"
        val journey1 =
            JourneyInputUtils.createDatedJourney(
                ref = ref,
            )
        timeService.offset(journey1)
        val context1 = journeyValidator.validate(journey1.ref, journey1)
        journeyRepo.store(context1)

        timeService.offset(1.minutes)

        publishDatedJourneyRoutine.execute(timeService.now())

        val result = assertJourneyRecordExists(ref)
        Assertions.assertThat(result.record.publishedRevision == result.record.revision)
        Assertions.assertThat(result.record.publishedAt != null)
    }

    @Test
    fun `test publishing av unpublished journeys to OpenSearch`() {
        val ref = "some-ref"
        val now = LocalDate.now()
        val indexName = openSearchProperties.journeyIndexName
        // we publish operatingDate - 1 till operatingDate + 2
        // so first and last ones shouldn't be published
        val journey1 = JourneyInputUtils.createDatedJourney("$ref-1", now.minusDays(2).toString())
        val journey2 = JourneyInputUtils.createDatedJourney("$ref-2", now.minusDays(1).toString())
        val journey3 = JourneyInputUtils.createDatedJourney("$ref-3", now.toString())
        val journey4 = JourneyInputUtils.createDatedJourney("$ref-4", now.plusDays(1).toString())
        val journey5 = JourneyInputUtils.createDatedJourney("$ref-5", now.plusDays(2).toString())
        val journey6 = JourneyInputUtils.createDatedJourney("$ref-6", now.plusDays(3).toString())

        timeService.offset(journey3)

        val context1 = journeyValidator.validate(journey1.ref, journey1, recordMetadata = true)
        journeyRepo.store(context1)
        insightService.insight(context1)

        val context2 = journeyValidator.validate(journey2.ref, journey2, recordMetadata = true)
        journeyRepo.store(context2)
        insightService.insight(context2)

        val context3 = journeyValidator.validate(journey3.ref, journey3, recordMetadata = true)
        journeyRepo.store(context3)
        insightService.insight(context3)

        val context4 = journeyValidator.validate(journey4.ref, journey4, recordMetadata = true)
        journeyRepo.store(context4)
        insightService.insight(context4)

        val context5 = journeyValidator.validate(journey5.ref, journey5, recordMetadata = true)
        journeyRepo.store(context5)
        insightService.insight(context5)

        val context6 = journeyValidator.validate(journey6.ref, journey6, recordMetadata = true)
        journeyRepo.store(context6)
        insightService.insight(context6)

        timeService.offset(1.minutes, true)

        val published = publishDatedJourneyToOpenSearchRoutine.execute(timeService.now())

        Assertions.assertThat(published).isEqualTo(4)

        refreshOpenSearchIndices()

        val osDoc1 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-1")
        Assertions.assertThat(osDoc1).isNull()

        val osDoc2 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-2")
        Assertions.assertThat(osDoc2).isNotNull()

        val osDoc3 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-3")
        Assertions.assertThat(osDoc3).isNotNull()

        val osDoc4 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-4")
        Assertions.assertThat(osDoc4).isNotNull()

        val osDoc5 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-5")
        Assertions.assertThat(osDoc5).isNotNull()

        val osDoc6 = openSearchService.fetchDocumentById<DTODatedJourney>(indexName, "$ref-6")
        Assertions.assertThat(osDoc6).isNull()
    }

    @Test
    fun `test dated journey DTO produces correct Entity as is now in Journey Man`() {
        val inputDatedJourney = TestUtils.readDatedJourney("dated-journey/dated-journey-input-dto.json")
        val expectedJourneyStr = TestUtils.readFile("dated-journey/dated-journey-output-entity.json")

        val internalJourney = inputMapper.mapDatedJourney(inputDatedJourney)
        val mappedJourneyMsg = publishDatedJourneyRoutine.createOutputMessage(internalJourney)

        assert(mappedJourneyMsg.ok)
        assert(mappedJourneyMsg.value != null)

        val expectedJourney = JsonUtils.toJsonNode(MAPPER, expectedJourneyStr)
        val mappedJourney = JsonUtils.toJsonNode(MAPPER, mappedJourneyMsg.value!!)

        applyIgnore(expectedJourney)
        applyIgnore(mappedJourney)

        val diff = JsonDiff.of(expectedJourney, mappedJourney)
        assert(!diff.diff) { JsonUtils.toJson(diff.details, true) }
    }

    private fun applyIgnore(node: JsonNode) {
        node.removePaths(IGNORED_PATHS)
    }
}
