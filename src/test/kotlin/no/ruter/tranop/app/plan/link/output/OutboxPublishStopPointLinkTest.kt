package no.ruter.tranop.app.plan.link.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

// TODO: Simulate exception in routine to check error handling
class OutboxPublishStopPointLinkTest : AbstractKafkaTest() {
    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
    }

    @Test
    fun `Outbox Publish - Stop Point Link Entity V2`() {
        val ref = "some-stop-point-link-ref"
        val stopPointLink =
            DTOStopPointLink()
                .withRef(ref)
                .withFromQuayRef("QUAY1")
                .withToQuayRef("QUAY2")
                .withFromStopPointRef("STOP:POINT1")
                .withToStopPointRef("STOP:POINT2")

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.STOP_POINT_LINK,
                targetType = DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(stopPointLink),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        stopPointLinkOutboxEntityV2PublishingRoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointLinkProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)
    }

    @Test
    fun `Outbox Publish - Stop Point Link DTO`() {
        val ref = "some-stop-point-link-ref"
        val stopPointLink =
            DTOStopPointLink()
                .withRef(
                    ref,
                ).withFromQuayRef("QUAY1")
                .withToQuayRef("QUAY2")

        outboxRepository.store(
            Outbox(
                dataType = DBOutboxDataType.STOP_POINT_LINK,
                targetType = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                payloadRef = "pl-123",
                payload = JsonUtils.toJson(stopPointLink),
                ref = "ob-123",
            ),
        )

        timeService.offset(30.seconds)

        stopPointLinkOutboxInternalPublishingRoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointLinkDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)
    }
}
