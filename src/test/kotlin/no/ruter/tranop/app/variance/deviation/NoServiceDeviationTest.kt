package no.ruter.tranop.app.variance.deviation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull

class NoServiceDeviationTest : AbstractADTv4DeviationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]
    val vehicleTaskJourney04: PDJDatedJourney = vehicleTask[3]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `deviation - no service - single journey - create & delete`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        // Create no service deviation via API
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId)
        val request = createNoServiceRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(serviceDeviationId)
        Assertions.assertEquals(APIServiceDeviationCode.NO_SERVICE.value, returned.deviation?.spec?.code)

        // Assert no service deviation has been applied to stored journey.
        assertNoServiceJourney(journeyId)

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(serviceDeviationId, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert no service deviation has been recalled on stored journey.
        assertNoServiceRecalledJourney(journeyId)
    }

    @Test
    fun `deviation - no service - single journey - create & update - change journey`() {
        // Store our test journey in database.
        val journeyId1 = vehicleTaskJourney03.ref
        val journeyId2 = vehicleTaskJourney04.ref
        storeDatedJourneys(vehicleTaskJourney03, vehicleTaskJourney04)

        // Create no service deviation via API
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId1)
        val request = createNoServiceRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert no service deviation has been applied to stored journey.
        assertNoServiceJourney(journeyId1)

        // Update deviation by changing impact from first to second journey.
        val updatedImpact = ImpactUtils.journeyImpact(journeyId = journeyId2)
        val updatedRequest = createNoServiceRequest(impact = updatedImpact, action = APIPostRequestType.UPDATE)
        postServiceDeviationUpdateRequest(serviceDeviationId, updatedRequest)

        // Assert no service deviation has been recalled on first journey and applied on second journey.
        assertNoServiceJourney(journeyId2)
        assertNoServiceRecalledJourney(journeyId1)
    }

    @Test
    fun `deviation - no service - single journey - single call - create & delete`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to "NSR:Quay:11095"))

        // Create no service deviation via API
        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId, stopPointCalls = calls)
        val request = createNoServiceRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(serviceDeviationId)
        Assertions.assertEquals(APIServiceDeviationCode.NO_SERVICE.value, returned.deviation?.spec?.code)

        // Assert deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALLS_OMITTED)
            EventUtils.assertEvents(journey.data, events = types)
            assertNoServiceStatus(journey, omitted = false, partiallyOmitted = true)
        }

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(serviceDeviationId, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            val record = journey.record
            Assertions.assertEquals(3, record.revision)

            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALLS_OMITTED, DTOEventType.CALLS_UN_OMITTED)
            EventUtils.assertEvents(journey.data, events = types)
            assertNoServiceStatus(journey, omitted = false, partiallyOmitted = false)
        }
    }

    private fun createNoServiceRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceDeviationPostRequest = createServiceDeviationRequest(APIServiceDeviationCode.NO_SERVICE, impact, action)

    private fun assertNoServiceStatus(
        record: InternalDatedJourney,
        omitted: Boolean,
        partiallyOmitted: Boolean,
    ) {
        val journey = record.data
        val journeyState = assertNotNull(journey.journeyState)
        val journeyDeviationState = assertNotNull(journeyState.journeyDeviationState)

        Assertions.assertEquals(omitted, journey.omitted)
        Assertions.assertEquals(omitted, journeyDeviationState.omitted)
        Assertions.assertEquals(partiallyOmitted, journeyDeviationState.partiallyOmitted)
    }

    private fun assertNoServiceJourney(journeyId1: String?) {
        assertNotNull(journeyRepo.fetchByRef(journeyId1)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.OMITTED)
            EventUtils.assertEvents(journey.data, events = types)
            assertNoServiceStatus(journey, omitted = true, partiallyOmitted = false)
        }
    }

    private fun assertNoServiceRecalledJourney(journeyId: String?) {
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            val record = journey.record
            Assertions.assertEquals(3, record.revision)

            val types = listOf(DTOEventType.IMPORTED, DTOEventType.OMITTED, DTOEventType.UN_OMITTED)
            EventUtils.assertEvents(journey.data, events = types)
            assertNoServiceStatus(journey, omitted = false, partiallyOmitted = false)
        }
    }
}
