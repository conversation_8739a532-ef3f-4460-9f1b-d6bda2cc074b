package no.ruter.tranop.app.variance.deviation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.app.variance.deviation.test.DeviationTestUtils
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull

class DelayDeviationImpactUpdateTest : AbstractADTv4DeviationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney01: PDJDatedJourney = vehicleTask[0]
    val vehicleTaskJourney02: PDJDatedJourney = vehicleTask[1]
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `deviation - delay - journey delay - multiple journeys to single journey - remove changes from unaffected journeys`() {
        // Store our test journey in database.
        val journeys =
            listOf(
                vehicleTaskJourney01,
                vehicleTaskJourney02,
                vehicleTaskJourney03,
            )
        storeDatedJourneys(journeys)

        // Register delay impacting all three journeys.
        val delay = 3
        val options =
            listOf(
                ImpactUtils.journeyOption(journeyId = vehicleTaskJourney01.ref),
                ImpactUtils.journeyOption(journeyId = vehicleTaskJourney02.ref),
                ImpactUtils.journeyOption(journeyId = vehicleTaskJourney03.ref),
            )
        val impact = APIServiceImpact(journeys = options)
        val request = createDelayRequest(delay = delay, impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(serviceDeviationId)
        Assertions.assertEquals(APIServiceDeviationCode.DELAY.value, returned.deviation?.spec?.code)

        // Assert resolved journey targets stored on deviation reflects impact provided in request (three journeys targeted)
        val storedDeviation = deviationRepo.fetchByRef(assertNotNull(returned.deviation?.lifecycle?.serviceDeviationId))
        assertNotNull(storedDeviation) { dev ->
            val resolvedJourneyTargets = dev.resolvedJourneyTargets
            Assertions.assertEquals(journeys.size, resolvedJourneyTargets.refs.size)
            journeys.forEach { journey ->
                Assertions.assertTrue(resolvedJourneyTargets.refs.contains(journey.ref))
            }
        }

        // Assert impacted journeys have been associated with deviation.
        deviationRepo.externalRefRepo.getExternalRefs(serviceDeviationId).let { externalRefs ->
            val deviationDatedJourneyRefs = externalRefs.get(ExternalRefType.DATED_JOURNEY_V2_REF)
            Assertions.assertEquals(journeys.size, deviationDatedJourneyRefs.size)
            journeys.forEach { j -> Assertions.assertTrue(deviationDatedJourneyRefs.contains(j.ref)) }
        }

        // Assert deviation delay has been applied to stored journeys.
        journeys.forEach { inputJourney ->
            assertNotNull(journeyRepo.fetchByRef(inputJourney.ref)) { journey ->
                Assertions.assertEquals(2, journey.record.revision)
                val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED)
                val events = EventUtils.assertEvents(journey.data, events = types)
                DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[1], expectedDelayMinutes = delay)
            }
        }

        // Update delay deviation to only include the first journey.
        val updatedDelay = 7
        val updatedOptions =
            listOf(
                ImpactUtils.journeyOption(journeyId = vehicleTaskJourney01.ref),
            )
        val updatedImpact = APIServiceImpact(journeys = updatedOptions)
        val updatedDeviation = updateDelayDeviation(serviceDeviationId, delay = updatedDelay, impact = updatedImpact)
        Assertions.assertNotNull(updatedDeviation.record.journeyTargets)

        // Assert resolved journey targets stored on deviation has been updated to reflect changes (one journey only)
        val updatedResolvedJourneyTargets = updatedDeviation.resolvedJourneyTargets
        Assertions.assertEquals(1, updatedResolvedJourneyTargets.refs.size)
        Assertions.assertTrue(updatedResolvedJourneyTargets.refs.contains(vehicleTaskJourney01.ref))

        // Assert impacted journeys have been associated with deviation.
        deviationRepo.externalRefRepo.getExternalRefs(serviceDeviationId).let { externalRefs ->
            val deviationDatedJourneyRefs = externalRefs.get(ExternalRefType.DATED_JOURNEY_V2_REF)
            Assertions.assertEquals(1, deviationDatedJourneyRefs.size)
            Assertions.assertTrue(deviationDatedJourneyRefs.contains(vehicleTaskJourney01.ref))
        }

        // Assert deviation delay has been updated on included target journey.
        assertNotNull(journeyRepo.fetchByRef(vehicleTaskJourney01.ref)) { journey ->
            Assertions.assertEquals(3, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED, DTOEventType.DELAYED)
            val events = EventUtils.assertEvents(journey.data, events = types)
            DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[2], expectedDelayMinutes = updatedDelay)
        }

        // Assert deviation delay has been recalled on journeys no longer included in deviation.
        val excludedJourneys =
            listOf(
                vehicleTaskJourney02,
                vehicleTaskJourney03,
            )
        excludedJourneys.forEach { ex ->
            assertNotNull(journeyRepo.fetchByRef(ex.ref)) { journey ->
                Assertions.assertEquals(3, journey.record.revision)
                val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED, DTOEventType.UNDELAYED)
                val events = EventUtils.assertEvents(journey.data, events = types)
                DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[2], expectedDelayMinutes = 0)
            }
        }
    }
}
