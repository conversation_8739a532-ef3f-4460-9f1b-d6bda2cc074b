package no.ruter.tranop.app.variance.deviation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.outbox.config.OutboxCleanupProperties
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.app.variance.deviation.config.ServiceDeviationOutputConfig
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.app.variance.deviation.test.DeviationTestUtils
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviation
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.test.context.TestPropertySource
import kotlin.test.assertNotNull

@TestPropertySource(
    properties = [
        "${OutboxCleanupProperties.CONF_PREFIX}.enabled=true",
        "${ServiceDeviationOutputConfig.KAFKA_AVRO_ENTITY_V1_ENABLED}=true",
        "${ServiceDeviationOutputConfig.KAFKA_DTO_V1_ENABLED}=true",
        "${ServiceDeviationOutputConfig.SNOWFLAKE_JSON_BI_V1_ENABLED}=true",
    ],
)
class DelayDeviationTest : AbstractADTv4DeviationApiTest() {
    companion object {
        private val ALT_HEADERS = httpHeaders(authOperatorIds = listOf("different-operator"))
    }

    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `deviation - delay - journey delay - basic CRUD`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        // Create delay deviation via API
        val ref = registerDelayDeviation(delay = 3, journeyId = journeyId)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(ref)
        assertDeviationDelay(ref, deviation = returned.deviation, delay = 3, revision = 1)

        // Assert deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED)
            val events = EventUtils.assertEvents(journey.data, events = types)
            DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[1], expectedDelayMinutes = 3)
        }

        // Assert deviation is _not_ readable by other operators.
        val restricted =
            getServiceDeviation(
                ref = ref,
                status = HttpStatus.NOT_FOUND,
                headers = ALT_HEADERS,
            )
        Assertions.assertNull(restricted.deviation)

        // Update deviation delay via API
        updateDelayDeviation(ref, delay = 7)

        // Assert updated deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(3, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED, DTOEventType.DELAYED)
            val events = EventUtils.assertEvents(journey.data, events = types)
            DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[2], expectedDelayMinutes = 7)
        }

        // Assert deviation can be found via "search" API
        val itemResponse = findServiceDeviations()
        Assertions.assertEquals(1, assertNotNull(itemResponse.page).itemCount)
        assertNotNull(itemResponse.items).first().let { item ->
            assertNotNull(item.lifecycle) { life ->
                Assertions.assertEquals(ref, life.serviceDeviationId)
                assertDeviationDelay(ref, deviation = item, delay = 7, revision = 2)
            }
        }

        // Assert deviation has been added to outbox
        val recordType = RecordType.SERVICE_DEVIATION
        val recordTypeDesc = recordType.desc
        recordType.outputs?.let { outputs ->
            val targeTypes = outputs.targetTypes
            val outputType = outputs.type
            Assertions.assertTrue(
                targeTypes.isNotEmpty(),
                "Target types empty [recordType=$recordTypeDesc, outputs=$outputType]",
            )
            outputs.targetTypes.forEach { targetType ->
                val deviation =
                    outboxRepository.findUnpublished(
                        dataType = outputs.type,
                        targetType = targetType,
                    )

                Assertions.assertEquals(2, deviation.size, "Failed for $recordTypeDesc, $targetType") // original + updated
            }
        } ?: throw AssertionError("No outputs defined for $recordTypeDesc")

        // Assert deviation can _not_ be found by other operators via "search" API
        val emptyResponse = findServiceDeviations(headers = ALT_HEADERS)
        Assertions.assertEquals(0, assertNotNull(emptyResponse.page).itemCount)

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(ref, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert record has been correctly updated as deleted internally.
        val deleted = assertStoredDeviationDelay(ref, revision = 3, delay = 7)
        Assertions.assertTrue(deleted.deleted)
        Assertions.assertNotNull(deleted.record.deletedAt)
        assertNotNull(deleted.record.deletedRevision) { deletedRevision ->
            Assertions.assertEquals(3, deletedRevision)
        }

        // Assert deleted record can no longer be retrieved via API, even by its rightful owner.
        getServiceDeviation(ref, status = HttpStatus.NOT_FOUND)

        // Assert deviation delay has been removed from stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(4, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.DELAYED, DTOEventType.DELAYED, DTOEventType.UNDELAYED)
            val events = EventUtils.assertEvents(journey.data, events = types)
            DeviationTestUtils.assertJourneyDelay(journey.data, delayEvent = events[3], expectedDelayMinutes = 0)
        }
    }

    @Test
    fun `deviation - delay - single call journey delay - basic CRUD`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        // Create delay deviation via API
        val nsrQuayId = "NSR:Quay:11095"
        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to nsrQuayId))

        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId, stopPointCalls = calls)
        val initialDelay = 5
        val body = createDelayRequest(delay = initialDelay, impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(body)

        // Assert deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)

            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALL_DELAY)
            val events = EventUtils.assertEvents(journey.data, events = types)

            // Assert event has expected service deviation metadata.
            val event = events.last()
            Assertions.assertEquals(DTOEventType.CALL_DELAY, event.type)

            DeviationTestUtils.assertJourneyCallDelays(journey.data, expectedDelays = mapOf(stopPointRef to initialDelay))

            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.QUAY_REFS, nsrQuayId)
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.DELAY_MINUTES, initialDelay.toString())
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF, serviceDeviationId)
        }

        // Update deviation delay via API
        val updatedDelay = 3
        updateDelayDeviation(serviceDeviationId, delay = updatedDelay)

        // Assert updated deviation delay has been applied to stored journey.
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(3, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALL_DELAY, DTOEventType.CALL_DELAY)
            val events = EventUtils.assertEvents(journey.data, events = types)

            // Assert event has expected service deviation metadata.
            val event = events.last()
            Assertions.assertEquals(DTOEventType.CALL_DELAY, event.type)

            DeviationTestUtils.assertJourneyCallDelays(journey.data, expectedDelays = mapOf(stopPointRef to updatedDelay))

            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.QUAY_REFS, nsrQuayId)
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.DELAY_MINUTES, updatedDelay.toString())
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF, serviceDeviationId)
        }

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(serviceDeviationId, comment = "delete comment with a good reason")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert deviation delay has been removed from stored journey.
        val deletedDelay = 0
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(4, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALL_DELAY, DTOEventType.CALL_DELAY, DTOEventType.CALL_DELAY_RECALLED)
            val events = EventUtils.assertEvents(journey.data, events = types)

            // Assert event has expected service deviation metadata.
            val event = events.last()
            Assertions.assertEquals(DTOEventType.CALL_DELAY_RECALLED, event.type)

            DeviationTestUtils.assertJourneyCallDelays(journey.data, expectedDelays = mapOf(stopPointRef to deletedDelay))

            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.QUAY_REFS, nsrQuayId)
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.DELAY_MINUTES, deletedDelay.toString())
            EventUtils.assertMetadataValue(event, DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF, serviceDeviationId)
        }
    }

    private fun assertDeviationDelay(
        ref: String,
        deviation: APIServiceDeviation?,
        delay: Int,
        revision: Int,
    ) {
        assertNotNull(deviation) { d ->
            assertNotNull(d.spec) { s ->
                assertNotNull(s.parameters) { p ->
                    assertNotNull(p.delayMinutes) { m ->
                        Assertions.assertEquals(delay, m)
                    }
                }
            }
        }

        assertStoredDeviationDelay(ref, revision, delay)
    }

    private fun assertStoredDeviationDelay(
        ref: String,
        revision: Int,
        delay: Int,
    ): InternalServiceDeviation {
        val stored = assertNotNull(deviationRepo.fetchByRef(ref, restricted = false))
        Assertions.assertEquals(revision, stored.record.revision)
        assertNotNull(stored.data.spec) { s ->
            assertNotNull(s.parameters) { p ->
                assertNotNull(p.delayMinutes) { m ->
                    Assertions.assertEquals(delay, m)
                }
            }
        }
        return stored
    }
}
