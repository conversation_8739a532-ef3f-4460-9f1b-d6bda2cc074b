package no.ruter.tranop.app.variance.mitigation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.mitigation.api.AbstractADTv4MitigationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

// TODO: Add test for updating partial cancellation
class CancellationMitigationTest : AbstractADTv4MitigationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]
    val vehicleTaskJourney04: PDJDatedJourney = vehicleTask[3]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `mitigation - cancellation - cancel single journey`() {
        verifySingleJourneyCancellation()
    }

    @Test
    fun `mitigation - cancellation - cancel single journey - create + update`() {
        val journey1 = vehicleTaskJourney03
        val journey2 = vehicleTaskJourney04
        storeDatedJourneys(journey2)

        val serviceMitigationId = verifySingleJourneyCancellation() // also stores journey1

        // Update deviation by changing impact from first to second journey.
        val updatedImpact = ImpactUtils.journeyImpact(journeyId = journey2.ref)
        val updatedRequest = createCancellationRequest(impact = updatedImpact, action = APIPostRequestType.UPDATE)
        updateMitigationRequest(serviceMitigationId, updatedRequest)

        assertJourneyCancellation(
            journeyRef = journey2.ref,
            serviceMitigationId = serviceMitigationId,
        )
        assertJourneyCancellationRecalled(
            journeyRef = journey1.ref,
            linked = false, // journey1 is no longer referenced by service mitigation.
            serviceMitigationId = serviceMitigationId,
        )
    }

    @Test
    fun `mitigation - cancellation - cancel single journey + delete cancellation`() {
        val ref = vehicleTaskJourney03.ref

        // TODO: Assert comment has been stored and is obtainable somewhere...
        val comment = "I changed my mind"
        val serviceMitigationId = verifySingleJourneyCancellation()

        postServiceMitigationDeleteRequest(serviceMitigationId, comment = comment)

        // Verify final revision is no longer cancelled.
        assertJourneyCancellationRecalled(
            journeyRef = ref,
            linked = true, // deleted service mitigation still has reference / link to journey.
            serviceMitigationId = serviceMitigationId,
        )
    }

    @Test
    fun `mitigation - cancellation - cancel single call in single journey`() {
        verifySingleJourneySingleCallCancellation()
    }

    @Test
    fun `mitigation - cancellation - cancel single call in single journey + delete cancellation`() {
        val ref = vehicleTaskJourney03.ref

        // TODO: Assert comment has been stored and is obtainable somewhere...
        val comment = "I changed my mind"
        val serviceMitigationId = verifySingleJourneySingleCallCancellation()

        postServiceMitigationDeleteRequest(serviceMitigationId, comment = comment)

        // Verify final revision is no longer cancelled.
        val r3 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(3, r3.record.revision)
        assertCancellationStatus(r3, cancelled = false, partiallyCancelled = false)
        EventUtils.assertEvents(r3.data, listOf(DTOEventType.IMPORTED, DTOEventType.CALLS_CANCELLED, DTOEventType.CALLS_UNCANCELLED))
    }

    private fun verifySingleJourneyCancellation(): String {
        storeDatedJourneys(vehicleTaskJourney03)
        val ref = vehicleTaskJourney03.ref

        // Verify initial revision is not cancelled.
        val r1 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(1, r1.record.revision)
        assertCancellationStatus(r1, cancelled = false, partiallyCancelled = false)

        val impact = ImpactUtils.journeyImpact(journeyId = ref)
        val body = createCancellationRequest(impact)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        val serviceMitigationId = assertNotNull(res.mitigation?.lifecycle?.serviceMitigationId)

        // Verify we now have a second revision, which is cancelled.
        assertJourneyCancellation(
            journeyRef = ref,
            serviceMitigationId = serviceMitigationId,
        )
        return serviceMitigationId
    }

    private fun verifySingleJourneySingleCallCancellation(): String {
        storeDatedJourneys(vehicleTaskJourney03)
        val ref = vehicleTaskJourney03.ref

        // Verify initial revision is not cancelled.
        val r1 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(1, r1.record.revision)
        assertCancellationStatus(r1, cancelled = false, partiallyCancelled = false)

        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to "NSR:Quay:11095"))

        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = ref, stopPointCalls = calls)
        val body = createCancellationRequest(impact)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)

        // Verify we now have a second revision, which is cancelled.
        val r2 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(2, r2.record.revision)
        assertCancellationStatus(r2, cancelled = false, partiallyCancelled = true)
        EventUtils.assertEvents(r2.data, listOf(DTOEventType.IMPORTED, DTOEventType.CALLS_CANCELLED))

        return assertNotNull(res.mitigation?.lifecycle?.serviceMitigationId)
    }

    private fun assertJourneyCancellation(
        journeyRef: String?,
        serviceMitigationId: String,
    ) {
        assertNotNull(journeyRepo.fetchByRef(journeyRef)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)
            assertCancellationStatus(journey, cancelled = true, partiallyCancelled = false)

            val events = listOf(DTOEventType.IMPORTED, DTOEventType.CANCELLED)
            EventUtils.assertEvents(journey.data, events)
        }

        // Assert impacted journey has been associated with mitigation.
        mitigationRepo.externalRefRepo.getExternalRefs(serviceMitigationId).let { externalRefs ->
            val deviationDatedJourneyRefs = externalRefs.get(ExternalRefType.DATED_JOURNEY_V2_REF)
            Assertions.assertTrue(deviationDatedJourneyRefs.contains(journeyRef))
        }
    }

    private fun assertJourneyCancellationRecalled(
        journeyRef: String,
        serviceMitigationId: String,
        linked: Boolean,
    ) {
        assertNotNull(journeyRepo.fetchByRef(journeyRef)) { journey ->
            Assertions.assertEquals(3, journey.record.revision)
            assertCancellationStatus(journey, cancelled = false, partiallyCancelled = false)

            val events = listOf(DTOEventType.IMPORTED, DTOEventType.CANCELLED, DTOEventType.UNCANCELLED)
            EventUtils.assertEvents(journey.data, events)
        }

        // Assert journey association with service mitigation (linked or not linked)
        mitigationRepo.externalRefRepo.getExternalRefs(serviceMitigationId).let { externalRefs ->
            val deviationDatedJourneyRefs = externalRefs.get(ExternalRefType.DATED_JOURNEY_V2_REF)
            Assertions.assertEquals(linked, deviationDatedJourneyRefs.contains(journeyRef))
        }
    }

    private fun assertCancellationStatus(
        record: InternalDatedJourney,
        cancelled: Boolean,
        partiallyCancelled: Boolean,
    ) {
        val journey = record.data
        val journeyState = assertNotNull(journey.journeyState)
        val journeyMitigationState = assertNotNull(journeyState.journeyMitigationState)

        Assertions.assertEquals(cancelled, record.cancelled)
        Assertions.assertEquals(cancelled, journey.cancelled)
        Assertions.assertEquals(cancelled, journeyMitigationState.cancelled)

        Assertions.assertEquals(partiallyCancelled, record.partiallyCancelled)
        Assertions.assertEquals(partiallyCancelled, journeyMitigationState.partiallyCancelled)
    }

    private fun createCancellationRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceMitigationPostRequest = createServiceMitigationRequest(DTOServiceMitigationCode.CANCELLATION, impact, action)
}
