//package no.ruter.tranop.app.variance.deviation.output
//
//import no.ruter.rdp.common.json.JsonUtils
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
//import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
//import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
//import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
//import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
//import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
//import no.ruter.tranop.journey.deviation.dto.model.common.DTOServiceDeviationReason
//import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import kotlin.test.assertEquals
//import kotlin.test.assertNotNull
//
//class ServiceDeviationSnowflakeIngestClientTest {
//    val clientFactory = SnowflakeTestClientFactory()
//
//    val streamingIngestClient =
//        ServiceDeviationSnowflakeIngestClient(
//            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
//            clientFactory = clientFactory,
//        )
//
//    @BeforeEach
//    fun setUp() {
//        clientFactory.reset()
//    }
//
//    @Test
//    fun `stream sends service deviation data to Snowflake successfully`() {
//        val serviceDeviation =
//            InternalServiceDeviation(
//                data =
//                    DTOServiceDeviation().apply {
//                        this.ref = "sd-test-123"
//                        this.spec =
//                            DTOServiceDeviationSpec().apply {
//                                this.code = DTOServiceDeviationCode.DELAY
//                                this.reason =
//                                    DTOServiceDeviationReason().apply {
//                                        this.code = "PUBLIC_EVENT"
//                                        this.comment = "17 mai trafikk"
//                                    }
//                                this.impact =
//                                    DTOServiceImpact().apply {
//                                        this.stopPoints = emptyList()
//                                        this.journeys = emptyList()
//                                        this.lines = emptyList()
//                                    }
//                            }
//                    },
//                record =
//                    DeviationRecord().apply {
//                        operatorId = "operator"
//                        authorityId = "authority"
//                    },
//            )
//
//        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()
//        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", biServiceDeviation)))
//
//        val capturedMap = firstIngestedRow()
//        assertEquals("authority", capturedMap["AUTHORITY_ID"])
//        assertEquals("operator", capturedMap["OPERATOR_ID"])
//        assertNotNull(capturedMap["QUAY_REFS"])
//        assertNotNull(capturedMap["DATED_JOURNEY_V2_REFS"])
//        assertNotNull(capturedMap["LINE_REFS"])
//
//        val jsonDataString = capturedMap["JSONDATA"].toString()
//        val jsonData = JsonUtils.toJsonNode(jsonDataString)
//        assertNotNull(jsonData)
//    }
//
//    @Test
//    fun `stream sends service deviation with null values successfully`() {
//        val serviceDeviation =
//            InternalServiceDeviation(
//                data =
//                    DTOServiceDeviation().apply {
//                        this.ref = "sd-test-456"
//                        this.spec =
//                            DTOServiceDeviationSpec().apply {
//                                this.code =
//                                    DTOServiceDeviationCode.NO_SERVICE
//                                this.impact =
//                                    DTOServiceImpact().apply {
//                                        this.stopPoints = emptyList()
//                                        this.journeys = emptyList()
//                                        this.lines = emptyList()
//                                    }
//                            }
//                    },
//                record =
//                    DeviationRecord().apply {
//                        operatorId = "ruter"
//                        authorityId = "ruter"
//                    },
//            )
//
//        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()
//        streamingIngestClient.ingest(listOf(Pair("outbox-ref-2", biServiceDeviation)))
//
//        val capturedMap = firstIngestedRow()
//        assertEquals("ruter", capturedMap["AUTHORITY_ID"])
//        assertEquals("ruter", capturedMap["OPERATOR_ID"])
//        assertNotNull(capturedMap["QUAY_REFS"])
//        assertNotNull(capturedMap["DATED_JOURNEY_V2_REFS"])
//        assertNotNull(capturedMap["LINE_REFS"])
//
//        val jsonDataString = capturedMap["JSONDATA"].toString()
//        val jsonData = JsonUtils.toJsonNode(jsonDataString)
//        assertNotNull(jsonData)
//    }
//
//    @Test
//    fun `toBIServiceDeviation converts DTOServiceDeviation correctly`() {
//        val serviceDeviation =
//            InternalServiceDeviation(
//                data =
//                    DTOServiceDeviation().apply {
//                        this.ref = "sd-conversion-test"
//                        this.spec =
//                            DTOServiceDeviationSpec().apply {
//                                this.code = DTOServiceDeviationCode.BYPASS
//                                this.reason =
//                                    DTOServiceDeviationReason().apply {
//                                        this.code = "CONSTRUCTION"
//                                        this.comment = "Road construction work"
//                                    }
//                            }
//                    },
//                record =
//                    DeviationRecord().apply {
//                        operatorId = "ruter"
//                        authorityId = "ruter"
//                    },
//            )
//
//        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()
//
//        assertEquals("sd-conversion-test", biServiceDeviation.ref)
//        assertNotNull(biServiceDeviation.spec)
//    }
//
//    private fun firstIngestedRow(): Map<String?, Any?> =
//        clientFactory
//            .channel(
//                clientName = ServiceDeviationSnowflakeIngestClient.CLIENT_BUILDER_NAME,
//                channelName = ServiceDeviationSnowflakeIngestClient.CHANNEL_BUILDER_NAME,
//            ).rows
//            .first() ?: throw AssertionError("ingested row is null")
//}
