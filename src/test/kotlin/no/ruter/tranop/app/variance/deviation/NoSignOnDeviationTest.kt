package no.ruter.tranop.app.variance.deviation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationSpec
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull

class NoSignOnDeviationTest : AbstractADTv4DeviationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]
    val vehicleTaskJourney04: PDJDatedJourney = vehicleTask[3]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `deviation - no sign-on - single journey - create & delete`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        // Create no sign-on deviation via API
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId)
        val request = createNoSignOnRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(serviceDeviationId)
        Assertions.assertEquals(APIServiceDeviationCode.NO_SIGN_ON.value, returned.deviation?.spec?.code)

        // Assert deviation delay has been applied to stored journey.
        assertNoSignOnJourney(journeyId)

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(serviceDeviationId, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert deviation delay has been applied to stored journey.
        assertNoSignOnJourneyRecalled(journeyId)
    }

    @Test
    fun `deviation - no sign-on - single journey - create & update - change journey`() {
        // Store test journeys in database.
        val journeyId1 = vehicleTaskJourney03.ref
        val journeyId2 = vehicleTaskJourney04.ref
        storeDatedJourneys(vehicleTaskJourney03, vehicleTaskJourney04)

        // Create no sign-on deviation via API
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId1)
        val request = createNoSignOnRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert no sign-on deviation has been applied to first journey.
        assertNoSignOnJourney(journeyId1)

        // Update deviation by changing impact from first to second journey.
        val updatedImpact = ImpactUtils.journeyImpact(journeyId = journeyId2)
        val updatedRequest = createNoSignOnRequest(impact = updatedImpact, action = APIPostRequestType.UPDATE)
        postServiceDeviationUpdateRequest(serviceDeviationId, updatedRequest)

        // Assert no sign-on deviation has been moved from first to second journey.
        assertNoSignOnJourney(journeyId2)
        assertNoSignOnJourneyRecalled(journeyId1)
    }

    private fun createNoSignOnRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceDeviationPostRequest {
        val spec =
            APIServiceDeviationSpec(
                code = APIServiceDeviationCode.NO_SIGN_ON.value,
                impact = impact,
            )
        return APIServiceDeviationPostRequest(
            spec = spec,
            action = action.value,
        )
    }

    private fun assertNoSignOnJourney(journeyId: String?) {
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            Assertions.assertEquals(2, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.OPERATING_WITHOUT_SIGN_ON)
            EventUtils.assertEvents(journey.data, events = types)
            Assertions.assertTrue(journey.data.journeyState.journeyDeviationState.operatingWithoutSignon)
        }
    }

    private fun assertNoSignOnJourneyRecalled(journeyId: String?) {
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            val record = journey.record
            Assertions.assertEquals(3, record.revision)

            val types =
                listOf(
                    DTOEventType.IMPORTED,
                    DTOEventType.OPERATING_WITHOUT_SIGN_ON,
                    DTOEventType.OPERATING_WITHOUT_SIGN_ON_RECALLED,
                )
            EventUtils.assertEvents(journey.data, events = types)
            Assertions.assertFalse(journey.data.journeyState.journeyDeviationState.operatingWithoutSignon)
        }
    }
}
