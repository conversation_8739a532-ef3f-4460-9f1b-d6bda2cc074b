package no.ruter.tranop.app.variance.common.test

import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import kotlin.test.assertNotNull

class EventUtils private constructor() {
    companion object {
        fun assertEvents(
            journey: DTODatedJourney?,
            events: List<DTOEventType>,
        ): List<DTOEvent> {
            val journeyEvents = assertNotNull(journey).events

            fun getInfoMessage(): String {
                val actual = journeyEvents.joinToString(separator = ", ") { it.type?.value ?: "null" }
                val expected = events.joinToString(separator = ", ")
                return "expected [$expected] but was [$actual]"
            }

            Assertions.assertEquals(events.size, journeyEvents.size) { getInfoMessage() }

            events.forEachIndexed { i, expectedType ->
                Assertions.assertEquals(expectedType, journeyEvents[i].type) { getInfoMessage() }
            }
            return journeyEvents
        }

        fun assertMetadataValue(
            event: DTOEvent?,
            key: DTOEventMetadataKeyType,
            value: String,
        ) {
            assertNotNull(assertNotNull(event).metadata) { md ->
                val meta = md.filter { it.key == key }
                Assertions.assertEquals(1, meta.size)
                Assertions.assertEquals(value, meta.first().value)
            }
        }
    }
}
