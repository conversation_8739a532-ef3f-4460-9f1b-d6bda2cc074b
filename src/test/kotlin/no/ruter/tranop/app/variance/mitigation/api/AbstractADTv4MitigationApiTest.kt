package no.ruter.tranop.app.variance.mitigation.api

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.variance.common.AbstractADTv4ApiTest
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationRepository
import no.ruter.tranop.assignment.adt.v4.api.MitigationApi
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindowOption
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceMitigationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import kotlin.reflect.KCallable
import kotlin.test.assertNotNull

abstract class AbstractADTv4MitigationApiTest : AbstractADTv4ApiTest() {
    companion object {
        const val ID_PARAM = "{serviceMitigationId}"
    }

    @Autowired
    protected lateinit var mitigationRepo: ServiceMitigationRepository

    // ${api.base-path:/actual/base/path}
    protected val apiBasePath: String by lazy {
        val path = getRequestMappingValue(MitigationApi::class.annotations)
        path.split(":").last().removeSuffix("}")
    }

    protected fun resolveURL(callable: KCallable<*>): String {
        val path = getRequestMappingValue(callable.annotations)
        return "${apiBasePath}$path"
    }

    protected fun postMitigationRequest(
        body: APIServiceMitigationPostRequest,
        status: HttpStatus = HttpStatus.CREATED,
    ): APIServiceMitigationPostResponse {
        val url = resolveURL(MitigationApi::postServiceMitigation)
        val json = post(url, body, status, headers = HEADERS_ADT_AUTH)
        return JsonUtils.toObject(json, APIServiceMitigationPostResponse::class.java)
    }

    protected fun updateMitigationRequest(
        id: String,
        body: APIServiceMitigationPostRequest,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceMitigationPostResponse {
        val url = resolveURL(MitigationApi::updateServiceMitigation).replace(ID_PARAM, id)
        val json = post(url, body, status, headers)
        return JsonUtils.toObject(json, APIServiceMitigationPostResponse::class.java)
    }

    protected fun postServiceMitigationDeleteRequest(
        ref: String,
        comment: String,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceMitigationPostResponse {
        val body =
            APIServiceMitigationPostRequest(
                action = APIPostRequestType.DELETE.value,
                comment = comment,
                spec = APIServiceMitigationSpec(code = APIServiceMitigationCode.REPLACEMENT_SERVICE.value),
            )
        return updateMitigationRequest(id = ref, body = body, status = status, headers = headers)
    }

    protected fun postServiceMitigationApproveRequest(
        ref: String,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceMitigationPostResponse {
        val body =
            APIServiceMitigationPostRequest(
                action = APIPostRequestType.APPROVE.value,
            )
        return updateMitigationRequest(id = ref, body, status = status)
    }

    protected fun getServiceMitigation(
        ref: String,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceMitigationGetResponse {
        val url = resolveURL(MitigationApi::getServiceMitigation).replace(ID_PARAM, ref)
        return getAndMap(
            url = url,
            headers = headers,
            status = status,
            responseClass = APIServiceMitigationGetResponse::class.java,
        )
    }

    protected fun findServiceMitigations(headers: Map<String, String> = HEADERS_ADT_AUTH): APIServiceMitigationListResponse {
        val url = resolveURL(MitigationApi::findServiceMitigations)
        return getAndMap(
            url = url,
            headers = headers,
            status = HttpStatus.OK,
            responseClass = APIServiceMitigationListResponse::class.java,
        )
    }

    protected fun registerReplacementServiceMitigation(journeyId: String): APIServiceMitigationPostResponse {
        val body = createReplacementServiceRequest(journeyId)
        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)

        val ref = assertNotNull(res.mitigation!!.lifecycle!!.serviceMitigationId)

        val stored = assertNotNull(mitigationRepo.fetchByRef(ref, restricted = false))
        val record = stored.record
        Assertions.assertEquals(ref, record.ref)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        Assertions.assertEquals(OP_ID_OPERATOR_1, record.operatorId)
        Assertions.assertEquals(AUTH_ID_AUTHORITY_1, record.authorityId)
        return res
    }

    protected fun createServiceMitigationRequest(
        code: DTOServiceMitigationCode,
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceMitigationPostRequest {
        val spec =
            APIServiceMitigationSpec(
                code = code.value,
                impact = impact,
            )
        return APIServiceMitigationPostRequest(
            action = action.value,
            spec = spec,
        )
    }

    protected fun createReplacementServiceRequest(journeyId: String): APIServiceMitigationPostRequest {
        val spec =
            APIServiceMitigationSpec(
                code = APIServiceMitigationCode.REPLACEMENT_SERVICE.value,
                impact =
                    APIServiceImpact(
                        journeys =
                            listOf(
                                APIJourneySpecWindowOption(
                                    journey =
                                        APIJourneySpecWindow(
                                            spec =
                                                APIJourneySpec(journeyId = journeyId),
                                        ),
                                ),
                            ),
                    ),
            )
        return APIServiceMitigationPostRequest(spec = spec)
    }
}
