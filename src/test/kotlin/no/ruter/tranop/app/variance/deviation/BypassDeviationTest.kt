package no.ruter.tranop.app.variance.deviation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import no.ruter.tranop.common.dto.model.DTOServiceDeviationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

class BypassDeviationTest : AbstractADTv4DeviationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]
    val vehicleTaskJourney05: PDJDatedJourney = vehicleTask[4]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `deviation - bypass - single journey - not allowed`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        // Create bypass deviation via API
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId)
        val request = createBypassRequest(impact = impact)
        val response = postDeviationRequest(request, status = HttpStatus.BAD_REQUEST)
        assertNotNull(response.result) { result ->
            Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, result.status!!.code)
            assertNotNull(result.details) { details ->
                Assertions.assertEquals(1, details.size)
                assertNotNull(details.firstOrNull()) { detail ->
                    Assertions.assertNull(detail.code)
                    val msg = "Bypass deviation request must specify at least one call in one journey"
                    Assertions.assertEquals(APIStatusResponseReasonCode.BAD_REQUEST.value, detail.reason)
                    Assertions.assertTrue(detail.description!!.startsWith(prefix = msg))
                }
            }
        }
    }

    @Test
    fun `deviation - bypass - single journey - single call - create & delete`() {
        // Store our test journey in database.
        val journeyId = vehicleTaskJourney03.ref
        storeDatedJourneys(vehicleTaskJourney03)

        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to "NSR:Quay:11095"))

        // Create bypass deviation via API
        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId, stopPointCalls = calls)
        val request = createBypassRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(serviceDeviationId)
        Assertions.assertEquals(APIServiceDeviationCode.BYPASS.value, returned.deviation?.spec?.code)

        assertJourneyCallBypass(journeyId, serviceDeviationId)

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(serviceDeviationId, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        assertJourneyCallBypassRecalled(journeyId, serviceDeviationId)
    }

    @Test
    fun `deviation - bypass - single journey - single call - create & update - change journey`() {
        // Store our test journey in database.
        val journeyId1 = vehicleTaskJourney03.ref
        val journeyId2 = vehicleTaskJourney05.ref
        storeDatedJourneys(vehicleTaskJourney03, vehicleTaskJourney05)

        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to "NSR:Quay:11095"))

        // Create bypass deviation via API
        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = journeyId1, stopPointCalls = calls)
        val request = createBypassRequest(impact = impact)
        val serviceDeviationId = executePostDeviationCreateRequest(request)

        assertJourneyCallBypass(journeyId1, serviceDeviationId)

        // Update bypass deviation to impact a different journey.
        val updatedImpact = ImpactUtils.journeyImpact(journeyId = journeyId2, stopPointCalls = calls)
        val updatedRequest = createBypassRequest(impact = updatedImpact, action = APIPostRequestType.UPDATE)
        postServiceDeviationUpdateRequest(serviceDeviationId, updatedRequest)

        // Verify bypass state has been moved from first to second journey.
        assertJourneyCallBypass(journeyId2, serviceDeviationId)
        assertJourneyCallBypassRecalled(journeyId1, serviceDeviationId)
    }

    private fun createBypassRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceDeviationPostRequest = createServiceDeviationRequest(APIServiceDeviationCode.BYPASS, impact, action)

    private fun assertJourneyCallBypass(
        journeyId: String?,
        serviceDeviationId: String,
    ) {
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            // Assert bypass event has been applied to stored journey.
            Assertions.assertEquals(2, journey.record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALL_BYPASS)
            val events = EventUtils.assertEvents(journey.data, events = types)

            // Assert event has service deviation id as metadata value.
            val key = DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF
            EventUtils.assertMetadataValue(events.last(), key, serviceDeviationId)

            // Asser journey state has deviation as active for journey.
            val deviation =
                assertNotNull(
                    journey.data.journeyState
                        ?.journeyDeviationState
                        ?.deviations
                        ?.firstOrNull(),
                )
            Assertions.assertEquals(serviceDeviationId, deviation.ref)
            Assertions.assertEquals(DTOServiceDeviationCode.BYPASS.value, deviation.code)
        }
    }

    private fun assertJourneyCallBypassRecalled(
        journeyId: String?,
        serviceDeviationId: String,
    ) {
        assertNotNull(journeyRepo.fetchByRef(journeyId)) { journey ->
            // Assert bypass recall event has been applied to stored journey.
            val record = journey.record
            Assertions.assertEquals(3, record.revision)
            val types = listOf(DTOEventType.IMPORTED, DTOEventType.CALL_BYPASS, DTOEventType.CALL_BYPASS_RECALLED)
            val events = EventUtils.assertEvents(journey.data, events = types)

            // Assert event has service deviation id as metadata value.
            val key = DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF
            EventUtils.assertMetadataValue(events.last(), key, serviceDeviationId)

            // Assert journey state does not have deviation as active for journey.
            val deviation =
                assertNotNull(
                    journey.data.journeyState
                        ?.journeyDeviationState
                        ?.deviations,
                )
            Assertions.assertEquals(0, deviation.size)
        }
    }
}
