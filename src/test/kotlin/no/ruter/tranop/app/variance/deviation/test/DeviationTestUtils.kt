package no.ruter.tranop.app.variance.deviation.test

import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import org.junit.jupiter.api.Assertions
import java.time.Duration

class DeviationTestUtils private constructor() {
    companion object {
        fun assertJourneyDelay(
            journey: DTODatedJourney,
            delayEvent: DTOEvent?,
            expectedDelayMinutes: Int,
        ) {
            EventUtils.assertMetadataValue(
                event = delayEvent,
                key = DTOEventMetadataKeyType.DELAY_MINUTES,
                value = expectedDelayMinutes.toString(),
            )
            journey.plan.calls.forEach { call ->
                assertJourneyCallDelay(call, expectedDelayMinutes)
            }
        }

        fun assertJourneyCallDelays(
            journey: DTODatedJourney,
            expectedDelays: Map<String, Int>,
        ) {
            journey.plan.calls.forEach { call ->
                val expectedDelay = expectedDelays[call.stopPointRef]
                if (expectedDelay != null) {
                    assertJourneyCallDelay(call, expectedDelay)
                } else {
                    assertJourneyCallDelay(call, expectedDelayMinutes = 0)
                }
            }
        }

        fun assertJourneyCallDelay(
            call: DTODatedJourneyCall,
            expectedDelayMinutes: Int,
        ) {
            val expectedArrival = call.expectedArrival?.toOffsetDateTime()
            val expectedDeparture = call.expectedDeparture?.toOffsetDateTime()
            if (expectedDelayMinutes != 0) {
                val plannedArrival = call.plannedArrival?.toOffsetDateTime()
                val arrivalDelta = Duration.between(plannedArrival, expectedArrival).toMinutes().toInt()
                Assertions.assertEquals(arrivalDelta, expectedDelayMinutes)

                val plannedDeparture = call.plannedDeparture?.toOffsetDateTime()
                val departureDelta = Duration.between(plannedDeparture, expectedDeparture).toMinutes().toInt()
                Assertions.assertEquals(departureDelta, expectedDelayMinutes)
            } else {
                Assertions.assertNull(expectedArrival)
                Assertions.assertNull(expectedDeparture)
            }
        }
    }
}
