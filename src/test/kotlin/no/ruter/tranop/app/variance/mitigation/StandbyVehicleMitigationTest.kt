package no.ruter.tranop.app.variance.mitigation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.event.journey.input.process.mitigation.StandbyVehicleJourneyEventHandler
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.variance.common.test.EventUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.mitigation.api.AbstractADTv4MitigationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

class StandbyVehicleMitigationTest : AbstractADTv4MitigationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]
    val vehicleTaskJourney04: PDJDatedJourney = vehicleTask[3]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `mitigation - stand-by vehicle - single journey - create`() {
        val ref = vehicleTaskJourney03.ref

        verifySingleJourneyStandbyVehicle()

        val journey = assertNotNull(journeyRepo.fetchByRef(ref))
        assertNotNull(journey.data.operators) { ops ->
            Assertions.assertEquals(2, ops.size)
            assertOperator(ops[0], ref = "RUT:Operator:220", name = "Sporveien Trikken AS")
            assertOperator(ops[1], ref = OP_ID_OPERATOR_1, name = StandbyVehicleJourneyEventHandler.CONTINGENCY_OPERATOR_NAME)
        }
    }

    @Test
    fun `mitigation - stand-by-vehicle - single journey - create + update`() {
        val journey1 = vehicleTaskJourney03
        val journey2 = vehicleTaskJourney04
        storeDatedJourneys(journey2)

        val serviceMitigationId = verifySingleJourneyStandbyVehicle() // also store journey 1

        assertStandByVehicle(journey1.ref)

        // Update deviation by changing impact from first to second journey.
        val updatedImpact = ImpactUtils.journeyImpact(journeyId = journey2.ref)
        val updatedRequest = createStandbyVehicleRequest(impact = updatedImpact, action = APIPostRequestType.UPDATE)
        updateMitigationRequest(serviceMitigationId, updatedRequest)

        // Verify final revision no longer has contingency vehicle planned.
        assertStandByVehicle(journey2.ref)
        assertStandbyVehicleRecalled(journey1.ref)
    }

    @Test
    fun `mitigation - stand-by-vehicle - single journey - create + delete`() {
        val ref = vehicleTaskJourney03.ref

        // TODO: Assert comment has been stored and is obtainable somewhere...
        val comment = "I changed my mind"
        val serviceMitigationId = verifySingleJourneyStandbyVehicle()

        postServiceMitigationDeleteRequest(serviceMitigationId, comment = comment)

        // Verify final revision no longer has contingency vehicle planned.
        assertStandbyVehicleRecalled(ref)
    }

    private fun assertStandByVehicle(ref: String?) {
        val r2 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(2, r2.record.revision)
        val events =
            listOf(
                DTOEventType.IMPORTED,
                DTOEventType.CONTINGENCY_VEHICLE_PLANNED,
            )
        EventUtils.assertEvents(r2.data, events)
    }

    private fun assertStandbyVehicleRecalled(ref: String?) {
        val r3 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(3, r3.record.revision)
        val events =
            listOf(
                DTOEventType.IMPORTED,
                DTOEventType.CONTINGENCY_VEHICLE_PLANNED,
                DTOEventType.CONTINGENCY_VEHICLE_RECALLED,
            )
        EventUtils.assertEvents(r3.data, events)
    }

    private fun verifySingleJourneyStandbyVehicle(): String {
        storeDatedJourneys(vehicleTaskJourney03)
        val ref = vehicleTaskJourney03.ref

        // Verify initial revision
        val r1 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(1, r1.record.revision)

        val impact = ImpactUtils.journeyImpact(journeyId = ref)
        val body = createStandbyVehicleRequest(impact)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)

        val serviceMitigationId = assertNotNull(res.mitigation?.lifecycle?.serviceMitigationId)

        // Verify we now have a second revision, with stand-by vehicle planned.
        assertStandByVehicle(ref)

        return serviceMitigationId
    }

    private fun createStandbyVehicleRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceMitigationPostRequest = createServiceMitigationRequest(DTOServiceMitigationCode.STANDBY_VEHICLE_PLANNED, impact, action)

    private fun assertOperator(
        operator: DTOOperator?,
        ref: String,
        name: String,
    ) {
        assertNotNull(operator) { op ->
            Assertions.assertEquals(ref, op.operatorRef)
            Assertions.assertEquals(name, op.name)
        }
    }
}
