//package no.ruter.tranop.app.variance.mitigation.output
//
//import no.ruter.rdp.common.json.JsonUtils
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
//import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
//import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
//import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
//import no.ruter.tranop.common.dto.model.DTOServiceMitigationCode
//import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
//import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationParameters
//import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
//import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
//import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpec
//import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindow
//import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindowOption
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import kotlin.test.assertEquals
//import kotlin.test.assertNotNull
//
//class ServiceMitigationSnowflakeIngestClientTest {
//    val clientFactory = SnowflakeTestClientFactory()
//
//    val streamingIngestClient =
//        ServiceMitigationSnowflakeIngestClient(
//            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
//            clientFactory = clientFactory,
//        )
//
//    @BeforeEach
//    fun setUp() {
//        clientFactory.reset()
//    }
//
//    @Test
//    fun `stream sends service mitigation data to Snowflake successfully`() {
//        val serviceMitigation =
//            InternalServiceMitigation(
//                data =
//                    DTOServiceMitigation().apply {
//                        this.ref = "sm-test-123"
//                        this.spec =
//                            DTOServiceMitigationSpec().apply {
//                                this.code = DTOServiceMitigationCode.REPLACEMENT_SERVICE
//                                this.impact =
//                                    DTOServiceImpact().apply {
//                                        this.stopPoints = emptyList()
//                                        this.journeys =
//                                            listOf(
//                                                DTOJourneySpecWindowOption().apply {
//                                                    this.journey =
//                                                        DTOJourneySpecWindow().apply {
//                                                            this.spec =
//                                                                DTOJourneySpec().apply {
//                                                                    this.journeyId = "djj-journey-id"
//                                                                }
//                                                        }
//                                                },
//                                            )
//                                        this.lines = emptyList()
//                                    }
//                                this.parameters =
//                                    DTOServiceMitigationParameters().apply {
//                                        this.vehicleId = "vehicleId"
//                                        this.transportMode = "BUS"
//                                    }
//                            }
//                    },
//                record =
//                    MitigationRecord().apply {
//                        operatorId = "operator"
//                        authorityId = "authority"
//                    },
//            )
//
//        val biServiceMitigation = serviceMitigation.toBIServiceMitigation()
//        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", biServiceMitigation)))
//
//        val capturedMap = firstIngestedRow()
//        assertEquals("authority", capturedMap["AUTHORITY_ID"])
//        assertEquals("operator", capturedMap["OPERATOR_ID"])
//        assertNotNull(capturedMap["QUAY_REFS"])
//        assertNotNull(capturedMap["DATED_JOURNEY_V2_REFS"])
//        assertNotNull(capturedMap["LINE_REFS"])
//
//        val jsonDataString = capturedMap["JSONDATA"].toString()
//        val jsonData = JsonUtils.toJsonNode(jsonDataString)
//        assertNotNull(jsonData)
//    }
//
//    @Test
//    fun `stream sends service mitigation with null values successfully`() {
//        val serviceMitigation =
//            InternalServiceMitigation(
//                data =
//                    DTOServiceMitigation().apply {
//                        this.ref = "sm-test-456"
//                        this.spec =
//                            DTOServiceMitigationSpec().apply {
//                                this.code = DTOServiceMitigationCode.REPLACEMENT_SERVICE
//                                this.impact =
//                                    DTOServiceImpact().apply {
//                                        this.stopPoints = emptyList()
//                                        this.journeys = emptyList()
//                                        this.lines = emptyList()
//                                    }
//                            }
//                    },
//                record =
//                    MitigationRecord().apply {
//                        operatorId = "ruter"
//                        authorityId = "ruter"
//                    },
//            )
//
//        val biServiceMitigation = serviceMitigation.toBIServiceMitigation()
//        streamingIngestClient.ingest(listOf(Pair("outbox-ref-2", biServiceMitigation)))
//
//        val capturedMap = firstIngestedRow()
//        assertEquals("ruter", capturedMap["AUTHORITY_ID"])
//        assertEquals("ruter", capturedMap["OPERATOR_ID"])
//        assertNotNull(capturedMap["QUAY_REFS"])
//        assertNotNull(capturedMap["DATED_JOURNEY_V2_REFS"])
//        assertNotNull(capturedMap["LINE_REFS"])
//
//        val jsonDataString = capturedMap["JSONDATA"].toString()
//        val jsonData = JsonUtils.toJsonNode(jsonDataString)
//        assertNotNull(jsonData)
//    }
//
//    @Test
//    fun `toBIServiceMitigation converts DTOServiceMitigation correctly`() {
//        val serviceMitigation =
//            InternalServiceMitigation(
//                data =
//                    DTOServiceMitigation().apply {
//                        this.ref = "sm-conversion-test"
//                        this.spec =
//                            DTOServiceMitigationSpec().apply {
//                                this.code = DTOServiceMitigationCode.CANCELLATION
//                            }
//                    },
//                record =
//                    MitigationRecord().apply {
//                        operatorId = "ruter"
//                        authorityId = "ruter"
//                    },
//            )
//
//        val biServiceMitigation = serviceMitigation.toBIServiceMitigation()
//
//        assertEquals("sm-conversion-test", biServiceMitigation.ref)
//        assertNotNull(biServiceMitigation.spec)
//    }
//
//    private fun firstIngestedRow(): Map<String?, Any?> =
//        clientFactory
//            .channel(
//                clientName = ServiceMitigationSnowflakeIngestClient.CLIENT_BUILDER_NAME,
//                channelName = ServiceMitigationSnowflakeIngestClient.CHANNEL_BUILDER_NAME,
//            ).rows
//            .first() ?: throw AssertionError("ingested row is null")
//}
